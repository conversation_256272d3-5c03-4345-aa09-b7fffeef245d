{"name": "drumkit", "version": "0.48.23", "description": "Copilot for Logistics", "repository": {"type": "git", "url": "https://github.com/drumkitai/vulcan.git"}, "scripts": {"build": "vite build", "build:staging": "cross-env __STAGING__=true vite build --mode staging", "build:watch": "cross-env __DEV__=true vite build -w --mode dev", "build:staging:watch": "cross-env __STAGING__=true vite build -w --mode staging", "build:hmr": "rollup --config scripts/reload/rollup.config.ts --configPlugin typescript", "wss": "node scripts/reload/initReloadServer.js", "dev": "run-p wss build:watch", "staging": "run-p wss build:staging:watch", "test": "jest", "prettier": "prettier --write '**/*.{js,jsx,ts,tsx,json,css,scss,md}'", "lint": "eslint --max-warnings=0 '**/*.{ts,tsx,js,jsx}'", "release": "node scripts/release.js", "changeset": "changeset"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "type": "module", "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@hookform/error-message": "^2.0.1", "@inboxsdk/core": "2.2.7", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@sentry/browser": "10.3.0", "@sentry/integrations": "7.114.0", "@sentry/types": "^10.3.0", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/vite": "^4.1.11", "@types/lodash": "^4.17.20", "@types/mustache": "^4.2.6", "animejs": "^4.1.2", "antd": "^5.26.7", "axios": "^1.12.0", "axios-retry": "^4.5.0", "cal-sans": "^1.0.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "construct-style-sheets-polyfill": "^3.1.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "dompurify": "^3.2.6", "flat": "^6.0.1", "framer-motion": "^12.23.12", "fuse.js": "^7.1.0", "jodit": "^4.6.2", "jodit-react": "^5.2.19", "lodash": "^4.17.21", "lucide-react": "^0.539.0", "mustache": "^4.2.0", "neverthrow": "^8.2.0", "posthog-js": "^1.259.0", "quill": "^2.0.3", "react": "^19.1.1", "react-content-loader": "^7.1.1", "react-day-picker": "^9.8.1", "react-dom": "^19.1.1", "react-dropzone": "^14.3.8", "react-feather": "^2.0.10", "react-hook-form": "^7.62.0", "react-quill": "^2.0.0", "react-router-dom": "^7.8.0", "recharts": "^3.1.2", "styled-components": "^6.1.19", "swr": "^2.3.4", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "typescript-eslint": "^8.39.0", "uuid": "^11.1.0"}, "devDependencies": {"@changesets/changelog-github": "^0.5.1", "@changesets/cli": "^2.29.5", "@eslint/compat": "^1.3.2", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-toast": "^1.2.14", "@rollup/plugin-typescript": "^12.1.4", "@sentry/vite-plugin": "^4.0.2", "@testing-library/dom": "^10.4.1", "@testing-library/react": "^16.3.0", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/animejs": "^3.1.13", "@types/chrome": "^0.1.3", "@types/jest": "^30.0.0", "@types/node": "^24.2.1", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@types/uuid": "^10.0.0", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "8.39.0", "@typescript-eslint/parser": "^8.39.0", "@vitejs/plugin-react": "^5.0.0", "autoprefixer": "^10.4.21", "chokidar": "^4.0.3", "cross-env": "^10.0.0", "eslint": "^9.33.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "5.5.4", "eslint-plugin-react": "^7.37.5", "fs-extra": "11.3.1", "jest": "30.0.5", "jest-chrome": "^0.8.0", "jest-environment-jsdom": "^29.7.0", "npm-run-all": "^4.1.5", "postcss": "^8.5.6", "postcss-nested": "^7.0.1", "prettier": "3.6.2", "rollup": "4.46.2", "sass": "1.90.0", "tailwindcss": "^4.1.11", "ts-jest": "29.4.1", "ts-loader": "9.5.2", "tslib": "^2.8.1", "typescript": "5.9.2", "vite": "^7.1.5", "ws": "8.18.3"}, "packageManager": "yarn@3.6.3"}