import packageJson from './package.json';

/**
 * After changing, please reload the extension at `chrome://extensions`
 */
const manifest: chrome.runtime.ManifestV3 = {
  manifest_version: 3,
  name: 'Drumkit',
  version: packageJson.version,
  description: packageJson.description,
  permissions: [
    'cookies',
    'scripting',
    'activeTab',
    // Normally you specify static 'sidepanel' path (https://developer.chrome.com/docs/extensions/reference/api/sidePanel),
    // but we use background scripts to conditionally render Drumkit sidebar via HTML injection in Relay TMS and delegated Gmail
    // (due to legacy code & host-specific caveats),
    // and as a sidepanel on all other websites.
    'sidePanel',
    'contextMenus',
  ],
  host_permissions: [
    // Drumkit browser extension can *run* on any site but is not necessarily *integrated* with every site
    // (e.g. parsing webpage & autofilling Drumkit forms)
    '<all_urls>',
  ],
  options_page: 'src/pages/options/index.html',
  action: {
    default_title: 'Drumkit',
  },
  background: {
    service_worker: 'src/pages/background/index.js',
    type: 'module',
  },
  icons: {
    '128': 'icon-128.png',
  },
  content_scripts: [
    {
      matches: [
        'https://*.aljex.descartes.com/*',
        'https://*.taicloud.net/*',
        'https://*.turvo.com/*',
        'https://mail.google.com/*',
        'https://*.aljex.com/*',
        'https://*.relaytms.com/*',
        'https://training.relaytms.com/*',
      ],
      js: ['src/pages/content/index.js'],
      // KEY for cache invalidation
      // TODO: these can be dynamically imported
      // css: ['assets/css/Index.chunk.css', 'assets/css/App.chunk.css'],
      css: ['assets/css/App.chunk.css'],
    },
  ],
  web_accessible_resources: [
    {
      resources: [
        'assets/js/*.js',
        'assets/css/*.css',
        'assets/gif/*.gif',
        'assets/png/*.png',
        'icon-128.png',
        'icon-34.png',
      ],
      matches: ['*://*/*'],
    },
  ],
  content_security_policy: {
    extension_pages:
      "script-src 'self'; object-src 'self'; script-src-elem 'self' 'unsafe-inline' http://localhost:* http://127.0.0.1:*;",
  },
};

export default manifest;
