# drumkit

## 0.48.23

### Patch Changes

- [#1179](https://github.com/drumkitai/vulcan/pull/1179) [`c1c51fd`](https://github.com/drumkitai/vulcan/commit/c1c51fde94f7416112978cd292c272c5cc40556a) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Prevent FE panics in Outlook - Lucas

- [#1176](https://github.com/drumkitai/vulcan/pull/1176) [`86ff100`](https://github.com/drumkitai/vulcan/commit/86ff100b0dd1c4858486fa6d80c65d95cb4cd8d0) Thanks [@Dewey3K](https://github.com/Dewey3K)! - fix turvo load id parsing for app.turvo -- aaron

- [#1174](https://github.com/drumkitai/vulcan/pull/1174) [`e25db2e`](https://github.com/drumkitai/vulcan/commit/e25db2e4a2cd17890b61b528e8b8c4ce00478ac1) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Don't fetch operators for Turvo if not enabled -- Sophie

- [#1180](https://github.com/drumkitai/vulcan/pull/1180) [`7e630b2`](https://github.com/drumkitai/vulcan/commit/7e630b25634a2ca2c6c8b0fbff0f585aec3b8814) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Fix Opendock Select Input in Relay -Winston

- [#1178](https://github.com/drumkitai/vulcan/pull/1178) [`4f1d84a`](https://github.com/drumkitai/vulcan/commit/4f1d84a1971456bec230fd1ddd6d642d22a2afe5) Thanks [@Ronak-DianApps](https://github.com/Ronak-DianApps)! - Shipwell Quoting/Bidding Portal Scraping -Ronak

## 0.48.22

### Patch Changes

- [#1173](https://github.com/drumkitai/vulcan/pull/1173) [`f393da3`](https://github.com/drumkitai/vulcan/commit/f393da310819de846172fd23665784c595d63b07) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Fix 3G load attributes, map appt fields correctly and refactor Load Info form - Lucas

- [#1168](https://github.com/drumkitai/vulcan/pull/1168) [`c392575`](https://github.com/drumkitai/vulcan/commit/c392575a0db6565a7f3e6eef4552ea859b7fbab0) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Update DAT RateView tooltip after getting updated rate. -- boogs

- [#1171](https://github.com/drumkitai/vulcan/pull/1171) [`7cc40ee`](https://github.com/drumkitai/vulcan/commit/7cc40eedc47e5b55f0356feb2731c66ed6270ca5) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Yardview properly omit slots when a slot is full -Winston

## 0.48.21

### Patch Changes

- [#1167](https://github.com/drumkitai/vulcan/pull/1167) [`ac02b59`](https://github.com/drumkitai/vulcan/commit/ac02b591b7f603be29436905abbd93c0da0d6d5c) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Fix timepicker not disabling according to load attributes - Lucas

- [#1166](https://github.com/drumkitai/vulcan/pull/1166) [`5635b53`](https://github.com/drumkitai/vulcan/commit/5635b53178f24796b78d9feb19a8121a86ab9f41) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Refactor Aljex load building form to support AI improvements -- Sophie & Varun

- [#1165](https://github.com/drumkitai/vulcan/pull/1165) [`2625d07`](https://github.com/drumkitai/vulcan/commit/2625d075470475badcd9430c7567c9156a529738) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Yardview get open slots for next week -Winston

- [#1162](https://github.com/drumkitai/vulcan/pull/1162) [`3f980ad`](https://github.com/drumkitai/vulcan/commit/3f980adef512547b8a4d1660a20ce6cb89e4a895) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Added support for multi-stop loads in the Load View, allowing users to see all pickup and delivery stops in a load rather than just the first pickup and last delivery. -- lucas

## 0.48.20

### Patch Changes

- [#1159](https://github.com/drumkitai/vulcan/pull/1159) [`a9613fa`](https://github.com/drumkitai/vulcan/commit/a9613fa11dfe8f352054c2c124bae95b3367e91e) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Update DAT sliders to avoid disabling previous lookups. -- boogs

- [#1160](https://github.com/drumkitai/vulcan/pull/1160) [`2428126`](https://github.com/drumkitai/vulcan/commit/242812679939254d99d23c5eb05d6db1d78009f5) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Add time labels on x-axis in TMS lane history charts. -- boogs

- [#1161](https://github.com/drumkitai/vulcan/pull/1161) [`792e520`](https://github.com/drumkitai/vulcan/commit/792e52053c22af39df39e1fb5e0b025d7d4a5373) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Yardview Scheduling with Warehouse Timezone -Winston

- [#1157](https://github.com/drumkitai/vulcan/pull/1157) [`e81d8f5`](https://github.com/drumkitai/vulcan/commit/e81d8f5814ff630a3e096e0cec14f62adb6f22b9) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Costco Logo and Form UI Improvements -Winston

## 0.48.19

### Patch Changes

- [#1155](https://github.com/drumkitai/vulcan/pull/1155) [`7a8d3c6`](https://github.com/drumkitai/vulcan/commit/7a8d3c63ab3e1bba101af705274be34836e153ca) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Costco Scheduling Integration -Parikshit, Ronak, Winston

## 0.48.18

### Patch Changes

- [#1151](https://github.com/drumkitai/vulcan/pull/1151) [`af15530`](https://github.com/drumkitai/vulcan/commit/af15530ea6d285542f48f76157f2bc870151f0e3) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Allow SDS to Remove Carrier Locations Themselves (yay) -Winston

- [#1154](https://github.com/drumkitai/vulcan/pull/1154) [`0f9b35d`](https://github.com/drumkitai/vulcan/commit/0f9b35dfdf17268f164ae1c1f2c31a0ac02078f9) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Enhance DAT Lane History button and label chart x-axis - Lucas

- [#1153](https://github.com/drumkitai/vulcan/pull/1153) [`98f2283`](https://github.com/drumkitai/vulcan/commit/98f22833af7348b96fe69d2df637a54d93a572d4) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Add a city-state pair tooltip when hovering over each leg in a quote. -- boogs

## 0.48.17

### Patch Changes

- [#1148](https://github.com/drumkitai/vulcan/pull/1148) [`76706fc`](https://github.com/drumkitai/vulcan/commit/76706fc3a4f78000bd1e555ad11c220548098959) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Fix batch quote styling and other vulcan style fixes -Winston

## 0.48.16

### Patch Changes

- [#1146](https://github.com/drumkitai/vulcan/pull/1146) [`d0c60d6`](https://github.com/drumkitai/vulcan/commit/d0c60d6aaa3ad02b5e43311a19580564fc3d6585) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - OneNetwork warehouse update and remove default global font size -Winston

- [#1142](https://github.com/drumkitai/vulcan/pull/1142) [`148dc47`](https://github.com/drumkitai/vulcan/commit/148dc470100f3776ce93c789aae898e46c13e905) Thanks [@Winston-Hsiao](https://github.com/Winston-Hsiao)! - Wrap label text instead of overflow and allow AI hint to be rendered inline -Winston

- [#1144](https://github.com/drumkitai/vulcan/pull/1144) [`45bd2db`](https://github.com/drumkitai/vulcan/commit/45bd2db146df5d18ebd6d9d6b83e50d7231472d8) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Include zip code in carrier email template if it's a zip code lookup (don't include otherwise). -- boogs

- [#1138](https://github.com/drumkitai/vulcan/pull/1138) [`3b704f4`](https://github.com/drumkitai/vulcan/commit/3b704f4435e30e4c7db4a4f3d4099205117bc94d) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Add Opendock File Upload -Varun & Winston

## 0.48.15

### Patch Changes

- [#1133](https://github.com/drumkitai/vulcan/pull/1133) [`0e6b6c9`](https://github.com/drumkitai/vulcan/commit/0e6b6c9552b7a5112400735f2a799c0a5bf9a94f) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - add ability to clear appointment type for turvo -Aaron

## 0.48.14

### Patch Changes

- [#1135](https://github.com/drumkitai/vulcan/pull/1135) [`acf71ca`](https://github.com/drumkitai/vulcan/commit/acf71cabdcd08c91e4cd33007d8efbfe96257601) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Yardview Scheduling Updates For Launch with NFI -Winston

- [#1132](https://github.com/drumkitai/vulcan/pull/1132) [`1faf9e2`](https://github.com/drumkitai/vulcan/commit/1faf9e2f17e9061d6a6e2a87429979cdaf63a9df) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Add loading state to refresh button in RHFDebounceSelect -Varun

## 0.48.13

### Patch Changes

- [#1131](https://github.com/drumkitai/vulcan/pull/1131) [`3692668`](https://github.com/drumkitai/vulcan/commit/3692668bc28cb86ba85424f8b66d6ee6351f238c) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Add DAT Lane History + Allow for custom RateView parameters - Lucas

- [#1125](https://github.com/drumkitai/vulcan/pull/1125) [`6610c57`](https://github.com/drumkitai/vulcan/commit/6610c57104086a09da1200a41b1726071e0351af) Thanks [@lsouza4](https://github.com/lsouza4)! - Changed brand color from orange to pink-salmon in dark mode - Lucas

- [#1123](https://github.com/drumkitai/vulcan/pull/1123) [`c3ebbec`](https://github.com/drumkitai/vulcan/commit/c3ebbec8a13c9329fdc7b2d616c69df8c1cab6e9) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Display DAT Load to Truck ratio - Lucas

- [#1128](https://github.com/drumkitai/vulcan/pull/1128) [`389ae7b`](https://github.com/drumkitai/vulcan/commit/389ae7b3555d799c304ee290bd478a915365a40d) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Auto-update Alexandria from eng-3855-add-equipment-details-to-the-equipment-needed-section-and

- [#1129](https://github.com/drumkitai/vulcan/pull/1129) [`91e3c32`](https://github.com/drumkitai/vulcan/commit/91e3c3233671f28850a69be922a4c651e1322067) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Fix unreadable text for attachments in dark mode. -- boogs

- [#1130](https://github.com/drumkitai/vulcan/pull/1130) [`1049232`](https://github.com/drumkitai/vulcan/commit/104923216d2807a0c977c7821c5cc33da58bba84) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Update copy to explain new longest leg math for DAT. -- boogs

- [#1126](https://github.com/drumkitai/vulcan/pull/1126) [`8cd901b`](https://github.com/drumkitai/vulcan/commit/8cd901b5b96d9835adcdd1a816420c559cdffe93) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Auto-update Alexandria from eng-4167-pickupconsignee-location-search-share-data-and-results

## 0.48.12

### Patch Changes

- [#1122](https://github.com/drumkitai/vulcan/pull/1122) [`6ffd30f`](https://github.com/drumkitai/vulcan/commit/6ffd30f8a9743c873edc7d22da4ba6b5a5ecaf22) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Auto-update Alexandria from eng-3850-the-search-by-field-in-pickup-and-dropoff-should-combine

- [#1118](https://github.com/drumkitai/vulcan/pull/1118) [`1b764d3`](https://github.com/drumkitai/vulcan/commit/1b764d39acaa53434a051d30ebd5297978caa32a) Thanks [@lsouza4](https://github.com/lsouza4)! - Add FE cursor rules - Lucas

- [#1121](https://github.com/drumkitai/vulcan/pull/1121) [`bf0bdc3`](https://github.com/drumkitai/vulcan/commit/bf0bdc39292c6295c0b5d412d7790fe4b5017943) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Fix stuck Auth in Front - Lucas

## 0.48.11

### Patch Changes

- [#1116](https://github.com/drumkitai/vulcan/pull/1116) [`b4f8e0c`](https://github.com/drumkitai/vulcan/commit/b4f8e0c3869069bb7a08e1a49ec9b81a517d830e) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Auto-update Alexandria from eng-3857-save-pickup-and-dropoff-in-addition-to-po

## 0.48.10

### Patch Changes

- [#1114](https://github.com/drumkitai/vulcan/pull/1114) [`78f5e06`](https://github.com/drumkitai/vulcan/commit/78f5e06db3138a7500f7fe8eb0b595ef0944087e) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Auto-update Alexandria from eng-4036-when-you-dont-have-a-fuel-surcharge-but-line-haul-rate

## 0.48.9

### Patch Changes

- [#1112](https://github.com/drumkitai/vulcan/pull/1112) [`793f774`](https://github.com/drumkitai/vulcan/commit/793f774ab71bd5c13641765891a814227bfa420a) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Dock selection isn't showing up when Load Type is automatically selected - Lucas

## 0.48.8

### Patch Changes

- [#1111](https://github.com/drumkitai/vulcan/pull/1111) [`426db0e`](https://github.com/drumkitai/vulcan/commit/426db0e9710030ec21aa228cbdef2280ca84de02) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Increase size limit for file upload in Carrier Quoting for SDS - Sophie

- [#1109](https://github.com/drumkitai/vulcan/pull/1109) [`f035e5d`](https://github.com/drumkitai/vulcan/commit/f035e5d33e1c298a97c2e6b8811ccb7851bef5f9) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Improve error boundaries in Drumkit + Fix service features racing condition - Lucas

## 0.48.7

### Patch Changes

- [#1105](https://github.com/drumkitai/vulcan/pull/1105) [`e9602ad`](https://github.com/drumkitai/vulcan/commit/e9602ad3c9666dff93effd3700aec56c7894d6bd) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - OneNetwork Form PO Number Dropdown Input - Parikshit

- [#1107](https://github.com/drumkitai/vulcan/pull/1107) [`e757581`](https://github.com/drumkitai/vulcan/commit/e7575817234beaa5c41ff0427234e90e7a0e6699) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Add ref num and notes to Opendock Form + other improvements - Winston

## 0.48.6

### Patch Changes

- [#1103](https://github.com/drumkitai/vulcan/pull/1103) [`e44f1c7`](https://github.com/drumkitai/vulcan/commit/e44f1c768ce97cb36ad25c25c86c6fd1aae378ee) Thanks [@lsouza4](https://github.com/lsouza4)! - Suppress Vulcan background script errors - Lucas

- [#1098](https://github.com/drumkitai/vulcan/pull/1098) [`bbca737`](https://github.com/drumkitai/vulcan/commit/bbca737977eaa560978e7d95107859f0c0fa2935) Thanks [@Dewey3K](https://github.com/Dewey3K)! - added load scraping for tai - aaron

- [#1104](https://github.com/drumkitai/vulcan/pull/1104) [`2f591ce`](https://github.com/drumkitai/vulcan/commit/2f591ce894418b12c9e7c92c563dfcfb8b2750ee) Thanks [@lsouza4](https://github.com/lsouza4)! - Fix signin/signup broken layouts - Lucas

- [#1090](https://github.com/drumkitai/vulcan/pull/1090) [`820d0b5`](https://github.com/drumkitai/vulcan/commit/820d0b5770f46108e200cba35708aabadec38302) Thanks [@lsouza4](https://github.com/lsouza4)! - Disable sourcemaps for dev builds - Lucas

- [#1100](https://github.com/drumkitai/vulcan/pull/1100) [`8620b3f`](https://github.com/drumkitai/vulcan/commit/8620b3f44102926bcee52e0ce12d469dc3de179d) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Manhattan Scheduling Form - Winston / Parikshit

- [#1102](https://github.com/drumkitai/vulcan/pull/1102) [`7870d9b`](https://github.com/drumkitai/vulcan/commit/7870d9b05ee3d8c2e0b6bbc53b442b1a0ad9ca10) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Permit users to type in the fsc number input again limiting to two decimal points. The previous implementation didn't work as expected since users couldn't type in the number. -- boogs

- [#1095](https://github.com/drumkitai/vulcan/pull/1095) [`8639c1a`](https://github.com/drumkitai/vulcan/commit/8639c1ae4cc6ee6098dea679bc18b00b3b5bfeb7) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Fix null warehouse search result bug and polish scheduling form UI - Winston

- [#1098](https://github.com/drumkitai/vulcan/pull/1098) [`bbca737`](https://github.com/drumkitai/vulcan/commit/bbca737977eaa560978e7d95107859f0c0fa2935) Thanks [@Dewey3K](https://github.com/Dewey3K)! - enable global sidepanel refresh for new aljex while keeping desired behavior for other TMSs - aaron

- [#1101](https://github.com/drumkitai/vulcan/pull/1101) [`4edcfe7`](https://github.com/drumkitai/vulcan/commit/4edcfe7c8f465cdc71b46d3e713b418cb141b165) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Pass integration id to confirmApptslot (create appt) - Ronak / Winston

## 0.48.5

### Patch Changes

- [#1091](https://github.com/drumkitai/vulcan/pull/1091) [`13b08c2`](https://github.com/drumkitai/vulcan/commit/13b08c2f1004b91b618d2cdbff1ba3fbc84163a3) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Fix Advanced Search, Starred Load and load persistance issue - Lucas

## 0.48.4

### Patch Changes

- [#1087](https://github.com/drumkitai/vulcan/pull/1087) [`ea1b107`](https://github.com/drumkitai/vulcan/commit/ea1b107c549f81fd26e50b8ae521fb42da3ed58e) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Fix CQ UI issues - Lucas

- [#1086](https://github.com/drumkitai/vulcan/pull/1086) [`51f4c27`](https://github.com/drumkitai/vulcan/commit/51f4c27e3f3a253e8457bb32aa407fc73b74f5e2) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Update Greenscreens to Triumph in QQ - Lucas

- [#1085](https://github.com/drumkitai/vulcan/pull/1085) [`799974f`](https://github.com/drumkitai/vulcan/commit/799974f81e7999cefa5ad19fb57725007498c7be) Thanks [@Dewey3K](https://github.com/Dewey3K)! - Added load parsing for new aljex, and fixed Turvo display load id - aaron

- [#1084](https://github.com/drumkitai/vulcan/pull/1084) [`cd726e8`](https://github.com/drumkitai/vulcan/commit/cd726e8e830459eb2266f7e3be0b3c99abc60246) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Fix issue where Outlook sidebar pinning didn't refresh loads associated with email - Lucas

- [#1088](https://github.com/drumkitai/vulcan/pull/1088) [`d407555`](https://github.com/drumkitai/vulcan/commit/d407555729da6e1e3d8206f3fd8ee8b5d15efbb1) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Default to White Glove quoting instead of Quick Quote for SDS - Winston

- [#1082](https://github.com/drumkitai/vulcan/pull/1082) [`bffb04c`](https://github.com/drumkitai/vulcan/commit/bffb04c33ef42056da606041c0ea0f0de3f91585) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Truncate DAT Fuel price input to 2 decimal places - Winston

## 0.48.3

### Patch Changes

- [#1080](https://github.com/drumkitai/vulcan/pull/1080) [`39e7390`](https://github.com/drumkitai/vulcan/commit/39e739028eced969de82da473629b9a81273177f) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Tailwind refactor post-release fixes - Lucas

- [#1078](https://github.com/drumkitai/vulcan/pull/1078) [`8150ec2`](https://github.com/drumkitai/vulcan/commit/8150ec2718f8e6cea36b79047c923ab57684ff1d) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Added turvo load auto population in drumkit, with sidebaropener button - aaron

- [#1079](https://github.com/drumkitai/vulcan/pull/1079) [`4cd30d9`](https://github.com/drumkitai/vulcan/commit/4cd30d9b28ff16ec6d82cc451d0a38c513c65006) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Trident Olympus Lane Rate for Batch Quote and Multi-Stop QQ - Winston

- [#1072](https://github.com/drumkitai/vulcan/pull/1072) [`d82b545`](https://github.com/drumkitai/vulcan/commit/d82b5451072a7b4f80bab33d365070523d61fd39) Thanks [@Dewey3K](https://github.com/Dewey3K)! - Added turvo load auto population in drumkit, with sidebaropener button - aaron

- [#1081](https://github.com/drumkitai/vulcan/pull/1081) [`d2043fd`](https://github.com/drumkitai/vulcan/commit/d2043fd6d7b43d254027444286c126abb8015c6e) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Polish Carrier Quote UI Post Tailwind Refactor - Winston

- [#1077](https://github.com/drumkitai/vulcan/pull/1077) [`3a5dc1d`](https://github.com/drumkitai/vulcan/commit/3a5dc1d516350619bdbd2194fbbed411fa932ddf) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Remove extra space on searchbar border - Lucas

- [#1074](https://github.com/drumkitai/vulcan/pull/1074) [`61193ff`](https://github.com/drumkitai/vulcan/commit/61193ff02e6d824f17d0b57ae064f00f0e53ff74) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Update Relay load and show confirmation URL after creating a YardView scheduling appointment. -- ronak and boogs

## 0.48.2

### Patch Changes

- [#1067](https://github.com/drumkitai/vulcan/pull/1067) [`d0dc403`](https://github.com/drumkitai/vulcan/commit/d0dc403393a1e74ed1815694d2ec44cde71200c8) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - QQ + Batch Quote + General Polish Post Tailwind Refactor - Winston

- [#1064](https://github.com/drumkitai/vulcan/pull/1064) [`a2a6a6f`](https://github.com/drumkitai/vulcan/commit/a2a6a6fb7d5dd6369a4e6869236f0cb410498562) Thanks [@lsouza4](https://github.com/lsouza4)! - Fix remaining Vulcan build issues Tailwind v4 and postcss - Lucas

- [#1069](https://github.com/drumkitai/vulcan/pull/1069) [`2df4099`](https://github.com/drumkitai/vulcan/commit/2df4099de4bdddb568df316196cb231921dce3ce) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Fix the inconsistent tab styling in the appointment scheduling section from the Tailwind V4 migration. -- boogs

- [#1034](https://github.com/drumkitai/vulcan/pull/1034) [`f48bae0`](https://github.com/drumkitai/vulcan/commit/f48bae05d410d25859c0274cd697333126ee7527) Thanks [@dependabot](https://github.com/apps/dependabot)! - Including 79 dependency updates - Lucas

- [#1038](https://github.com/drumkitai/vulcan/pull/1038) [`bb4a7f5`](https://github.com/drumkitai/vulcan/commit/bb4a7f532620610cf1538363c81db5fecf019389) Thanks [@lsouza4](https://github.com/lsouza4)! - Color pallete refactor, utility components + Tailwind improvements - Lucas

- [#1071](https://github.com/drumkitai/vulcan/pull/1071) [`be57a6a`](https://github.com/drumkitai/vulcan/commit/be57a6a934f7e996e0f646403816581fb87c995e) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Fix reported issues after Tailwind v4 migration - Lucas

- [#1070](https://github.com/drumkitai/vulcan/pull/1070) [`74a6bd3`](https://github.com/drumkitai/vulcan/commit/74a6bd36d74525d81a1ea1617acb04a4db1d1411) Thanks [@lsouza4](https://github.com/lsouza4)! - Wrap Relay-injected sidebar on Shadow DOM context - Lucas

- [#1065](https://github.com/drumkitai/vulcan/pull/1065) [`b024eb1`](https://github.com/drumkitai/vulcan/commit/b024eb13df73a7b812687a11932b05d171630a5c) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Adding compatibility to removed/renamed Tailwind classes on v4 - Lucas

- [#1066](https://github.com/drumkitai/vulcan/pull/1066) [`30cb674`](https://github.com/drumkitai/vulcan/commit/30cb6745e807600e50204452623a7e06d05d2e2d) Thanks [@lsouza4](https://github.com/lsouza4)! - Tailwind v4 migration: Migrate theme from config to CSS - Lucas

## 0.48.1

### Patch Changes

- [#1059](https://github.com/drumkitai/vulcan/pull/1059) [`4c16110`](https://github.com/drumkitai/vulcan/commit/4c161107a69690b225616bd4f8b27d8be7c25fd0) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Fix quote card tooltip UI on Gmail - Lucas

- [#475](https://github.com/drumkitai/vulcan/pull/475) [`4a0ebdb`](https://github.com/drumkitai/vulcan/commit/4a0ebdb79d901744ca007bce19cc2cdaecb315db) Thanks [@Sophie1142](https://github.com/Sophie1142)! - Added a Drumkit opener button - aaron

- [#1057](https://github.com/drumkitai/vulcan/pull/1057) [`018af6b`](https://github.com/drumkitai/vulcan/commit/018af6b24a8b82360c5b1e92807bc13bf6c4af08) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Add better validation and resolve discrepancies for getting open slots and creating appointments in OneNetwork appointment scheduling flow.

## 0.48.0

### Minor Changes

- [#1054](https://github.com/drumkitai/vulcan/pull/1054) [`58d3b22`](https://github.com/drumkitai/vulcan/commit/58d3b22847085ff147f160a0f336a0546f508f3f) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Add multi-stop quick quoting feature - Sophie + Boogs + Winston

### Patch Changes

- [#1052](https://github.com/drumkitai/vulcan/pull/1052) [`bdc91ca`](https://github.com/drumkitai/vulcan/commit/bdc91ca256c94f0f25c67517f1e5e83844558285) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Add order type input for Trident Mcleod Load Building - Winston

- [#1050](https://github.com/drumkitai/vulcan/pull/1050) [`4c84b1d`](https://github.com/drumkitai/vulcan/commit/4c84b1dfa17194785b2595311f2d298c21622b09) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Fix React queue errors by following proper hook init best practices - Winston

- [#1055](https://github.com/drumkitai/vulcan/pull/1055) [`61212fe`](https://github.com/drumkitai/vulcan/commit/61212fe72c06e69450e5fd833e304326dee7be0a) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Add table of loads for TMS Lane History - Lucas

## 0.47.3

### Patch Changes

- [#1048](https://github.com/drumkitai/vulcan/pull/1048) [`f19e4f2`](https://github.com/drumkitai/vulcan/commit/f19e4f28f1e8fec27839339f3e88cfef587f72ac) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Add conditional SCAC input field in E2open form for third-party flow. -- Parikshit and Boogs

## 0.47.2

### Patch Changes

- [#1047](https://github.com/drumkitai/vulcan/pull/1047) [`7471b57`](https://github.com/drumkitai/vulcan/commit/7471b575454d1fc6aa27cd5fa4c9121ec6a5e24f) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Update the UX flow and AI-filling in YardView form. -- boogs

- [#1046](https://github.com/drumkitai/vulcan/pull/1046) [`d4e946c`](https://github.com/drumkitai/vulcan/commit/d4e946c1bc908fbb71412e6282f1508cbab30c31) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Display appointment conflict error messages in YardView appointment submissions. -- boogs

- [#1045](https://github.com/drumkitai/vulcan/pull/1045) [`712e28d`](https://github.com/drumkitai/vulcan/commit/712e28d73330d57ac8296ae1273678b0d154e71e) Thanks [@Dewey3K](https://github.com/Dewey3K)! - Make aljex load detection global (For MLM) - aaron

- [#1043](https://github.com/drumkitai/vulcan/pull/1043) [`155dd4b`](https://github.com/drumkitai/vulcan/commit/155dd4bc209ac96c82c54a5439ee55914fcc4ab8) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Fix appt scheduling issues with warehouses on wrong forms - Lucas

## 0.47.1

### Patch Changes

- [#1036](https://github.com/drumkitai/vulcan/pull/1036) [`4884ec7`](https://github.com/drumkitai/vulcan/commit/4884ec77665bc9c564b94cf07b89bb307bfd9918) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Update AI suggested comment on YardView form to include customer name. -- boogs

- [#1031](https://github.com/drumkitai/vulcan/pull/1031) [`22304f7`](https://github.com/drumkitai/vulcan/commit/22304f7a3a61ca8aa35d4862cd7888580ac364cd) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Change input field order in YardView form for better UX. -- boogs

- [#1035](https://github.com/drumkitai/vulcan/pull/1035) [`c6c8b0e`](https://github.com/drumkitai/vulcan/commit/c6c8b0e2884764dbf6fd82dcb4d91f65300c9914) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Default to dropoff appt tab is load has dropoff warehouse but not pickup - Lucas

- [#1033](https://github.com/drumkitai/vulcan/pull/1033) [`b81f877`](https://github.com/drumkitai/vulcan/commit/b81f877ab013ecc00313e1e91ee67455224f34cc) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Fix issue where Schedulers don't show up on FE - Lucas

- [#1037](https://github.com/drumkitai/vulcan/pull/1037) [`40b25ec`](https://github.com/drumkitai/vulcan/commit/40b25ecd89f452be00499678a5a5171015f84f83) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Display notifications for empty values on required fields in YardView form. -- boogs

## 0.47.0

### Minor Changes

- [#1029](https://github.com/drumkitai/vulcan/pull/1029) [`3e4c950`](https://github.com/drumkitai/vulcan/commit/3e4c950fc414b1149e55e15f354ef924509ff7f9) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Add YardView form and API flow. -- boogs

## 0.46.4

### Patch Changes

- [#1027](https://github.com/drumkitai/vulcan/pull/1027) [`ec58a7b`](https://github.com/drumkitai/vulcan/commit/ec58a7b033a702035b5e2c29be1c4064426c9fc3) Thanks [@lsouza4](https://github.com/lsouza4)! - Email Templates for CQ - Vulcan CSS fix - Lucas

## 0.46.3

### Patch Changes

- [#1024](https://github.com/drumkitai/vulcan/pull/1024) [`27d5312`](https://github.com/drumkitai/vulcan/commit/27d5312163b73dfaa2f660882842615e9856efe0) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Improvements on Load Info form for 3G TMS loads - Lucas

- [#1026](https://github.com/drumkitai/vulcan/pull/1026) [`a7b867e`](https://github.com/drumkitai/vulcan/commit/a7b867ee90e802f12a4be48e2d9af36064c72f41) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Support email templates for carrier quoting - Lucas

## 0.46.2

### Patch Changes

- [#1019](https://github.com/drumkitai/vulcan/pull/1019) [`8b7a276`](https://github.com/drumkitai/vulcan/commit/8b7a276acdbe0c4e039076304c2059560fa84f2f) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Lane History from Service should filter out 0-values - Lucas

- [#1022](https://github.com/drumkitai/vulcan/pull/1022) [`e237fdc`](https://github.com/drumkitai/vulcan/commit/e237fdcb9792b5306189255c81f6540d7a1f7224) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Revert updates on Greenscreens logo and rate names to Triumph - Lucas

- [#1021](https://github.com/drumkitai/vulcan/pull/1021) [`da41494`](https://github.com/drumkitai/vulcan/commit/da4149431d6e1c81d417248a47c0d4d751013af4) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Update Greenscreens logo and rate names to Triumph - Lucas

- [#1023](https://github.com/drumkitai/vulcan/pull/1023) [`d7a0974`](https://github.com/drumkitai/vulcan/commit/d7a0974281417a70e1fcf84c9967af36f272d85d) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - White Glove no longer auto select carrier locations - Winston

## 0.46.1

### Patch Changes

- [#1017](https://github.com/drumkitai/vulcan/pull/1017) [`02f739a`](https://github.com/drumkitai/vulcan/commit/02f739ae3fbe78741fd66625ecdb11f073947d7c) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Fix Jodit bug with character input - Lucas

- [#1015](https://github.com/drumkitai/vulcan/pull/1015) [`9e42dfe`](https://github.com/drumkitai/vulcan/commit/9e42dfe051fb030e0308fdd2080c141aa19765f3) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Add Batch Quote Indicator above suggestion cards and small UI improvements - Winston

- [#1018](https://github.com/drumkitai/vulcan/pull/1018) [`a932cb2`](https://github.com/drumkitai/vulcan/commit/a932cb2d8562fac1d684bf6b59b13a6d4fa78786) Thanks [@lsouza4](https://github.com/lsouza4)! - Fix Vulcan CI/CD - Lucas

## 0.46.0

### Minor Changes

- [#1009](https://github.com/drumkitai/vulcan/pull/1009) [`bfeb711`](https://github.com/drumkitai/vulcan/commit/bfeb711dd8f25164b73e0a6f63d6e838908a66a3) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Feature: Aljex Create Load - Sophie & Varun

- [#1011](https://github.com/drumkitai/vulcan/pull/1011) [`a0db99e`](https://github.com/drumkitai/vulcan/commit/a0db99ec1d621c718c02917200018a87482e5bfe) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Add batch quote route and add more detailed BQ Posthog logging - Winston

### Patch Changes

- [#1014](https://github.com/drumkitai/vulcan/pull/1014) [`6c4a88f`](https://github.com/drumkitai/vulcan/commit/6c4a88f427822a0e9dbcbf614ab55d1e525f60a0) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Fix issue with appt times being set to 00:00 - Lucas

- [#1013](https://github.com/drumkitai/vulcan/pull/1013) [`c3ce0cd`](https://github.com/drumkitai/vulcan/commit/c3ce0cd119aa2a901123a9f8d0b0dd07c205fc37) Thanks [@lsouza4](https://github.com/lsouza4)! - Add safe script execution for Chrome - Lucas

- [#1012](https://github.com/drumkitai/vulcan/pull/1012) [`3385944`](https://github.com/drumkitai/vulcan/commit/33859441a4ed252d091b0500836abcd684032d52) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Disable sucessful load building toasts - Lucas

## 0.45.6

### Patch Changes

- [#1007](https://github.com/drumkitai/vulcan/pull/1007) [`b558cef`](https://github.com/drumkitai/vulcan/commit/b558cefb712ea943ed9643852b012b05e3597832) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Adjust layout for load found to prevent awkward shifting due to scrollbar - Lucas

- [#997](https://github.com/drumkitai/vulcan/pull/997) [`1f8827d`](https://github.com/drumkitai/vulcan/commit/1f8827d8c01f2f58f1d04f34e063c39fc40533a6) Thanks [@Sophie1142](https://github.com/Sophie1142)! - Support additional E2Open reefer and equipment type formats -- Sophie

- [#1004](https://github.com/drumkitai/vulcan/pull/1004) [`0fa7b7e`](https://github.com/drumkitai/vulcan/commit/0fa7b7ed2bdb51e261be14ae5b86c10f3924b137) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Capture copied scheduling emails in RDS. -- Ronak and Boogs

- [#998](https://github.com/drumkitai/vulcan/pull/998) [`eef3907`](https://github.com/drumkitai/vulcan/commit/eef3907344050514d26ec1a4e8e061b8135939f8) Thanks [@Sophie1142](https://github.com/Sophie1142)! - Improve background worker/side panel error visibility - Sophie

- [#1006](https://github.com/drumkitai/vulcan/pull/1006) [`85735a5`](https://github.com/drumkitai/vulcan/commit/85735a57e0e68472d453a567b10512b385a17806) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Fix QQ inputs losing focus after keystrokes - Lucas

## 0.45.5

### Patch Changes

- [#1002](https://github.com/drumkitai/vulcan/pull/1002) [`f217946`](https://github.com/drumkitai/vulcan/commit/f21794670268abe66da8dc53381e7f4fe71f51d8) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Auto-update Alexandria from revert-679-eng-2545-show-a-loading-drum-if-an-email-is-being-ingested-or

## 0.45.4

### Patch Changes

- [#999](https://github.com/drumkitai/vulcan/pull/999) [`ec1d961`](https://github.com/drumkitai/vulcan/commit/ec1d961dc5993bb3d472d610faa72440409ac769) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Fix load detection issue - Lucas

## 0.45.3

### Patch Changes

- [#995](https://github.com/drumkitai/vulcan/pull/995) [`14fae9c`](https://github.com/drumkitai/vulcan/commit/14fae9c8b70d010a8ae6df9db08e437e7f443af0) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Remove extra spaces from itemDescription in CQ form - Lucas

## 0.45.2

### Patch Changes

- [#976](https://github.com/drumkitai/vulcan/pull/976) [`3cbc36e`](https://github.com/drumkitai/vulcan/commit/3cbc36ea62a7477f9a0ba2e9ce39321484b14918) Thanks [@patty-j](https://github.com/patty-j)! - Eng 2545 show a loading drum if an email is being ingested

## 0.45.1

### Patch Changes

- [#992](https://github.com/drumkitai/vulcan/pull/992) [`a1bb310`](https://github.com/drumkitai/vulcan/commit/a1bb310e3b4fce9b19ab9cc9978e3b8c93e4927c) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Replace Quill with Jodit - Lucas

## 0.45.0

### Minor Changes

- [#990](https://github.com/drumkitai/vulcan/pull/990) [`81799d3`](https://github.com/drumkitai/vulcan/commit/81799d3944b47fca4d267ae91acc78775691f6c7) Thanks [@dhruv4](https://github.com/dhruv4)! - Dummy changeset for 3G TMS changes

## 0.44.7

### Patch Changes

- [#987](https://github.com/drumkitai/vulcan/pull/987) [`ac9da89`](https://github.com/drumkitai/vulcan/commit/ac9da89bbcc0d47b6366efe26eb1acb2e3b41356) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Disable customer dropdown for unsupported TMSes (Able + Quick Quote fix) -- Sophie

- [#986](https://github.com/drumkitai/vulcan/pull/986) [`4dc2405`](https://github.com/drumkitai/vulcan/commit/4dc2405dd51240f43fc27a41927894bc867f7a88) Thanks [@Sophie1142](https://github.com/Sophie1142)! - ENG-3840 Fix E2Open distance parsing - Sophie

## 0.44.6

### Patch Changes

- [#984](https://github.com/drumkitai/vulcan/pull/984) [`5f99355`](https://github.com/drumkitai/vulcan/commit/5f993557fe812158d598c14a400ad80d079fdf6c) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Auto-update Alexandria from eng-3858-we-arent-saving-all-load-info-in-list-view-correctly

## 0.44.5

### Patch Changes

- [#982](https://github.com/drumkitai/vulcan/pull/982) [`4a27155`](https://github.com/drumkitai/vulcan/commit/4a2715543e55937ec55fedc8d77d46bd2d548f25) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Add third-party E2open flow. -- Ronak and Boogs

## 0.44.4

### Patch Changes

- [#980](https://github.com/drumkitai/vulcan/pull/980) [`a6dae90`](https://github.com/drumkitai/vulcan/commit/a6dae9095a2f6dea743bb73ade8ecffb7d1f52ae) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Auto-update Alexandria from eng-3876-putviewedloadhistory-request-failed-with-status-code-405

- [#974](https://github.com/drumkitai/vulcan/pull/974) [`5f222e3`](https://github.com/drumkitai/vulcan/commit/5f222e386cb53766b78ba67084cee72849659ef3) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Here I've moved the equipment input above the commodity input in the turvo load building form. - SAM

- [#978](https://github.com/drumkitai/vulcan/pull/978) [`91c8809`](https://github.com/drumkitai/vulcan/commit/91c8809a4b9381b20e8aafc32a3a830465dc67de) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Removing deprecated Email Scheduling form/preview components held NFI's table - Lucas

- [#979](https://github.com/drumkitai/vulcan/pull/979) [`95fec14`](https://github.com/drumkitai/vulcan/commit/95fec14e62828b73ba286dfd2f7f002e4842c117) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Fix DOE FSC not populating on first render - Sophie

- [#960](https://github.com/drumkitai/vulcan/pull/960) [`fa9c22d`](https://github.com/drumkitai/vulcan/commit/fa9c22dee04c856bd00a1d0780f276723985179e) Thanks [@lsouza4](https://github.com/lsouza4)! - Add Quill for rich-text editing - Lucas

- [#977](https://github.com/drumkitai/vulcan/pull/977) [`58e6598`](https://github.com/drumkitai/vulcan/commit/58e6598eec9832d73c2fd63043986b73ca3e3de1) Thanks [@Sophie1142](https://github.com/Sophie1142)! - Fix reefer mapping in E2Open -- Sophie

## 0.44.3

### Patch Changes

- [#971](https://github.com/drumkitai/vulcan/pull/971) [`3d33f07`](https://github.com/drumkitai/vulcan/commit/3d33f07848e5ee7596cc2c9df7ad6bac30114e7b) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Here I've added a feature flag to control if only city and state are required for McLeod load building or not - SAM

- [#973](https://github.com/drumkitai/vulcan/pull/973) [`1c3b5c7`](https://github.com/drumkitai/vulcan/commit/1c3b5c761e1d8146a96057d5dc9460c10fc592b7) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Support NFI fields on scheduling templates - Lucas

## 0.44.2

### Patch Changes

- [#969](https://github.com/drumkitai/vulcan/pull/969) [`64cace7`](https://github.com/drumkitai/vulcan/commit/64cace78cb10168a39de55eead4a063c2d7a41fd) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Render HTML scheduling email templates correctly. -- boogs

## 0.44.1

### Patch Changes

- [#967](https://github.com/drumkitai/vulcan/pull/967) [`3d07ef9`](https://github.com/drumkitai/vulcan/commit/3d07ef9e08f8ea0c71671dce600a0c57998974f9) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Fix Appointment Scheduling logic for showing Email Request - Lucas

## 0.44.0

### Minor Changes

- [#966](https://github.com/drumkitai/vulcan/pull/966) [`b312a28`](https://github.com/drumkitai/vulcan/commit/b312a28974c96fa5ff2a0284400e72a3518d7b26) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Add new Scheduling 1.0 flow. -- boogs and lucas

### Patch Changes

- [#961](https://github.com/drumkitai/vulcan/pull/961) [`e1f1312`](https://github.com/drumkitai/vulcan/commit/e1f13124c5a1d748167e60f19d9f2d4a731f7a82) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - here I added the planning comment field that fetch requested - SAM

- [#965](https://github.com/drumkitai/vulcan/pull/965) [`b5d5859`](https://github.com/drumkitai/vulcan/commit/b5d5859b651f770fb089ff5a4efd6464a20f76ee) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Fixing and improving descriptive errors for the Quick Quote form - Lucas

- [#964](https://github.com/drumkitai/vulcan/pull/964) [`2755cc1`](https://github.com/drumkitai/vulcan/commit/2755cc184fb1effc551a89c23439130adce0f011) Thanks [@lsouza4](https://github.com/lsouza4)! - Update PR Template - Lucas

- [#963](https://github.com/drumkitai/vulcan/pull/963) [`765c8f4`](https://github.com/drumkitai/vulcan/commit/765c8f4e21f21a951952c6652deec4587e818c61) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Added contact and name fields to the load information tab at Fetch Freight's request - SAM

## 0.43.1

### Patch Changes

- [#959](https://github.com/drumkitai/vulcan/pull/959) [`c0c9fdd`](https://github.com/drumkitai/vulcan/commit/c0c9fddb51d163f27b2fd3573ffa8222b2608392) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Add TMS Lane History to Batch Quote and feature flag usage - Winston

- [#956](https://github.com/drumkitai/vulcan/pull/956) [`d928792`](https://github.com/drumkitai/vulcan/commit/d928792629a2a4ecdb2f1aaae103bd3d0d67114c) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Here I've removed the required arg from the tms submission customer input in the quick quote form. We now rely solely on the manual validation. - SAM

## 0.43.0

### Minor Changes

- [#955](https://github.com/drumkitai/vulcan/pull/955) [`1f2add5`](https://github.com/drumkitai/vulcan/commit/1f2add59c1a441b85f85c946edeb12bfe3830872) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Batch Quote beta - Winston

### Patch Changes

- [#952](https://github.com/drumkitai/vulcan/pull/952) [`e3edc5a`](https://github.com/drumkitai/vulcan/commit/e3edc5a8f941704b132c0f976e8d5b5e90f5fbb3) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Add support for customer-specific GlobalTranzTMS Lane History

- [#953](https://github.com/drumkitai/vulcan/pull/953) [`35f665a`](https://github.com/drumkitai/vulcan/commit/35f665ac343e0a166b3d9a786fc03f5bf3c20d94) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Here I added a check for fscProvider from the quick quote config. Fetch Freight wants to default to DOE. - SAM

- [#954](https://github.com/drumkitai/vulcan/pull/954) [`0bd6681`](https://github.com/drumkitai/vulcan/commit/0bd6681c418ecc4e4213ffb058beb0f630356a3c) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Here I've added a tooltip for GS quote info to help end-users understand where the rates are coming from. - SAM

- [#951](https://github.com/drumkitai/vulcan/pull/951) [`fc7c9b9`](https://github.com/drumkitai/vulcan/commit/fc7c9b9c608dcda0ccca8f87d8b7c8de698fa8e5) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Here I removed the orderType field from the McleodLoadBuildingForm for Fetch Freight as they don't need it. - SAM

- [#949](https://github.com/drumkitai/vulcan/pull/949) [`d8684ec`](https://github.com/drumkitai/vulcan/commit/d8684ec25ac254b7adee2ebe77495a4d69c313b5) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - File upload size limit stated as 3MB and change white glove template signature for SDS - Winston

## 0.42.3

### Patch Changes

- [#941](https://github.com/drumkitai/vulcan/pull/941) [`9052e98`](https://github.com/drumkitai/vulcan/commit/9052e9834488be5f839e81ef85e9aad45c33a457) Thanks [@Winston-Hsiao](https://github.com/Winston-Hsiao)! - Add framer motion package needed for batch quote - Winston

- [#947](https://github.com/drumkitai/vulcan/pull/947) [`91c2ff2`](https://github.com/drumkitai/vulcan/commit/91c2ff23e4fc1feb0f0cdbd9bca64060e9e3655d) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Fix supporting Canadian locations in Carrier Quoting - Sophie

- [#948](https://github.com/drumkitai/vulcan/pull/948) [`5675b72`](https://github.com/drumkitai/vulcan/commit/5675b7243001468e3a801c89f117bfa488f08d58) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Cleanup event listeners and timers that may interfere with garbage collection - Winston

- [#946](https://github.com/drumkitai/vulcan/pull/946) [`11d3f06`](https://github.com/drumkitai/vulcan/commit/11d3f06dd653b15efc1678af73b5ec9dcd9e3ff1) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Here I fixed an issue where the QQ customer input UI wasn't updating when a value was set. The customer needs to be in the dropdown for it to appear in the UI. - SAM

## 0.42.2

### Patch Changes

- [#942](https://github.com/drumkitai/vulcan/pull/942) [`2ab1c32`](https://github.com/drumkitai/vulcan/commit/2ab1c3260a61a6db94f271baa07ed83459ac1a34) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Here I've added an option to confirm an appointment to the loadInfoTab for Fetch Freight. - SAM

## 0.42.1

### Patch Changes

- [#932](https://github.com/drumkitai/vulcan/pull/932) [`0652a86`](https://github.com/drumkitai/vulcan/commit/0652a869475f072a5763470fc2212dcf2590c9d3) Thanks [@Winston-Hsiao](https://github.com/Winston-Hsiao)! - Fix double search input in LoadView - Winston

- [#929](https://github.com/drumkitai/vulcan/pull/929) [`f203ca4`](https://github.com/drumkitai/vulcan/commit/f203ca45b8ce0958d656a90cf67663123ac4a581) Thanks [@Winston-Hsiao](https://github.com/Winston-Hsiao)! - Update InboxSDK version - Winston

- [#928](https://github.com/drumkitai/vulcan/pull/928) [`099bea8`](https://github.com/drumkitai/vulcan/commit/099bea861ee474a0fb8e1976563fa22114c1cb27) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Fix advanced search UI cut off in gmail sidebar - Sam

- [#935](https://github.com/drumkitai/vulcan/pull/935) [`5c6de49`](https://github.com/drumkitai/vulcan/commit/5c6de4952a4882d4d9a922ed25fbe58fb5ac388d) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - CQ Review Carrier Responses shows up after sending out carrier quote emails - Winston

## 0.42.0

### Minor Changes

- [#925](https://github.com/drumkitai/vulcan/pull/925) [`5f13569`](https://github.com/drumkitai/vulcan/commit/5f135691ce96fe1a23f093ef011a66c121f88b92) Thanks [@Winston-Hsiao](https://github.com/Winston-Hsiao)! - Improve chrome extension performance - Winston

## 0.41.6

### Patch Changes

- [#922](https://github.com/drumkitai/vulcan/pull/922) [`c23a7b4`](https://github.com/drumkitai/vulcan/commit/c23a7b4ee082a5d98503f3de313ab41ebbfc7c69) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - QQ QuoteCard always display transport type - Winston

## 0.41.5

### Patch Changes

- [#920](https://github.com/drumkitai/vulcan/pull/920) [`3b54fe1`](https://github.com/drumkitai/vulcan/commit/3b54fe16a85fe588b213b78475ea544878521d95) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - QQ Quote Card tooltips for mapped transp types now uses quoting integration name - Winston

## 0.41.4

### Patch Changes

- [#915](https://github.com/drumkitai/vulcan/pull/915) [`ea89418`](https://github.com/drumkitai/vulcan/commit/ea89418fecfca1ba46c76a52e3172f3fe4a35882) Thanks [@Sophie1142](https://github.com/Sophie1142)! - Bidding portal fixes & improvements -- Sophie

## 0.41.3

### Patch Changes

- [#917](https://github.com/drumkitai/vulcan/pull/917) [`d851d81`](https://github.com/drumkitai/vulcan/commit/d851d8188e09ce45d9dd385a7bd75a43718858d5) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Add selectedQuickQuoteId capture to quick quote form - Patrick

- [#917](https://github.com/drumkitai/vulcan/pull/917) [`d851d81`](https://github.com/drumkitai/vulcan/commit/d851d8188e09ce45d9dd385a7bd75a43718858d5) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Quick quote transport type dropdown mapping update and clarity for GS/DAT lookups - Winston

## 0.41.2

### Patch Changes

- [#914](https://github.com/drumkitai/vulcan/pull/914) [`cbaf96f`](https://github.com/drumkitai/vulcan/commit/cbaf96f2bbb97bc68a0df540f19bdb1f9b307120) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Here I have made sure we submit zero-time dates to the BE if user doesn't enter dates. This resolves the trident olympus no rate info bug. - SAM

- [#906](https://github.com/drumkitai/vulcan/pull/906) [`f2d917f`](https://github.com/drumkitai/vulcan/commit/f2d917f875719e168487d8933d6aeb696573ecd4) Thanks [@Sophie1142](https://github.com/Sophie1142)! - Eng 3656 E2Open follow-ups: don't submit automatically and fix submission for Make Offer (Simple) flow for Fetch - Sophie

- [#910](https://github.com/drumkitai/vulcan/pull/910) [`912a94d`](https://github.com/drumkitai/vulcan/commit/912a94d40fd7fe991407bf0fce16c91d6c4ff1a7) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Here I have resolved the sidebar loading bug where switching between a load email and a suggestion email would result in the load not showing up. - SAM

- [#912](https://github.com/drumkitai/vulcan/pull/912) [`9a27d1c`](https://github.com/drumkitai/vulcan/commit/9a27d1c8a45c2f8a244500144d86dfbc41f30ad8) Thanks [@Sophie1142](https://github.com/Sophie1142)! - Update Alexandria pointer to include E2Open improvements - Sophie

## 0.41.1

### Patch Changes

- [#901](https://github.com/drumkitai/vulcan/pull/901) [`799218c`](https://github.com/drumkitai/vulcan/commit/799218c80eeb4891e624167f52b1b9068273ecd4) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Here I've added additional reference numbers to load building form. - SAM

- [#905](https://github.com/drumkitai/vulcan/pull/905) [`0bfd20e`](https://github.com/drumkitai/vulcan/commit/0bfd20e24f3763d8ad4502148d2854f83f103257) Thanks [@Winston-Hsiao](https://github.com/Winston-Hsiao)! - Fix conditional hook calls in quote sidebar - Winston

- [#908](https://github.com/drumkitai/vulcan/pull/908) [`9d93368`](https://github.com/drumkitai/vulcan/commit/9d93368e57be93aa2db3394211bf675070fc35bb) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Fetch Freight is having internal conflicts due to us setting salesperson for them in McLeod loads. We disabled this. - SAM

- [#907](https://github.com/drumkitai/vulcan/pull/907) [`f36be03`](https://github.com/drumkitai/vulcan/commit/f36be0302c4c277ace4fbf3828027bce444e9361) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Don't fetch operators for NFI 3G loads - Lucas

- [#909](https://github.com/drumkitai/vulcan/pull/909) [`95f4fa7`](https://github.com/drumkitai/vulcan/commit/95f4fa7a6f5f3f5ff75c2999b21a7d0777fde471) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Add QQ and CQ copy buttons to log to Posthog - Winston

- [#902](https://github.com/drumkitai/vulcan/pull/902) [`b866392`](https://github.com/drumkitai/vulcan/commit/b866392a39c1f71927a4ff92642a2859e46373ae) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - PostHog improvements for realtime Slack notifs - Lucas

- [#899](https://github.com/drumkitai/vulcan/pull/899) [`fd2e362`](https://github.com/drumkitai/vulcan/commit/fd2e362ded43710ebb49f2269448d225c0ca8f83) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Here I've created native search for locations. - SAM

## 0.41.0

### Minor Changes

- [#897](https://github.com/drumkitai/vulcan/pull/897) [`2bdf97d`](https://github.com/drumkitai/vulcan/commit/2bdf97dc4f0c9fa0fd4d131c3fbd68d9485d097a) Thanks [@Winston-Hsiao](https://github.com/Winston-Hsiao)! - Carrier Quote Carrier Select UI Revamp - Winston

### Patch Changes

- [#889](https://github.com/drumkitai/vulcan/pull/889) [`5b62238`](https://github.com/drumkitai/vulcan/commit/5b62238226c309c5af2c5e9fd7f8d56e4c38b7fc) Thanks [@Sophie1142](https://github.com/Sophie1142)! - ENG-3584 FreightView improvement: submit quote to portal & isolate SidePanel context - Sophie

- [#898](https://github.com/drumkitai/vulcan/pull/898) [`51c063c`](https://github.com/drumkitai/vulcan/commit/51c063c8351a529910bca50e810defc32a52b8c5) Thanks [@Sophie1142](https://github.com/Sophie1142)! - Feat: Implement E2Open portal scraping for Fetch & PLS - Sophie

- [#895](https://github.com/drumkitai/vulcan/pull/895) [`45532f1`](https://github.com/drumkitai/vulcan/commit/45532f1400fe76575f8cac43509d10a43c59a2f5) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Prevent GlobalTranzTMS customer fetching on CQ flow - Lucas

- [#893](https://github.com/drumkitai/vulcan/pull/893) [`69e7d97`](https://github.com/drumkitai/vulcan/commit/69e7d9738df94263637566da21006d6e82f281f2) Thanks [@lsouza4](https://github.com/lsouza4)! - Fix load searchbar in QQ for PLS - Lucas

## 0.40.1

### Patch Changes

- [#892](https://github.com/drumkitai/vulcan/pull/892) [`6f09325`](https://github.com/drumkitai/vulcan/commit/6f093256d619249b644bb21348c18cddd21333ea) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Refactor Quick Quote dates logic - Lucas

- [#887](https://github.com/drumkitai/vulcan/pull/887) [`61214a5`](https://github.com/drumkitai/vulcan/commit/61214a5e7be3e1cc850bf5836a6929b0f3baef9b) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Here I've included the user's revenue code in the authuser object and solved one or two bugs. -SAM

- [#891](https://github.com/drumkitai/vulcan/pull/891) [`1286946`](https://github.com/drumkitai/vulcan/commit/1286946015f8bef8b9dcd933606f11dd2177c696) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Here I've added a McLeodLoadBuildingForm input for salesperson1 and salesperson1percent. This is available for Fetch Freight. - SAM

## 0.40.0

### Minor Changes

- [#883](https://github.com/drumkitai/vulcan/pull/883) [`3536bd9`](https://github.com/drumkitai/vulcan/commit/3536bd9ac849d015030745a7fa63c6b3ac28ec45) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Quote calc in QQ and CQ now respect margin vs markup feature flag, fixed margin equation for PLS - Winston

### Patch Changes

- [#886](https://github.com/drumkitai/vulcan/pull/886) [`808bd49`](https://github.com/drumkitai/vulcan/commit/808bd49b993d641fa4ec1a1c90270024add5254a) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Update carrier quote email send toast message - Winston

- [#884](https://github.com/drumkitai/vulcan/pull/884) [`f01391c`](https://github.com/drumkitai/vulcan/commit/f01391c2aeabc9d0b6d6bb81bba241a177cd5fa2) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Fix alignment bug in Load Building form - Lucas

- [#880](https://github.com/drumkitai/vulcan/pull/880) [`209903d`](https://github.com/drumkitai/vulcan/commit/209903d7f075baa5a16d1e99cf4d1d10c3fc23c6) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Here I've added two fields, pallet required and pallet count for the McLeodLoadBuildingForm - Sam

- [#882](https://github.com/drumkitai/vulcan/pull/882) [`ff46629`](https://github.com/drumkitai/vulcan/commit/ff46629252ba1ce86abd745dc6f845cfc2073cf3) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Pass in request type from Opendock and Retalix scheduling forms to API request. (Used for creating warehouse and address assocations) -- boogs

- [#869](https://github.com/drumkitai/vulcan/pull/869) [`1e3519c`](https://github.com/drumkitai/vulcan/commit/1e3519c34ff6b7fed605b73d02b98ed25a7771b8) Thanks [@lsouza4](https://github.com/lsouza4)! - Fixing Vulcan hot-reloading local builds - Lucas

- [#885](https://github.com/drumkitai/vulcan/pull/885) [`560558d`](https://github.com/drumkitai/vulcan/commit/560558dc973ee94ae87be0fe68c0c4f4880626d4) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Filter out empty carrier groups, show CQ tab badge iff carrier respond, update UI and toast - Winston

## 0.39.1

### Patch Changes

- [#878](https://github.com/drumkitai/vulcan/pull/878) [`e4270c3`](https://github.com/drumkitai/vulcan/commit/e4270c35ba9e52dd11b8c9b589d1fae527f9585a) Thanks [@lsouza4](https://github.com/lsouza4)! - Updating stale Alexandria pointer - Lucas

## 0.39.0

### Minor Changes

- [#876](https://github.com/drumkitai/vulcan/pull/876) [`ee12489`](https://github.com/drumkitai/vulcan/commit/ee124899d1e53ee4bd4843576cfaed69f6ec577c) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Carrier Quote using Carrier Groups - Winston

### Patch Changes

- [#872](https://github.com/drumkitai/vulcan/pull/872) [`1805ce8`](https://github.com/drumkitai/vulcan/commit/1805ce87d23de4e1a800e7e51557124d7dc7a0cd) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Supporting QQ email templates - Lucas

- [#875](https://github.com/drumkitai/vulcan/pull/875) [`2b87ee1`](https://github.com/drumkitai/vulcan/commit/2b87ee1abcb5d9da3b06a98265826f19afe3e898) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Here I've added Trident and Fetch McLeod commodities and handled them in suggestions too. On submission, the commodities map right into McLeod. - SAM

- [#877](https://github.com/drumkitai/vulcan/pull/877) [`b2fc0bd`](https://github.com/drumkitai/vulcan/commit/b2fc0bdc557d1cfd07e132230005074467bb2d9a) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Badge for CQ tab, capture selected group id - Winston

- [#874](https://github.com/drumkitai/vulcan/pull/874) [`db5fdd3`](https://github.com/drumkitai/vulcan/commit/db5fdd3255e4536f2fd68473ef1ae01672a938c0) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Here I made a small addition to the Fetch Freight trailer types by including the code in the description - SAM

## 0.38.1

### Patch Changes

- [#870](https://github.com/drumkitai/vulcan/pull/870) [`cb23257`](https://github.com/drumkitai/vulcan/commit/cb23257e5aac97d3c748f1a5963177f2b60b4546) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Fixing QQ margin issues - Lucas

## 0.38.0

### Minor Changes

- [#868](https://github.com/drumkitai/vulcan/pull/868) [`db6065f`](https://github.com/drumkitai/vulcan/commit/db6065ffd2a204bbbf077bc8826caabe2c5c1aca) Thanks [@Sophie1142](https://github.com/Sophie1142)! - Feature: Add FreightView AI parsing - Sophie

### Patch Changes

- [#867](https://github.com/drumkitai/vulcan/pull/867) [`b57c00f`](https://github.com/drumkitai/vulcan/commit/b57c00fb58256d66d5f3d0fcf4f63c62dd4dba98) Thanks [@lsouza4](https://github.com/lsouza4)! - Add missing animation CSS to inline hint - Lucas

- [#864](https://github.com/drumkitai/vulcan/pull/864) [`48f28d0`](https://github.com/drumkitai/vulcan/commit/48f28d06f88ea6565348892ae63e413931ca4355) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Fix misaligned LB form fields on Front - Lucas

- [#866](https://github.com/drumkitai/vulcan/pull/866) [`b728b5a`](https://github.com/drumkitai/vulcan/commit/b728b5a04e854fed1cfbdcf251702fa6560c21da) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Support custom default margins based on user - Lucas

## 0.37.2

### Patch Changes

- [#861](https://github.com/drumkitai/vulcan/pull/861) [`9c45d73`](https://github.com/drumkitai/vulcan/commit/9c45d739088a3f14ffc6fde9c90f21e366467106) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Trimming 9-digit zipcodes to 5-digit format - Lucas

## 0.37.1

### Patch Changes

- [#855](https://github.com/drumkitai/vulcan/pull/855) [`d2f1f82`](https://github.com/drumkitai/vulcan/commit/d2f1f8207bd06ab98f501d0122dd65aeec04f753) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Default tab to Quick Quote if enabled; requested by Windmill but set for everyone - Sophie

- [#856](https://github.com/drumkitai/vulcan/pull/856) [`b3ccebb`](https://github.com/drumkitai/vulcan/commit/b3ccebb9d1f1bb2f2ce510e8b64157de3e82056d) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Address potential emailID race condition -- Sam

- [#858](https://github.com/drumkitai/vulcan/pull/858) [`0fe4181`](https://github.com/drumkitai/vulcan/commit/0fe4181f5ac2a8df74ffec0842f175b8fb551ac7) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Don't display empty page in request appointment section. -- boogs

- [#859](https://github.com/drumkitai/vulcan/pull/859) [`2b91883`](https://github.com/drumkitai/vulcan/commit/2b91883bd539f8e2c5d18a88085abb824f2929ca) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Fix load building for Fetch by handling tenant-level customizations - Sophie

- [#860](https://github.com/drumkitai/vulcan/pull/860) [`1a4b973`](https://github.com/drumkitai/vulcan/commit/1a4b973b51fa2805fc51c2580722c3f97978edb8) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Set default option for appointment scheduling dropdown to warehouse portal instead of email reply. -- boogs

## 0.37.0

### Minor Changes

- [#849](https://github.com/drumkitai/vulcan/pull/849) [`6cb66c5`](https://github.com/drumkitai/vulcan/commit/6cb66c5db9f4b233ea8b9fc0fb2b93668d869bd7) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Improving Redwood errors, adding TruckList email reprocessing - Lucas

### Patch Changes

- [#848](https://github.com/drumkitai/vulcan/pull/848) [`be0eed7`](https://github.com/drumkitai/vulcan/commit/be0eed7452e240829c5b2631209a3da833c78ffe) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - LoadView specifications transport type select input - Winston

- [#853](https://github.com/drumkitai/vulcan/pull/853) [`380d2b5`](https://github.com/drumkitai/vulcan/commit/380d2b5454c1a1419e95c7d58c117285f26e1138) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Carrier Quote Config for FE - Winston

- [#845](https://github.com/drumkitai/vulcan/pull/845) [`35087f0`](https://github.com/drumkitai/vulcan/commit/35087f072bce2c93b49caa29c9b7368dc563f3b4) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Add E2Open integration for Drumkit - boogs

- [#851](https://github.com/drumkitai/vulcan/pull/851) [`874edbe`](https://github.com/drumkitai/vulcan/commit/874edbe08521a6672576639c8a39e873cb3913cb) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Display warehouse information in E2open scheduling flow and fix timezone issue of slots. -- boogs

- [#850](https://github.com/drumkitai/vulcan/pull/850) [`5087bab`](https://github.com/drumkitai/vulcan/commit/5087bab7820e05e5dd7871b4397affec96b43c1e) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Fix Relay check call functionality; require Dispatch Source and ETA when dispatching via Load Info (not check call) form - Sophie

- [#847](https://github.com/drumkitai/vulcan/pull/847) [`5ac5b26`](https://github.com/drumkitai/vulcan/commit/5ac5b268246295c1efbc2c80acd7d87dc833ea9c) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Adding DAT Reports and Companies data for Olympus quote - Lucas

- [#854](https://github.com/drumkitai/vulcan/pull/854) [`9ac6ae1`](https://github.com/drumkitai/vulcan/commit/9ac6ae1cf686b5971c004013364ab45e6d674057) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Here I included the quick quote config in the service features so services can have custom quick quote form defaults at all steps of the form. - SAM

## 0.36.3

### Patch Changes

- [#837](https://github.com/drumkitai/vulcan/pull/837) [`f4effde`](https://github.com/drumkitai/vulcan/commit/f4effdeef7119e065b49ddf31a6039ba79af0d78) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Here I've ensured that loads cannot be updated in the sidebar, I've also ensured that transport types are visible - SAM

- [#841](https://github.com/drumkitai/vulcan/pull/841) [`b59f98a`](https://github.com/drumkitai/vulcan/commit/b59f98ab11c74ec1eb1b0eabd2d4ec71418b652a) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Copy button for track and trace email draft body - Winston

- [#844](https://github.com/drumkitai/vulcan/pull/844) [`2e0e261`](https://github.com/drumkitai/vulcan/commit/2e0e2611e3c1ad42c35c3800a253d517c8a39a24) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - CQ Allow users to upload and send emails to carriers with new attachments - Winston

- [#840](https://github.com/drumkitai/vulcan/pull/840) [`b92b2db`](https://github.com/drumkitai/vulcan/commit/b92b2db119e420979d179936e8c2fb6c669829c2) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Hiding Tai unavailable features - Sam/Lucas

- [#843](https://github.com/drumkitai/vulcan/pull/843) [`ceaa6e3`](https://github.com/drumkitai/vulcan/commit/ceaa6e33195ef6cb4f58402fba11298054f1aa6a) Thanks [@Winston-Hsiao](https://github.com/Winston-Hsiao)! - Add react-dropzone package for new file upload - Winston

- [#842](https://github.com/drumkitai/vulcan/pull/842) [`8888150`](https://github.com/drumkitai/vulcan/commit/8888150421e025b6a5a897a326515db51b90b9e4) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Adding GlobalTranz TMS integration - Lucas

## 0.36.2

### Patch Changes

- [#832](https://github.com/drumkitai/vulcan/pull/832) [`84e2c3a`](https://github.com/drumkitai/vulcan/commit/84e2c3a08dbd2e4e8765047a0fee871fb39ee2ab) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Preventing suggestion badge count when no valid suggestions - Lucas

- [#834](https://github.com/drumkitai/vulcan/pull/834) [`e5d37cf`](https://github.com/drumkitai/vulcan/commit/e5d37cfe00312db01fc3ca10d1f24dad877d027d) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Fix: nesting Help Center icon under profile dropdown - Lucas

- [#833](https://github.com/drumkitai/vulcan/pull/833) [`c47ee12`](https://github.com/drumkitai/vulcan/commit/c47ee127791f87778c0323135c234a4a813f4596) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Embedding Notiondesk page into sidebar - Lucas

- [#828](https://github.com/drumkitai/vulcan/pull/828) [`e6c6166`](https://github.com/drumkitai/vulcan/commit/e6c616658d58fffa78788c3c00f38d7401ceb3ba) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Load view hyper link to turvo shipment page for load - Winston

- [#836](https://github.com/drumkitai/vulcan/pull/836) [`c61b6dd`](https://github.com/drumkitai/vulcan/commit/c61b6ddeaf0a4a195f5bf1f5df7b5a13056c429e) Thanks [@Winston-Hsiao](https://github.com/Winston-Hsiao)! - Decrease global font size to 14 from 16 - Winston

- [#830](https://github.com/drumkitai/vulcan/pull/830) [`058a772`](https://github.com/drumkitai/vulcan/commit/058a77232e9c3e7275d94303aaae2ac4be2f107b) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - QQ support Sprinter TMS Lane History and update User Nav Dropdown - Winston

## 0.36.1

### Patch Changes

- [#826](https://github.com/drumkitai/vulcan/pull/826) [`5d38500`](https://github.com/drumkitai/vulcan/commit/5d385002fa48cce56d79c4f0379657b0a303e4ab) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Fix TMS Lane History Graph display based on feature flag - Winston

## 0.36.0

### Minor Changes

- [#822](https://github.com/drumkitai/vulcan/pull/822) [`307a46c`](https://github.com/drumkitai/vulcan/commit/307a46c13771b5dca1ced9b623caff8319ba01ff) Thanks [@Sophie1142](https://github.com/Sophie1142)! - Whitelist sidepanel on almost all sites - Sophie

## 0.35.7

### Patch Changes

- [#823](https://github.com/drumkitai/vulcan/pull/823) [`9b0aed2`](https://github.com/drumkitai/vulcan/commit/9b0aed29479ea78598060be6186a85a7f0ecce0d) Thanks [@Sophie1142](https://github.com/Sophie1142)! - Fix Turvo logo rendering by increase assetsInLineLimit in Vite config - Sophie & Winston

## 0.35.6

### Patch Changes

- [#821](https://github.com/drumkitai/vulcan/pull/821) [`8b675ee`](https://github.com/drumkitai/vulcan/commit/8b675ee97f8b81d4349a7d638fae5630f7e2d4b0) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Unblock lane history when service only has isTMSLaneHistoryEnabled - Lucas

- [#817](https://github.com/drumkitai/vulcan/pull/817) [`380cf80`](https://github.com/drumkitai/vulcan/commit/380cf80215c141653119c32190e686d78e4eabb2) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Turvo Icon for QQ Turvo Lane History -Winston

- [#819](https://github.com/drumkitai/vulcan/pull/819) [`a9c6ed1`](https://github.com/drumkitai/vulcan/commit/a9c6ed15017c5c6a1cfb2885fcbfd28ec959cacd) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Lane History UI Improvements - Winston

- [#812](https://github.com/drumkitai/vulcan/pull/812) [`065cde6`](https://github.com/drumkitai/vulcan/commit/065cde61a6f932d1b4956038334274cc8aba8d74) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Hide 'Save quote' if feature flag is disabled - Lucas

- [#814](https://github.com/drumkitai/vulcan/pull/814) [`2601b5d`](https://github.com/drumkitai/vulcan/commit/2601b5d49a7aa0bd2ed2dfa22b0d39b8d3f53d00) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Add label to explain low cost carrier for lane history - Lucas

- [#816](https://github.com/drumkitai/vulcan/pull/816) [`03fd24d`](https://github.com/drumkitai/vulcan/commit/03fd24d308c62aba0cc857b3c87b0ad3fa7bfefc) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Matching Canada postal codes and provinces for QQ - Lucas

- [#820](https://github.com/drumkitai/vulcan/pull/820) [`20e6c4e`](https://github.com/drumkitai/vulcan/commit/20e6c4eae564c894b61f11f41b348f25f0fd1739) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Add validation to Update Load form when dispatching Relay load - Sophie

- [#815](https://github.com/drumkitai/vulcan/pull/815) [`305ac2c`](https://github.com/drumkitai/vulcan/commit/305ac2c7ebd65572aa880e6fee98ce30746b96d1) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Reword Lane History tooltip for per-mile price range - Lucas

## 0.35.5

### Patch Changes

- [#810](https://github.com/drumkitai/vulcan/pull/810) [`789fc1c`](https://github.com/drumkitai/vulcan/commit/789fc1c3db2987a9100f89694dfdbecf5d36f9a3) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Decouple TMS quote submission from reply drafts - Lucas

- [#808](https://github.com/drumkitai/vulcan/pull/808) [`22755b3`](https://github.com/drumkitai/vulcan/commit/22755b35675d9f3e8d1d3623f0041380690969d1) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Adding FE support to per-mile prices from GlobalTranz - Lucas

## 0.35.4

### Patch Changes

- [#806](https://github.com/drumkitai/vulcan/pull/806) [`de023e2`](https://github.com/drumkitai/vulcan/commit/de023e28245737bd7e26b9051d78a535a514f96d) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Allow users to upload attachments from original email - Sophie

- [#804](https://github.com/drumkitai/vulcan/pull/804) [`e1d4328`](https://github.com/drumkitai/vulcan/commit/e1d432809a974430685e46aabb5493c4eb7f57eb) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - CarrierQuote notes display for carrier locations - Winston

## 0.35.3

### Patch Changes

- [#802](https://github.com/drumkitai/vulcan/pull/802) [`64caf1c`](https://github.com/drumkitai/vulcan/commit/64caf1ceba025112ff2b19ca7d4b76d12ce5d8d2) Thanks [@jinyanzang](https://github.com/jinyanzang)! - add freightwise support - JIN

- [#796](https://github.com/drumkitai/vulcan/pull/796) [`a084996`](https://github.com/drumkitai/vulcan/commit/a0849966ae46c817fc5d82da2bb4eaf677b7de92) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Prevent flashy DAT blue box in QQ - Lucas

- [#802](https://github.com/drumkitai/vulcan/pull/802) [`19b6db4`](https://github.com/drumkitai/vulcan/commit/19b6db4411d0ce59ec96108c84becfbd163bf6b9) Thanks [@jinyanzang](https://github.com/jinyanzang)! - Add Freightwise support - JIN

- [#794](https://github.com/drumkitai/vulcan/pull/794) [`13e0c42`](https://github.com/drumkitai/vulcan/commit/13e0c4283df83041c84476b0bf7776dd03fae140) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Fixing duplicated carrier cards on Quick Quote - Lucas

- [#790](https://github.com/drumkitai/vulcan/pull/790) [`eefde1c`](https://github.com/drumkitai/vulcan/commit/eefde1c835afa60de369f4bfe3f139dc430392d6) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Adding option to toggle fuel surcharge types on QQ Calculator - Lucas

- [#799](https://github.com/drumkitai/vulcan/pull/799) [`29c8775`](https://github.com/drumkitai/vulcan/commit/29c8775dd5b2514ad5e44df56a7e56a812f5ccaf) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Removing DAT Feature Flag - Lucas

- [#789](https://github.com/drumkitai/vulcan/pull/789) [`dd94f48`](https://github.com/drumkitai/vulcan/commit/dd94f484c51caea96b1da6de85a96636426be51e) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Storing user's quoting integrations in FE Context - Lucas

- [#791](https://github.com/drumkitai/vulcan/pull/791) [`20fcf52`](https://github.com/drumkitai/vulcan/commit/20fcf52abfd135d72dc4842136b10e5ef2dc32f8) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Support submitting quotes to TMS in Front - Lucas

- [#793](https://github.com/drumkitai/vulcan/pull/793) [`efbd528`](https://github.com/drumkitai/vulcan/commit/efbd5285fbce2f1d721e36ee6ce437180a265fe4) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Allowing QQ to work properly if service only has Lane History integrations - Lucas

- [#795](https://github.com/drumkitai/vulcan/pull/795) [`84f43c9`](https://github.com/drumkitai/vulcan/commit/84f43c956df05b3d6001db6055af039ca7d13855) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Here I've added the emailID tag to the QQ and CQ posthog configs. I've also added the has_qr_suggestions tag to the QQ posthog config. - SAM

- [#800](https://github.com/drumkitai/vulcan/pull/800) [`1fe8115`](https://github.com/drumkitai/vulcan/commit/1fe8115122b506d50d775d4107b27e8bb4c8824c) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Improvements for QQ Submit to TMS logic - Lucas

- [#792](https://github.com/drumkitai/vulcan/pull/792) [`691fb8e`](https://github.com/drumkitai/vulcan/commit/691fb8e47b1f1e05c185208d2cc9684765f17e89) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Visual improvements for QQ Lane History chart - Lucas

- [#787](https://github.com/drumkitai/vulcan/pull/787) [`77efa80`](https://github.com/drumkitai/vulcan/commit/77efa803092865cef5bffad43bfb8405a17e8f8f) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Fixing misalignment on QQ Inputs - Lucas

## 0.35.2

### Patch Changes

- [#783](https://github.com/drumkitai/vulcan/pull/783) [`f8802cf`](https://github.com/drumkitai/vulcan/commit/f8802cf44733ed85fc25c41cf6cfd6e74699b81f) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - QQ Visual refactor to hide date fields by default - Lucas

- [#786](https://github.com/drumkitai/vulcan/pull/786) [`19b4252`](https://github.com/drumkitai/vulcan/commit/19b4252e82667eb84423f196dd40feb1c3b480c1) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Customer input for carrier quote for metrics - Winston

- [#781](https://github.com/drumkitai/vulcan/pull/781) [`cf73f34`](https://github.com/drumkitai/vulcan/commit/cf73f3445b43fea02242a3c001a42b7114f6e609) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Don't pass in empty thread ID to API routes that require it to be a nonempty value. -- boogs

- [#785](https://github.com/drumkitai/vulcan/pull/785) [`93061ba`](https://github.com/drumkitai/vulcan/commit/93061baa3a1fbc9db8889fd2585572a52033fafa) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Auto-update Alexandria from eng-3200-have-discontinuous-line-charts-for-qq-for-null-data-instead -- LUCAS

## 0.35.1

### Patch Changes

- [#774](https://github.com/drumkitai/vulcan/pull/774) [`68e6bff`](https://github.com/drumkitai/vulcan/commit/68e6bffc2234f2fc62ecb2d8645b527b1b13d9ba) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Here I've updated our posting quotes to services routes to no longer require emailIDs as the gsquote route was breaking due to this. - SAM

- [#779](https://github.com/drumkitai/vulcan/pull/779) [`7f3f814`](https://github.com/drumkitai/vulcan/commit/7f3f81496de0cfe3a55c4ef2e6ce4039bfa96260) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Applying QQ Suggestions automatically when Quote tab is opened - Lucas

- [#776](https://github.com/drumkitai/vulcan/pull/776) [`968def8`](https://github.com/drumkitai/vulcan/commit/968def83bd08090645d0edb930da8735ee4306c7) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Fixing Turvo appointment timezone issues when displaying Loads - Devin

- [#775](https://github.com/drumkitai/vulcan/pull/775) [`e58d509`](https://github.com/drumkitai/vulcan/commit/e58d509455fecf170bb92b61d38366ed5788568f) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Here I've removed the Gorm model from within the request body so load submissions can be properly parsed on the backend. - SAM

- [#780](https://github.com/drumkitai/vulcan/pull/780) [`f6d703c`](https://github.com/drumkitai/vulcan/commit/f6d703cb3609c332649d1b3048b57b0431ca638b) Thanks [@Sophie1142](https://github.com/Sophie1142)! - Add RXO to Vulcan manifest for Fetch - Sophie

- [#772](https://github.com/drumkitai/vulcan/pull/772) [`2a5af20`](https://github.com/drumkitai/vulcan/commit/2a5af20048e06075ab288357d17409a26d643cba) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Here I've changed newlines to `<br>` tags in the QQ draft response since the mailClient expects/handles HTML. - Sam

- [#777](https://github.com/drumkitai/vulcan/pull/777) [`ea6c4e6`](https://github.com/drumkitai/vulcan/commit/ea6c4e6448603afae2918740eccdee16500ccdec) Thanks [@lsouza4](https://github.com/lsouza4)! - Rename config for disabling local email ingestion - Lucas

- [#778](https://github.com/drumkitai/vulcan/pull/778) [`675379b`](https://github.com/drumkitai/vulcan/commit/675379b2dc26f6bfaacce6e8ea39e803089f9e2d) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Adding fuel estimate/customer linehaul to QQ calculator - Lucas

## 0.35.0

### Minor Changes

- [#771](https://github.com/drumkitai/vulcan/pull/771) [`62228f8`](https://github.com/drumkitai/vulcan/commit/62228f8853342d66ba50096582406ab0cb1a5aef) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Remove Antd timepicker everywhere - Devin

  Refactor "Review responses" section Carrier Quoting and finish E2E implementation -- Sophie & Winston

### Patch Changes

- [#764](https://github.com/drumkitai/vulcan/pull/764) [`b9c6728`](https://github.com/drumkitai/vulcan/commit/b9c6728780e318f52db7b0725fde2b96a03255a8) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Here I removed the platform and feature flag constraint from fetching customers in the QQ form. - SAM
  Auto-update Alexandria from FIX/fetch-customers-in-QQ-form

- [#768](https://github.com/drumkitai/vulcan/pull/768) [`899897c`](https://github.com/drumkitai/vulcan/commit/899897c7ddc3e02db86a91c1507b4b1b336d2260) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Here I added a copy button to the draft response text area. This is considered a final action in the QQ flow. - SAM

- [#767](https://github.com/drumkitai/vulcan/pull/767) [`6fe7ee1`](https://github.com/drumkitai/vulcan/commit/6fe7ee192dea645a261843a9e6bf5fa6daa6a7e6) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Fix TruckStop logo size and missing Mcleod logo in Quick Quote - Sophie

- [#766](https://github.com/drumkitai/vulcan/pull/766) [`ecd2d0e`](https://github.com/drumkitai/vulcan/commit/ecd2d0eae7c2f624bbfd62359743aad62cf2fd55) Thanks [@Winston-Hsiao](https://github.com/Winston-Hsiao)! - Only render load search bar when on load view, no longer show on quote view - Winston

## 0.34.9

### Patch Changes

- [#761](https://github.com/drumkitai/vulcan/pull/761) [`6131623`](https://github.com/drumkitai/vulcan/commit/613162389b95736979553a8fae81edfb117c57aa) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Preventing errors when fetching QQ/LB suggestions for invalid thread ID - Lucas

- [#763](https://github.com/drumkitai/vulcan/pull/763) [`079d5bc`](https://github.com/drumkitai/vulcan/commit/079d5bc656882dfefd10417632aae5c3e7f90257) Thanks [@DrumkitBot](https://github.com/DrumkitBot)! - Preventing errors when fetching LB suggestions for invalid thread ID - Lucas

## 0.34.8

### Patch Changes

- [#758](https://github.com/drumkitai/vulcan/pull/758) [`0b7e566`](https://github.com/drumkitai/vulcan/commit/0b7e5661f1a5374e3bf4ccdc423fcc50c243f9eb) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - This removes timepicker to replace time selection dropdowns - Devin

- [#757](https://github.com/drumkitai/vulcan/pull/757) [`20a5da7`](https://github.com/drumkitai/vulcan/commit/20a5da78969bee37535e30c4543fbb120fef8f49) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Carrier location empty state and other UI/UX improvements - Winston

- [#759](https://github.com/drumkitai/vulcan/pull/759) [`6a6b7f1`](https://github.com/drumkitai/vulcan/commit/6a6b7f1c5607552fd6523b094d82407fa48dd89e) Thanks [@dhightnm](https://github.com/dhightnm)! - This adds robust parsing for addresses and phonenumbers - Devin

- [#751](https://github.com/drumkitai/vulcan/pull/751) [`d922d51`](https://github.com/drumkitai/vulcan/commit/d922d51ef577ea3e0234831d5d85f45c9d368b32) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Enable OpenDock for Fetch Freight, and feature flag to hide appointment email form - Sophie

- [#753](https://github.com/drumkitai/vulcan/pull/753) [`11c0501`](https://github.com/drumkitai/vulcan/commit/11c05011384a568e8ad1f09a9bc50adea95bb892) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fix UTC time in truck loads -- Devin

- [#755](https://github.com/drumkitai/vulcan/pull/755) [`6d9fe19`](https://github.com/drumkitai/vulcan/commit/6d9fe19fac2e91fa3733a5342afb892d86feeadc) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Here I've set some tms integration conditionals in the QQ form to allow services to use QQ without a tms integration. - SAM

- [#754](https://github.com/drumkitai/vulcan/pull/754) [`a1278fc`](https://github.com/drumkitai/vulcan/commit/a1278fc4c138db53e5f274b3619a425f174d52e1) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Here I have enhanced our quote request data capturing logic by adding association logic between quote requests and quotes/loads. I've also added logic to make sure we always have a quote request if the user gets a quick quote, and we now have a customer field in the QQ form. - SAM

- [#756](https://github.com/drumkitai/vulcan/pull/756) [`98f9755`](https://github.com/drumkitai/vulcan/commit/98f975558c3e0873ee1871e1e01078cebb80c2d6) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Always look for quote request suggestions, similar to load building -- Sophie

## 0.34.7

### Patch Changes

- [#747](https://github.com/drumkitai/vulcan/pull/747) [`6f2d7dd`](https://github.com/drumkitai/vulcan/commit/6f2d7dde163f8754bdc080688757b0ec65e53ddb) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - CarrierQuote refresh locations button - Winston

## 0.34.6

### Patch Changes

- [#745](https://github.com/drumkitai/vulcan/pull/745) [`49c4558`](https://github.com/drumkitai/vulcan/commit/49c4558c69db0c29c709b367719189df9ea3f723) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Reduce Sentry noise - Sophie

## 0.34.5

### Patch Changes

- [#743](https://github.com/drumkitai/vulcan/pull/743) [`7aaa851`](https://github.com/drumkitai/vulcan/commit/7aaa851882c714dcec1f8c97ab278baa9ad10213) Thanks [@Sophie1142](https://github.com/Sophie1142)! - Update Alexandria pointer to include Load vs. Quote view fixes - Sophie

## 0.34.4

### Patch Changes

- [#737](https://github.com/drumkitai/vulcan/pull/737) [`cb3dd86`](https://github.com/drumkitai/vulcan/commit/cb3dd8629e728175af5424062ee744e510b5b81d) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Here I added a fallback copy method to the copyToClipboard functionality in the CarrierPriceCalculator. - SAM

- [#739](https://github.com/drumkitai/vulcan/pull/739) [`3b4bb6a`](https://github.com/drumkitai/vulcan/commit/3b4bb6a016075fede9c64cc0f25a3bc857786674) Thanks [@lsouza4](https://github.com/lsouza4)! - Fixing blank sidepanel for logged out users - Lucas

- [#740](https://github.com/drumkitai/vulcan/pull/740) [`1a07897`](https://github.com/drumkitai/vulcan/commit/1a078975d80aacfe0e8e4c99b1aa0ccf5ac27cef) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fix CarrierQuote subject line location population - Winston

- [#741](https://github.com/drumkitai/vulcan/pull/741) [`d96e8dd`](https://github.com/drumkitai/vulcan/commit/d96e8dd07668c555b21514d4841a3d29eae42e58) Thanks [@Sophie1142](https://github.com/Sophie1142)! - Fix SDS load view & CQ by adding isQuoteView flag -- Sophie

## 0.34.3

### Patch Changes

- [#734](https://github.com/drumkitai/vulcan/pull/734) [`2bf2f43`](https://github.com/drumkitai/vulcan/commit/2bf2f43e66c4c5abb36cf66bd2fb14681dd420fd) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - CQ Form is now 2 separate forms and updated carrier card UI - Winston

## 0.34.2

### Patch Changes

- [#729](https://github.com/drumkitai/vulcan/pull/729) [`cb8cf84`](https://github.com/drumkitai/vulcan/commit/cb8cf840e17dc2d71b8f30cc5a948fdaac18944a) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fixed the advanced search popover. - SAM

- [#723](https://github.com/drumkitai/vulcan/pull/723) [`8685d1a`](https://github.com/drumkitai/vulcan/commit/8685d1aa41e99894d3ad0576395d462f3ebcdee6) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - multi-email support for carrier quote locations - Winston

- [#732](https://github.com/drumkitai/vulcan/pull/732) [`ee17722`](https://github.com/drumkitai/vulcan/commit/ee177222da70ac45c49ca92c70e0c4285cb54490) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Here we've updated the getQuickQuote functionality to support the API refactor for QQ. - SAM

- [#731](https://github.com/drumkitai/vulcan/pull/731) [`0676200`](https://github.com/drumkitai/vulcan/commit/06762004c2a95238d5e4591a7498c2894ea53dcb) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Quote View initial tab priority order and add CarrierQuote - Winston

- [#725](https://github.com/drumkitai/vulcan/pull/725) [`cb0c848`](https://github.com/drumkitai/vulcan/commit/cb0c8486dd94a287166b7c56e55658ad6f4fb51f) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Collapse date inputs for FindCarrier in CarrierQuote - Winston

- [#728](https://github.com/drumkitai/vulcan/pull/728) [`9020293`](https://github.com/drumkitai/vulcan/commit/9020293fc6fdaee25f17c6c4f1315296581b2e4b) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - CarrierCard UI Update - Winston

- [#730](https://github.com/drumkitai/vulcan/pull/730) [`ecdefb6`](https://github.com/drumkitai/vulcan/commit/ecdefb6f790375408dc7e02bd8c9eb1c1f9cd9c5) Thanks [@Sophie1142](https://github.com/Sophie1142)! - Add FreightView and Shipwell to manifest for Fetch Freight - Sophie

- [#727](https://github.com/drumkitai/vulcan/pull/727) [`d5a9f08`](https://github.com/drumkitai/vulcan/commit/d5a9f0867f733c5862546c96b05ec02559c4df10) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Removing Lane History timeout retry - Lucas

## 0.34.1

### Patch Changes

- [#719](https://github.com/drumkitai/vulcan/pull/719) [`219f27b`](https://github.com/drumkitai/vulcan/commit/219f27bf0da9655e18c7f191b9b0d4ff3dd62a68) Thanks [@lsouza4](https://github.com/lsouza4)! - Fixing issue with duplicated Drumkit instance for Aljex - Lucas

- [#720](https://github.com/drumkitai/vulcan/pull/720) [`b6ab89a`](https://github.com/drumkitai/vulcan/commit/b6ab89ab9b9845c16c11badab4aec9d7b1e8249d) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - The log out button only redirected the user to the Drumkit Portal but didn't sign them out there nor the sidebar. The button action has been fixed now. -- boogs

- [#713](https://github.com/drumkitai/vulcan/pull/713) [`0bc246f`](https://github.com/drumkitai/vulcan/commit/0bc246f772d4f08db705a0806fff9eb44233b43b) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fixing button CSS inside blue box to enable DAT - Lucas

- [#717](https://github.com/drumkitai/vulcan/pull/717) [`bfb9cdf`](https://github.com/drumkitai/vulcan/commit/bfb9cdf723e190b189c1313c218deb4aaad927c4) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Updating favorite loads folder icon - Lucas

- [#716](https://github.com/drumkitai/vulcan/pull/716) [`f4870c8`](https://github.com/drumkitai/vulcan/commit/f4870c8df6830033933a2fa83fad4a8e1569918b) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Don't make dropoff date a required field in the Quick Quote form. -- boogs

- [#721](https://github.com/drumkitai/vulcan/pull/721) [`3767632`](https://github.com/drumkitai/vulcan/commit/3767632ed172772bbbe0b833a33cd94e3ef3a4c8) Thanks [@Sophie1142](https://github.com/Sophie1142)! - Hotfix blank screen for NFI on Relay - Sophie

- [#718](https://github.com/drumkitai/vulcan/pull/718) [`6616025`](https://github.com/drumkitai/vulcan/commit/6616025b881b55844eb114eb5d8edf6d011d458c) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - White Glove updates for SDS - Winston

## 0.34.0

### Minor Changes

- [#710](https://github.com/drumkitai/vulcan/pull/710) [`630bb1a`](https://github.com/drumkitai/vulcan/commit/630bb1a3dbe6d86aea6a0b1024f60b0d27381d00) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Carrier Quote/White Glove v1 for SDS - Winston

## 0.33.0

### Minor Changes

- [#709](https://github.com/drumkitai/vulcan/pull/709) [`899406e`](https://github.com/drumkitai/vulcan/commit/899406ea00b7b4e7f35575d2a3ef19986f3b589c) Thanks [@lsouza4](https://github.com/lsouza4)! - Allow sidepanel to run on multiple domains - Lucas

- [#699](https://github.com/drumkitai/vulcan/pull/699) [`1fd0f68`](https://github.com/drumkitai/vulcan/commit/1fd0f68ad2177e1d57bc528d07124898fe44a9c1) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Make Quick Quote draft reply box editable - Sophie

### Patch Changes

- [#696](https://github.com/drumkitai/vulcan/pull/696) [`b873e76`](https://github.com/drumkitai/vulcan/commit/b873e767430a642814609e1a52f7a6706160c2b5) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Improving usage of rounded prices for Quotes - Lucas

- [#698](https://github.com/drumkitai/vulcan/pull/698) [`17148ab`](https://github.com/drumkitai/vulcan/commit/17148abced434f61f235c2efe453632b085babfa) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - DAT and GlobalTranz for Trifecta - Lucas

- [#702](https://github.com/drumkitai/vulcan/pull/702) [`6221409`](https://github.com/drumkitai/vulcan/commit/62214097836bf3c07cfe49a80cf53f147d018b9f) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Here I have added support for the no new or updated customers/locations error. -SAM

- [#703](https://github.com/drumkitai/vulcan/pull/703) [`c3ed528`](https://github.com/drumkitai/vulcan/commit/c3ed52886d65893a43383486afac3c9aa42e8943) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Adding feature flag to show/hide Load View icon - Lucas

- [#701](https://github.com/drumkitai/vulcan/pull/701) [`5f27899`](https://github.com/drumkitai/vulcan/commit/5f278995c032f4ec1a6795f84b55e4af94bfd4e4) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Adding retry for Lane History - Lucas

- [#692](https://github.com/drumkitai/vulcan/pull/692) [`57d411e`](https://github.com/drumkitai/vulcan/commit/57d411e3f66ccc8bf455f79d23f7a1591401ce69) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Adding GlobalTranz carrier card - Lucas

- [#704](https://github.com/drumkitai/vulcan/pull/704) [`b4ed2e8`](https://github.com/drumkitai/vulcan/commit/b4ed2e8da2be78694eb7095c967f93bd164f3dfb) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Add Retalix live appointment functionality including open slots retrieval. -- boogs

## 0.32.0

### Minor Changes

- [#688](https://github.com/drumkitai/vulcan/pull/688) [`a39b4c9`](https://github.com/drumkitai/vulcan/commit/a39b4c9d90aa2e7847a883720855b90448ee72e1) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Feat: Add Mcleod TMS quote lane history - Sophie

### Patch Changes

- [#691](https://github.com/drumkitai/vulcan/pull/691) [`a39fb77`](https://github.com/drumkitai/vulcan/commit/a39fb7793ea7c3e956a7cae87b8e586ce7c06adf) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fixing quote chart max value - Lucas

- [#690](https://github.com/drumkitai/vulcan/pull/690) [`e5b597e`](https://github.com/drumkitai/vulcan/commit/e5b597e7ec1dc1c3cddd6956217bee03752da51b) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Reduce filename font size in suggestion card - Sophie

## 0.31.3

### Patch Changes

- [#685](https://github.com/drumkitai/vulcan/pull/685) [`f95699d`](https://github.com/drumkitai/vulcan/commit/f95699d1dfb30c81004e414bec2f7a6aa5ce5af5) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Track Drumkit version on PostHog for Vulcan

- [#671](https://github.com/drumkitai/vulcan/pull/671) [`8a31265`](https://github.com/drumkitai/vulcan/commit/8a312657f06fb8deb7d8f09234d4e0dcb297a4b2) Thanks [@lsouza4](https://github.com/lsouza4)! - Removing Amplitude from Drumkit - Lucas

- [#682](https://github.com/drumkitai/vulcan/pull/682) [`a7a2e54`](https://github.com/drumkitai/vulcan/commit/a7a2e5400bffbfcb244f01b7c332bf014bbcce53) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fixing Advanced Search clipping issue - Lucas

## 0.31.2

### Patch Changes

- [#676](https://github.com/drumkitai/vulcan/pull/676) [`0870bba`](https://github.com/drumkitai/vulcan/commit/0870bba71406eef7403dff276b02d02787775ebd) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Here I have updated how quick quote suggestions are applied to handle the location input. I also updated the getLaneRateFromService functions to properly parse the location input. - SAM

- [#680](https://github.com/drumkitai/vulcan/pull/680) [`f3fb1cb`](https://github.com/drumkitai/vulcan/commit/f3fb1cb9d132e87e90757ef2a9a4818757d9cef1) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - update advanced search order and font appearance - JIN

- [#678](https://github.com/drumkitai/vulcan/pull/678) [`4fbb6c8`](https://github.com/drumkitai/vulcan/commit/4fbb6c83cb5685db7deb5c25e0f19144477d1fa6) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Here I've updated the Stop form UI for the TurvoLoadBuilding form so we show the name in the search field. - SAM

- [#679](https://github.com/drumkitai/vulcan/pull/679) [`064dc4e`](https://github.com/drumkitai/vulcan/commit/064dc4e71cd879a09af6acbac40806a9038c1224) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Here I aligned the debounce inputs to the start instead of the end. This fixes the misalignment-on-error issue. - SAM

## 0.31.1

### Patch Changes

- [#656](https://github.com/drumkitai/vulcan/pull/656) [`1fe23bd`](https://github.com/drumkitai/vulcan/commit/1fe23bdc4a1d148ff98fd8939015439c1bf40c56) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Here I added the inFlight status and updated quote requests (suggestions) accordingly. The aim here is to remove suggestions from the carousel only at the end of a successful quote submission, regardless of it the user leaves the email and returns later. - SAM

- [#659](https://github.com/drumkitai/vulcan/pull/659) [`aa9342d`](https://github.com/drumkitai/vulcan/commit/aa9342d59fa19561748d86c6c856312899f54568) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Addressing issues from Trident QQ 01/15/25 walkthrough - Lucas

- [#675](https://github.com/drumkitai/vulcan/pull/675) [`d944403`](https://github.com/drumkitai/vulcan/commit/d944403c862338ce763f6cb92a50831187aaae86) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Here I have added a few fields for building loads with Turvo. I've also tested the load creation with AI as well - SAM

- [#674](https://github.com/drumkitai/vulcan/pull/674) [`0022467`](https://github.com/drumkitai/vulcan/commit/00224670016a20fd7d830c395fea5ac6d7d93d8f) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Here I have made the zip and city state inputs into a single input - SAM

- [#664](https://github.com/drumkitai/vulcan/pull/664) [`54ab01e`](https://github.com/drumkitai/vulcan/commit/54ab01edd1f84815636c7203f15d4b0b8c6a31e4) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Improve tracking for truck list actions - Lucas

- [#662](https://github.com/drumkitai/vulcan/pull/662) [`04bde99`](https://github.com/drumkitai/vulcan/commit/04bde99f44db557b0ace1d300c759fcac134ee32) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Improvements on wording for the DAT carrier card - Lucas

- [#660](https://github.com/drumkitai/vulcan/pull/660) [`798c1be`](https://github.com/drumkitai/vulcan/commit/798c1be3fa36a38827fc69302732eb35c9fe68f8) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Here I have created the turvo load building form. -SAM

- [#668](https://github.com/drumkitai/vulcan/pull/668) [`f196301`](https://github.com/drumkitai/vulcan/commit/f196301553677ead76e7c307e64e7481c413a5fa) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Revisiting conditions to show both Load and Quote View buttons - Lucas

- [#654](https://github.com/drumkitai/vulcan/pull/654) [`9ef7c87`](https://github.com/drumkitai/vulcan/commit/9ef7c879ce1f3d25824bc47da55659697b15761d) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Hyperlink submission returned false positive due to response being 200 but html response showing an error. That is handled now. - SAM

- [#658](https://github.com/drumkitai/vulcan/pull/658) [`e18f530`](https://github.com/drumkitai/vulcan/commit/e18f53031b7a49ee1857dbf602aabcdd84a9f7a5) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Trident DAT metadata + updating data sent to BE - Lucas

- [#661](https://github.com/drumkitai/vulcan/pull/661) [`dad04bf`](https://github.com/drumkitai/vulcan/commit/dad04bf3ab98f7d74c3476f239bde7b679e37abb) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fixing order of suggestion cards for QQ and LB - Lucas

- [#657](https://github.com/drumkitai/vulcan/pull/657) [`ce8753f`](https://github.com/drumkitai/vulcan/commit/ce8753f5bb49d5687f6e9ee3221778238b146c40) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Here I remove special from the transport dropdown and replace the notes to the user about special equipment. - SAM

- [#634](https://github.com/drumkitai/vulcan/pull/634) [`4b8f430`](https://github.com/drumkitai/vulcan/commit/4b8f4300c499e7be5cce3423b51002382411d057) Thanks [@SamuelRCrider](https://github.com/SamuelRCrider)! - Here I have added logic to block quote suggestions that require special equipment - SAM

- [#667](https://github.com/drumkitai/vulcan/pull/667) [`5e1ebbd`](https://github.com/drumkitai/vulcan/commit/5e1ebbd6a05c8846a4f88938bc33cb6bb12be7a8) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Here I've updated the quick quote calculator component to handle zero and NaN values. Also I've added a toggle to handle dollar amount and percentage margins. - SAM

- [#672](https://github.com/drumkitai/vulcan/pull/672) [`09b4839`](https://github.com/drumkitai/vulcan/commit/09b4839a560578fccf1c21bb9b6debd4ff585d69) Thanks [@lsouza4](https://github.com/lsouza4)! - Fixing PostHog session replays for Vulcan - Lucas

- [#663](https://github.com/drumkitai/vulcan/pull/663) [`d9f11fb`](https://github.com/drumkitai/vulcan/commit/d9f11fb5fcd66fab069e3776fe858760061de639) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Including per-mile prices for Trident DAT - Lucas

- [#673](https://github.com/drumkitai/vulcan/pull/673) [`0650ffb`](https://github.com/drumkitai/vulcan/commit/0650ffbb51ee4b826e16ef55205e7334def10944) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Updating PostHog events - Lucas

- [#666](https://github.com/drumkitai/vulcan/pull/666) [`6c16bbd`](https://github.com/drumkitai/vulcan/commit/6c16bbd30441922ef7e26d56fba23ab4a583eb36) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Adding per-mile price for Greenscreens - Lucas

## 0.31.0

### Minor Changes

- [#637](https://github.com/drumkitai/vulcan/pull/637) [`71e2742`](https://github.com/drumkitai/vulcan/commit/71e2742ace621574da9f26d7e963e87410cb7dbf) Thanks [@dhruv4](https://github.com/dhruv4)! - Search bar and suggestion updates

### Patch Changes

- [#640](https://github.com/drumkitai/vulcan/pull/640) [`2095ab2`](https://github.com/drumkitai/vulcan/commit/2095ab2a2e4db1257720c74dc24d7f0285c59274) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fixing Trident DAT carrier information + misc improvements - Lucas

- [#647](https://github.com/drumkitai/vulcan/pull/647) [`d5aca65`](https://github.com/drumkitai/vulcan/commit/d5aca65e1b86c2022681cbd56e8e9edd018f3d8a) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Reverting Truck List release - Jin/Lucas

- [#648](https://github.com/drumkitai/vulcan/pull/648) [`77d2860`](https://github.com/drumkitai/vulcan/commit/77d2860df444eade9060c9b129d598e6b7e4aba9) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - UI improvements -- fix double colon, truck list buttons -- Sophie

- [#652](https://github.com/drumkitai/vulcan/pull/652) [`b75d8b9`](https://github.com/drumkitai/vulcan/commit/b75d8b9d1886eaf242aedd40ecb4b234b17ee706) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Setting truck list tab by default if no QQ or LB - Lucas

- [#641](https://github.com/drumkitai/vulcan/pull/641) [`baea842`](https://github.com/drumkitai/vulcan/commit/baea84238e69c3c0b9997326f714a1c29fd1f9b0) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Refactoring Truck List UX - Lucas

- [#642](https://github.com/drumkitai/vulcan/pull/642) [`1684fb0`](https://github.com/drumkitai/vulcan/commit/1684fb0db6aadc60562d67892747df283eaae1f2) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Advanced search improvements -- Sophie & Lucas

- [#645](https://github.com/drumkitai/vulcan/pull/645) [`d038d91`](https://github.com/drumkitai/vulcan/commit/d038d9157a3ea6063af9493c1626607e3bc4e151) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Changing zip code placeholder to a more intuitive one (12345) - Lucas

- [#651](https://github.com/drumkitai/vulcan/pull/651) [`186c1f6`](https://github.com/drumkitai/vulcan/commit/186c1f61283cf28d7b888f422c41345ac11ed58a) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Add `needsReauthorization` and `customPortalDomain` fields to `Email` type. -- boogs

- [#650](https://github.com/drumkitai/vulcan/pull/650) [`54623af`](https://github.com/drumkitai/vulcan/commit/54623aff3f27bdd2b913e3d2ea66b21b75698bce) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Unreverting Truck List UI refactor - Lucas

- [#639](https://github.com/drumkitai/vulcan/pull/639) [`3aca0e3`](https://github.com/drumkitai/vulcan/commit/3aca0e3da037d7fbdf0abfa1a349f1dcd469f148) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - QQ Refactor to show Truckstop Quotes and Risk Factors - Dhruv/Lucas

- [#643](https://github.com/drumkitai/vulcan/pull/643) [`e87704a`](https://github.com/drumkitai/vulcan/commit/e87704ae32ac3ed6771dfe231b9a4558fda6f092) Thanks [@lsouza4](https://github.com/lsouza4)! - Fixing duplicated border for Select component - Lucas

- [#644](https://github.com/drumkitai/vulcan/pull/644) [`3c58a1a`](https://github.com/drumkitai/vulcan/commit/3c58a1a732b87e305a81600e3ed724c39acaae3f) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Addressing bugs on Truck List refactored UI - Lucas

- [#653](https://github.com/drumkitai/vulcan/pull/653) [`eb6e8a5`](https://github.com/drumkitai/vulcan/commit/eb6e8a53941b4deacde11a06883ed2dc4a9a287d) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Do null checks on dropdown values for appointment scheduling. -- boogs and sophie

## 0.30.0

### Minor Changes

- [#631](https://github.com/drumkitai/vulcan/pull/631) [`f281cd7`](https://github.com/drumkitai/vulcan/commit/f281cd7f6b7125fa158d06272d3df9526e83a04b) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Refactor Quick Quote and Load Building suggestion cards - Sophie

### Patch Changes

- [#629](https://github.com/drumkitai/vulcan/pull/629) [`d798aae`](https://github.com/drumkitai/vulcan/commit/d798aae77095e782eb7e43245ca1ea03e32346f3) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fixing warehouse select not showing correct input value on load - Lucas

- [#630](https://github.com/drumkitai/vulcan/pull/630) [`275222b`](https://github.com/drumkitai/vulcan/commit/275222b5d95242d52b93a29115863b5b1d015cd7) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fix LB & QQ suggestions being removed when switching emails within thread - Sophie

- [#636](https://github.com/drumkitai/vulcan/pull/636) [`3f5a8ee`](https://github.com/drumkitai/vulcan/commit/3f5a8eebb38ced44b5c53d141d40f5449fb2886f) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Load View icon should be hidden only for Redwood - Lucas

- [#633](https://github.com/drumkitai/vulcan/pull/633) [`d2640ec`](https://github.com/drumkitai/vulcan/commit/d2640ec52cbac30d60c0f9c33c9356a2da557142) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fixing resizable search bar, renaming PO # and fixing label tags - Lucas

- [#627](https://github.com/drumkitai/vulcan/pull/627) [`d7deecb`](https://github.com/drumkitai/vulcan/commit/d7deecb325a3d63bf8d7bb524bf7a5b53f453e66) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Pass in the trailer type to the appointment creation request body. -- boogs

- [#632](https://github.com/drumkitai/vulcan/pull/632) [`9f51094`](https://github.com/drumkitai/vulcan/commit/9f51094835421c6c1cd84c531f91ee046610efd2) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Here I have refactored the carrier quoting options of the quick quote form. I've also added a route to fetch lane rates from specific services. - SAM

- [#635](https://github.com/drumkitai/vulcan/pull/635) [`2f8460e`](https://github.com/drumkitai/vulcan/commit/2f8460ef6c6ba9c55d0b6b8c092ec0f296aa7ce8) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fix the incorrect slot's warehouse timezone reference. It's `slot.warehouse.warehouseTimezone` and not `slot.warehouse.timezone`, which is undefined. -- boogs

## 0.29.2

### Patch Changes

- [#625](https://github.com/drumkitai/vulcan/pull/625) [`e896f6c`](https://github.com/drumkitai/vulcan/commit/e896f6ca649404ebda0a2c152f393bd1c38a1762) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fix timezone conversion of appointment times when daylight savings is in effect. -- boogs

## 0.29.1

### Patch Changes

- [#623](https://github.com/drumkitai/vulcan/pull/623) [`f385e13`](https://github.com/drumkitai/vulcan/commit/f385e13b19b227c98d7e4bca7c0cbebbeffcc5db) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Addressing FE Improvements quick issues before release - Lucas

## 0.29.0

### Minor Changes

- [#621](https://github.com/drumkitai/vulcan/pull/621) [`6298aa5`](https://github.com/drumkitai/vulcan/commit/6298aa5c7fd1ada511ef27ace6af5b097b2a1a94) Thanks [@lsouza4](https://github.com/lsouza4)! - Visual improvements for forms throughout the frontend - Lucas

### Patch Changes

- [#617](https://github.com/drumkitai/vulcan/pull/617) [`fdfa866`](https://github.com/drumkitai/vulcan/commit/fdfa866e58f3f3dd3d4e39749af0a73cafe9b0f5) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fixing 'flat' package version due to cjs conflict - Lucas

- [#622](https://github.com/drumkitai/vulcan/pull/622) [`e1cf9d3`](https://github.com/drumkitai/vulcan/commit/e1cf9d3d54e11fb8dc440caf265e4debabcd4723) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fix the incorrect warehouse ID reference needed to create appointments. -- boogs

- [#618](https://github.com/drumkitai/vulcan/pull/618) [`ae691de`](https://github.com/drumkitai/vulcan/commit/ae691de0714cb6f46a0ffc9dd5fc03f206ad207a) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Here I have moved removing suggestions to the very end of the quick quote flows - SAM

- [#615](https://github.com/drumkitai/vulcan/pull/615) [`75f2bd3`](https://github.com/drumkitai/vulcan/commit/75f2bd360c9a7b7c3d717b4ac90ca29113ef3711) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Truck List refactor v2: Error handling - Lucas

## 0.28.0

### Minor Changes

- [#599](https://github.com/drumkitai/vulcan/pull/599) [`f0db3e1`](https://github.com/drumkitai/vulcan/commit/f0db3e1610fad23144760aeafe5698e875e10a53) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Truck List form refactor - Lucas

### Patch Changes

- [#593](https://github.com/drumkitai/vulcan/pull/593) [`facbd53`](https://github.com/drumkitai/vulcan/commit/facbd5322aca2771a5d1cfaddad61c1c5bcb28d9) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Here I changed the customer lookup to start at 2+ char and the location lookup to start at 3+ char - SAM

- [#596](https://github.com/drumkitai/vulcan/pull/596) [`cae338f`](https://github.com/drumkitai/vulcan/commit/cae338f95370cb1838448c04a90a6f5d86361930) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Here I show the user a custom error message if GS returns quotes with low confidence. - SAM

- [#595](https://github.com/drumkitai/vulcan/pull/595) [`9046222`](https://github.com/drumkitai/vulcan/commit/90462229083f6ac15b387a45198d1a1ee3705aab) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Here I have fixed the broken autoscroll and updated the error messages for the quick quote form. - SAM

- [#614](https://github.com/drumkitai/vulcan/pull/614) [`ad4a9d5`](https://github.com/drumkitai/vulcan/commit/ad4a9d50e4ba49b40326131d5fa4af3cd1676960) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - associate quotes with quote suggestions - Boogs

- [#600](https://github.com/drumkitai/vulcan/pull/600) [`237b2a1`](https://github.com/drumkitai/vulcan/commit/237b2a163d21d9d73b5950e5f979020d356453ab) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Here I've reset two state variables that aren't available to the user until after they get a quick quote. - SAM

- [#605](https://github.com/drumkitai/vulcan/pull/605) [`c43a4ca`](https://github.com/drumkitai/vulcan/commit/c43a4ca57a96f84056d2b35a8e1fabdafaabc1b3) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Update quotes to store the final quote that was drafted in. - Boogs

- [#613](https://github.com/drumkitai/vulcan/pull/613) [`e837c2b`](https://github.com/drumkitai/vulcan/commit/e837c2b704b6330459999cf0b341b2fb30c8dd55) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fixing Truck List form unwanted caching - Lucas

- [#604](https://github.com/drumkitai/vulcan/pull/604) [`57ee3c6`](https://github.com/drumkitai/vulcan/commit/57ee3c634f00a09e941187c644d7b4d92a69f1ac) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Here I handle draft responses in the case that there is no quote from GS but the user's service still allows them to respond with a quote to the customer. - SAM

- [#614](https://github.com/drumkitai/vulcan/pull/614) [`ad4a9d5`](https://github.com/drumkitai/vulcan/commit/ad4a9d50e4ba49b40326131d5fa4af3cd1676960) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - associate quote with suggestion - Boogs

## 0.27.1

### Patch Changes

- [#590](https://github.com/drumkitai/vulcan/pull/590) [`4170303`](https://github.com/drumkitai/vulcan/commit/41703037db6de3da84088fb211118b5d217fc304) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Generalize appointment scheduling form and add Retalix scheduling integration.

## 0.27.0

### Minor Changes

- [#562](https://github.com/drumkitai/vulcan/pull/562) [`e96bdfc`](https://github.com/drumkitai/vulcan/commit/e96bdfc04c5dce1b18880ed1310648930d0bd978) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Proactively show required fields in input label - Sophie

- [#565](https://github.com/drumkitai/vulcan/pull/565) [`7530e44`](https://github.com/drumkitai/vulcan/commit/7530e44a0f89930fd364816666c914d0fe58d7cf) Thanks [@boogsbunny](https://github.com/boogsbunny)! - Switch to Quote View if truck list feature flag is enabled. Reset truck list suggestion if we fail to fetch a corresponding truck list suggestion.

### Patch Changes

- [#567](https://github.com/drumkitai/vulcan/pull/567) [`dc0ab7a`](https://github.com/drumkitai/vulcan/commit/dc0ab7ae03a430cf6fb31c4236fd87631643a7b5) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fixing unwanted form refreshes for Truck Lists - Lucas

- [#577](https://github.com/drumkitai/vulcan/pull/577) [`bba6f16`](https://github.com/drumkitai/vulcan/commit/bba6f16ce77a98cd7989c4f1883930ac480fac10) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fixing missing Manual Ingest and clipping input for Trucklist - Lucas

- [#559](https://github.com/drumkitai/vulcan/pull/559) [`ba3f333`](https://github.com/drumkitai/vulcan/commit/ba3f333d9c915252768d31fb0c2b658432693d7f) Thanks [@lsouza4](https://github.com/lsouza4)! - Fixing sourcemaps for Sentry - Lucas

- [#552](https://github.com/drumkitai/vulcan/pull/552) [`5a52963`](https://github.com/drumkitai/vulcan/commit/5a52963df245f83248d8a91ca2287e13fafb92fb) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Here I make the quote request suggestions route accept an array of quote requests. - Sam

- [#578](https://github.com/drumkitai/vulcan/pull/578) [`06a67b8`](https://github.com/drumkitai/vulcan/commit/06a67b817cdc271d5e2a4644af3806b75326217e) Thanks [@SamuelRCrider](https://github.com/SamuelRCrider)! - Here I am memoizing useFetchEmail email response to prevent unneeded rerenders. I am also cleaning up the initial view setting useEffect. -Sam

- [#572](https://github.com/drumkitai/vulcan/pull/572) [`a4cc52a`](https://github.com/drumkitai/vulcan/commit/a4cc52ac72b31adf8968376a613d1009bb75804a) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Here I've set a min-width for the quick quote calculator inputs so the margin input doesn't get clipped. I also changed the labels of these inputs. - Sam

- [#560](https://github.com/drumkitai/vulcan/pull/560) [`4b78ed8`](https://github.com/drumkitai/vulcan/commit/4b78ed8ea383d6a9c26bb7821af4eda3b853d8e6) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fixing Redwood form errors - Lucas

- [#588](https://github.com/drumkitai/vulcan/pull/588) [`26f86be`](https://github.com/drumkitai/vulcan/commit/26f86bebc799a12b6f41b71f150d30e436884a74) Thanks [@boogsbunny](https://github.com/boogsbunny)! - Set `capture_pageview` flag to false since we will manually capture pageviews.

- [#585](https://github.com/drumkitai/vulcan/pull/585) [`f6f19cc`](https://github.com/drumkitai/vulcan/commit/f6f19ccd297398c9f98ce5f3c6deef5f9f4e186b) Thanks [@lsouza4](https://github.com/lsouza4)! - Fixing dynamic import issue from cloned repo we built Vulcan from - Lucas

- [#568](https://github.com/drumkitai/vulcan/pull/568) [`f4e21d5`](https://github.com/drumkitai/vulcan/commit/f4e21d548059e937ae127c8a60ea59c812d42a32) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Show truck list required fields -- Sophie

- [#584](https://github.com/drumkitai/vulcan/pull/584) [`34f68ed`](https://github.com/drumkitai/vulcan/commit/34f68ed84d2c8b18342273de3da77db718470aaa) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fixing react-day-picker version, add missing shadcn styles - Lucas

- [#558](https://github.com/drumkitai/vulcan/pull/558) [`3f42c2c`](https://github.com/drumkitai/vulcan/commit/3f42c2ca72dba4cd4e31d5c511437018ca8326c7) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Correctly gating Truck List form behind feature flag - Lucas

- [#581](https://github.com/drumkitai/vulcan/pull/581) [`1d746fa`](https://github.com/drumkitai/vulcan/commit/1d746fa895e58a5d61462bb2f5cd4b2c94911d58) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Here I have properly hidden lane history if not enabled, fixed date bug in QQ, removed form clearing on submission for QQ in order to save form values for submitting quote to TMS. - Sam

- [#575](https://github.com/drumkitai/vulcan/pull/575) [`9c887d7`](https://github.com/drumkitai/vulcan/commit/9c887d7b18dcb3361aeec2d786069a47b279fa5e) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Add a button to manually ingest an email.

- [#563](https://github.com/drumkitai/vulcan/pull/563) [`02bfd9c`](https://github.com/drumkitai/vulcan/commit/02bfd9c63e5ea9e16e2d9c931081b851f819cc4e) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fix applying different load building suggestions, remove applied suggestions from carousel - Sophie

- [#571](https://github.com/drumkitai/vulcan/pull/571) [`0f315e8`](https://github.com/drumkitai/vulcan/commit/0f315e8fcd767dc9e8f37f9001e3490accde0a26) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Bringing back default dropoff and fixing unwanted carrier info refreshes for Truck Lists - Lucas

- [#555](https://github.com/drumkitai/vulcan/pull/555) [`942d34b`](https://github.com/drumkitai/vulcan/commit/942d34b5c7ae6e69b181cc9d947bd5d3992a7f68) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Allow manually creating Truck Lists - Lucas

- [#566](https://github.com/drumkitai/vulcan/pull/566) [`0214505`](https://github.com/drumkitai/vulcan/commit/021450508732a040b62f9b935e40e80f917d603e) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Here I have changed the suggestions card text on hover. - SAM

- [#587](https://github.com/drumkitai/vulcan/pull/587) [`047f1a6`](https://github.com/drumkitai/vulcan/commit/047f1a669a989c8a94771d039cd67c96530cbbdd) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Capture pageviews correctly and add more button captures for PostHog monitoring.

- [#582](https://github.com/drumkitai/vulcan/pull/582) [`5159f48`](https://github.com/drumkitai/vulcan/commit/5159f4899f3c319529e01c26d287b69fffe91fa7) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fixing Trucklist manual processing issue - Lucas

- [#564](https://github.com/drumkitai/vulcan/pull/564) [`fef4c1a`](https://github.com/drumkitai/vulcan/commit/fef4c1a720d36b6c2c71d36a3dd64895664a0d77) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Allowing Turvo Quote submissions by city/state - Lucas

- [#551](https://github.com/drumkitai/vulcan/pull/551) [`4933344`](https://github.com/drumkitai/vulcan/commit/4933344fbff2197d33b51c135496e97fecd8c9b1) Thanks [@SamuelRCrider](https://github.com/SamuelRCrider)! - Suggestions card pulled out and redesigned -Sam

- [#574](https://github.com/drumkitai/vulcan/pull/574) [`5324ea0`](https://github.com/drumkitai/vulcan/commit/5324ea080a3aebea55572a95f1b4eaca1aad9ddd) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Re-enabling manually created Truck Lists - Lucas

- [#576](https://github.com/drumkitai/vulcan/pull/576) [`f853b85`](https://github.com/drumkitai/vulcan/commit/f853b8582c75ad0ff3b9e4ef2dcdd23929a9e512) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Update manual ingest button request and name of button.

## 0.26.0

### Minor Changes

- [#538](https://github.com/drumkitai/vulcan/pull/538) [`2451eff`](https://github.com/drumkitai/vulcan/commit/2451effeb586f209d28da4efb0b42f2c5ce3feb2) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Allow Mcleod users to assign carrier via DebounceSelect (primarily for Outlook) - Sophie

### Patch Changes

- [#539](https://github.com/drumkitai/vulcan/pull/539) [`9b85046`](https://github.com/drumkitai/vulcan/commit/9b8504644826bee48560be16aa650e866eb51e6e) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Correct carrier information field references in JSON object for truck list submission.

- [#542](https://github.com/drumkitai/vulcan/pull/542) [`f58a1ab`](https://github.com/drumkitai/vulcan/commit/f58a1ab6ec6a07577a8211116d4fd91f2d192161) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fix occasionl OpenDock marshaling 500s due to floats instead of ints - Sophie

- [#540](https://github.com/drumkitai/vulcan/pull/540) [`3524a8c`](https://github.com/drumkitai/vulcan/commit/3524a8ce74f5cbf82cdfa524d7a565052388e758) Thanks [@Sophie1142](https://github.com/Sophie1142)! - Default to load view always if loads detected

- [#545](https://github.com/drumkitai/vulcan/pull/545) [`701c70e`](https://github.com/drumkitai/vulcan/commit/701c70eccdf7586dbb58dcaeda90e866f7e1e236) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fix: Improving conciseness of suggestion object filters - Lucas

- [#550](https://github.com/drumkitai/vulcan/pull/550) [`0b4a0d1`](https://github.com/drumkitai/vulcan/commit/0b4a0d1aaab44ed7ec3d8b7f43eac71edc63af83) Thanks [@lsouza4](https://github.com/lsouza4)! - Adding option to toggle sidebar visibility for Delegated Inboxes - Lucas

- [#544](https://github.com/drumkitai/vulcan/pull/544) [`f4a2808`](https://github.com/drumkitai/vulcan/commit/f4a280874f9608d3c767054d2e759fcd89b30667) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Here I set load building autoscroll to only work if clickedSuggestion is in load building pipeline. I also aligned carrier cost value with the carrier rate type. - Sam

- [#546](https://github.com/drumkitai/vulcan/pull/546) [`a314d61`](https://github.com/drumkitai/vulcan/commit/a314d6178b3ae1eaee002b06cdc51929c767ba38) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Here I put the lane history request behind the lane history service flag - Sam

- [#548](https://github.com/drumkitai/vulcan/pull/548) [`4509c1e`](https://github.com/drumkitai/vulcan/commit/4509c1ea64600988ad2d662569d250ec085284f0) Thanks [@lsouza4](https://github.com/lsouza4)! - Hotfix: updating packages and yarn.lock - Lucas

- [#543](https://github.com/drumkitai/vulcan/pull/543) [`fbb8a01`](https://github.com/drumkitai/vulcan/commit/fbb8a016d372001c93bef7aae91fbd7ca50ead5d) Thanks [@SamuelRCrider](https://github.com/SamuelRCrider)! - Here I have added a lane history graph to the quick quote tab - Sam

- [#536](https://github.com/drumkitai/vulcan/pull/536) [`873dfae`](https://github.com/drumkitai/vulcan/commit/873dfae30e49aa08516034fbd69879511fc2cba9) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fixing real-time customer search when submitting quotes to Turvo - Lucas

- [#549](https://github.com/drumkitai/vulcan/pull/549) [`0e1cacb`](https://github.com/drumkitai/vulcan/commit/0e1cacb691158df70772f8bfc309e18306d8816a) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fix Relay dispatch driver without driver's location - Sophie

## 0.25.5

### Patch Changes

- [#527](https://github.com/drumkitai/vulcan/pull/527) [`5037b07`](https://github.com/drumkitai/vulcan/commit/5037b07b784afb3c68691af58d6d0036225d9c4d) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Don't fetch objects for non-Mcleod TMS's, fix rendering of DebounceSelect - Sophie

- [#531](https://github.com/drumkitai/vulcan/pull/531) [`d664e36`](https://github.com/drumkitai/vulcan/commit/d664e363aec1f3c30f21dfe1cab2d78c89a805c8) Thanks [@jinyanzang](https://github.com/jinyanzang)! - add sourcemap for sentry - Jin

- [#532](https://github.com/drumkitai/vulcan/pull/532) [`7c15490`](https://github.com/drumkitai/vulcan/commit/7c154901637934ca821cc68cf99e48992c2a8e40) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fixing issue when destructuring Truck List errors - Lucas

- [#530](https://github.com/drumkitai/vulcan/pull/530) [`8fb4c92`](https://github.com/drumkitai/vulcan/commit/8fb4c9285c2f2768902b7a82ba62e60df621df1e) Thanks [@lsouza4](https://github.com/lsouza4)! - Fixing Posthog import between repos - Lucas

- [#529](https://github.com/drumkitai/vulcan/pull/529) [`5f382e7`](https://github.com/drumkitai/vulcan/commit/5f382e72e6a449d98227a9b5adac1dcf8ad0c1bf) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Including DC in the US State list - Lucas

- [#528](https://github.com/drumkitai/vulcan/pull/528) [`b98768c`](https://github.com/drumkitai/vulcan/commit/b98768c64e755f7c4f4ad698d82fbd8bea978e1d) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Improving error handling for Truck List form - Lucas

- [#534](https://github.com/drumkitai/vulcan/pull/534) [`599c386`](https://github.com/drumkitai/vulcan/commit/599c386207a072e0c8c7bb2093e2088eb31032b2) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Improving form validation for Quick Quote - Lucas

- [#524](https://github.com/drumkitai/vulcan/pull/524) [`20af927`](https://github.com/drumkitai/vulcan/commit/20af927de28dca991c6b6fdb43c0653756c6cfba) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - There are some minor fixes for trucklists here. Mainly we no longer cast clickedSuggestion.suggested as a type. The response type for fetching trucklists is also fixed here. - Sam

## 0.25.4

### Patch Changes

- [#513](https://github.com/drumkitai/vulcan/pull/513) [`782ed4e`](https://github.com/drumkitai/vulcan/commit/782ed4e0ea97c00f509edb57363ef42ffddf20a5) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - I mapped the truckType and collectionMethods to McLeod values. I also added Charleston to the revenue codes.

- [#508](https://github.com/drumkitai/vulcan/pull/508) [`3aaa878`](https://github.com/drumkitai/vulcan/commit/3aaa878d4021a0aab2eefc90946404ef1ea4160e) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - We persist form data in local storage using a newly written service - Sam

- [#512](https://github.com/drumkitai/vulcan/pull/512) [`d5292dd`](https://github.com/drumkitai/vulcan/commit/d5292ddb6568fbc94be33b638c69137b9418b3ee) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Added shouldDirty=true to fields that are updated by clickedSuggestions, hid AI label on mode field, added whitespace-nowrap to labels, split up MLBF.tsx, fixed some type casting bugs for the appt time fields - Sam

- [#515](https://github.com/drumkitai/vulcan/pull/515) [`acd8910`](https://github.com/drumkitai/vulcan/commit/acd89106f7ec37a631793935c36d93ede49711c1) Thanks [@lsouza4](https://github.com/lsouza4)! - Fixing Sentry initialization - Lucas

- [#511](https://github.com/drumkitai/vulcan/pull/511) [`de37c56`](https://github.com/drumkitai/vulcan/commit/de37c563d66675b4979bc225f86e4595b9cc5682) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - I deduplicated a bunch of load building types and fixed fuzzy match autocomplete - Sam

- [#520](https://github.com/drumkitai/vulcan/pull/520) [`c3126dd`](https://github.com/drumkitai/vulcan/commit/c3126dd3ab28a4f1fc44b7c380e753ef95451284) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - This will include the following: Auto-update Alexandria from trident-fixes-improvements-10-17 and Auto-update Alexandria from trident-fe-fixes. Both authored by Sophie1142. In terms of my additions, I did a hot fix to handle some potentially undefined values in the defaultValueMemo in the McleodLoadBuildingForm. - Sam

- [#521](https://github.com/drumkitai/vulcan/pull/521) [`c878208`](https://github.com/drumkitai/vulcan/commit/c878208ab11e9416ac9b520eb63ea90c6b07ffd8) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Here I have updated the sidebarWrapper useEffect to set the current view based on the email label and service flags - Sam

- [#516](https://github.com/drumkitai/vulcan/pull/516) [`b6edee1`](https://github.com/drumkitai/vulcan/commit/b6edee180fc094cd38fb41c609a23528cce9df3d) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fix stale customer list for Turvo

- [#522](https://github.com/drumkitai/vulcan/pull/522) [`7b1275c`](https://github.com/drumkitai/vulcan/commit/7b1275cbf9ab6fa2084f5fbf622c1d2115a62cc0) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - I added button types to the truckList form buttons and I also conditionally render the dropoff date fields by service id - Sam

- [#523](https://github.com/drumkitai/vulcan/pull/523) [`fd24bd5`](https://github.com/drumkitai/vulcan/commit/fd24bd5258520e311dbd3943fa0b4b4ca12b6bc3) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Few things here, first I fixed trucklistresponse type, then the res has a "carrier" field but the type was using "carrierInfo" so I corrected that, next we were fetching trucklists in the loadsidebar which is the wrong place so I moved this logic to the QuoteSidebar, finally there was a duplicate useEffect in the trucklisttab, I removed it. - Sam

## 0.25.3

### Patch Changes

- [#507](https://github.com/drumkitai/vulcan/pull/507) [`35838e8`](https://github.com/drumkitai/vulcan/commit/35838e8fd28c0a87f286628c54cc70627924c41d) Thanks [@lsouza4](https://github.com/lsouza4)! - Adding support for Delegated Inboxes in Gmail - Lucas

## 0.25.2

### Patch Changes

- [#499](https://github.com/drumkitai/vulcan/pull/499) [`3455ce9`](https://github.com/drumkitai/vulcan/commit/3455ce93fbba993495c52a71eebf2926510309b1) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fix displaying of truck lists since we moved to displaying it via the suggestions logic.

- [#497](https://github.com/drumkitai/vulcan/pull/497) [`7b2a004`](https://github.com/drumkitai/vulcan/commit/7b2a004ea2e7586c91ba82b2fa4f5d9d3a6b0cef) Thanks [@jinyanzang](https://github.com/jinyanzang)! - update warehouse search in appt scheduling - Jin

## 0.25.1

### Patch Changes

- [#494](https://github.com/drumkitai/vulcan/pull/494) [`2a6100c`](https://github.com/drumkitai/vulcan/commit/2a6100cbe2e1ae858bcf64d8e4c3b60c47c0f310) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fixing issue where Pickup and Dropoff load-matched warehouse was the same - Lucas

## 0.25.0

### Minor Changes

- [#487](https://github.com/drumkitai/vulcan/pull/487) [`48c654a`](https://github.com/drumkitai/vulcan/commit/48c654a44c72920263c15222f6c6f7d07cda7090) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Add Mcleod load building form (primarily for Outlook customer) -- Sophie

### Patch Changes

- [#489](https://github.com/drumkitai/vulcan/pull/489) [`8fa2c91`](https://github.com/drumkitai/vulcan/commit/8fa2c912a707ab2b90925444a79a4eb658f7f4c1) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Adding autocomplete for warehouse's select component - @Lucas

- [#493](https://github.com/drumkitai/vulcan/pull/493) [`0eced42`](https://github.com/drumkitai/vulcan/commit/0eced42ba72c0605aa28c30a800ff00313744824) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Adding Warehouse-to-Load association, improving warehouse select - Lucas

## 0.24.0

### Minor Changes

- [#481](https://github.com/drumkitai/vulcan/pull/481) [`c30e33d`](https://github.com/drumkitai/vulcan/commit/c30e33d68aa89e005eb3d4c104839b38ecf4be98) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Adding support to Quick Quote and Truck List Suggestions

### Patch Changes

- [#478](https://github.com/drumkitai/vulcan/pull/478) [`d1eb5e5`](https://github.com/drumkitai/vulcan/commit/d1eb5e582f5a3d4e5eb7f5eac04d253da2f9c57e) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fetch truck list errors from the backend and update error flows for carrier information.

- [#484](https://github.com/drumkitai/vulcan/pull/484) [`bf783a2`](https://github.com/drumkitai/vulcan/commit/bf783a2a159d67c5374c3d27da04fd1daac77d5c) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Including scheduler source when requesting warehouse-related data

- [#482](https://github.com/drumkitai/vulcan/pull/482) [`9f7d0f6`](https://github.com/drumkitai/vulcan/commit/9f7d0f69ea171a708b931d92a359bac5cec8fd25) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fixing default Dispatched Time for Aljex

- [#486](https://github.com/drumkitai/vulcan/pull/486) [`bcbbc5c`](https://github.com/drumkitai/vulcan/commit/bcbbc5ca5a186cb25ee7f055ece65a3033c0d93c) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Improving the select options for Warehouses

- [#483](https://github.com/drumkitai/vulcan/pull/483) [`d344af4`](https://github.com/drumkitai/vulcan/commit/d344af4e678b1bbb235509ed4cbe300c1096093a) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Return 404s when no truck lists found for email

## 0.23.1

### Patch Changes

- [#476](https://github.com/drumkitai/vulcan/pull/476) [`ee003b0`](https://github.com/drumkitai/vulcan/commit/ee003b072705c81c816217841c6f63f6aa395a64) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fixing Truck List call not gated behind Feature Flag

- [#473](https://github.com/drumkitai/vulcan/pull/473) [`eaa6ffc`](https://github.com/drumkitai/vulcan/commit/eaa6ffcf625f9f2508cf479579ef80214f151df2) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Dispatch driver when carrier info AI suggestion submitted to Relay -- Sophie

## 0.23.0

### Minor Changes

- [#388](https://github.com/drumkitai/vulcan/pull/388) [`86edb1f`](https://github.com/drumkitai/vulcan/commit/86edb1f75a7d8f28dc8dcd9ff7be0faf7712dc35) Thanks [@lsouza4](https://github.com/lsouza4)! - Suggestions Redesign

- [#459](https://github.com/drumkitai/vulcan/pull/459) [`b468014`](https://github.com/drumkitai/vulcan/commit/b46801439b287c65f3fd93c3ccf23933fb29a482) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Add load building support - Dhruv & Sophie

### Patch Changes

- [#468](https://github.com/drumkitai/vulcan/pull/468) [`f544d77`](https://github.com/drumkitai/vulcan/commit/f544d7746cd1c0d72e6d0fc5ed89972c8237b045) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fix reload extension page

- [#466](https://github.com/drumkitai/vulcan/pull/466) [`2b13232`](https://github.com/drumkitai/vulcan/commit/2b132325761fb57fa153281df3ec648354955370) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fixes & improvements to Contact Carrier section - Sophie

- [#467](https://github.com/drumkitai/vulcan/pull/467) [`e2b7db8`](https://github.com/drumkitai/vulcan/commit/e2b7db8cb73fc8e9dc95488522beb7cc0f894d56) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Normalize Relay check calls

- [#464](https://github.com/drumkitai/vulcan/pull/464) [`7abc4ba`](https://github.com/drumkitai/vulcan/commit/7abc4ba96018c7129039f7a56dbc6cd784b528a1) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fixing Quote View and Turvo Quote submission

- [#463](https://github.com/drumkitai/vulcan/pull/463) [`7c84802`](https://github.com/drumkitai/vulcan/commit/7c84802cded4fc32e5de3b478047b3a0f7234cbb) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Permit scheduling of emails with past appointment times in the Contact Carrier section.

- [#469](https://github.com/drumkitai/vulcan/pull/469) [`62a0d3c`](https://github.com/drumkitai/vulcan/commit/62a0d3c0a66fcf0e093e3036b1d635197d44d7d1) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - CSS Fixes for the Datepicker dropdown component

## 0.22.1

### Patch Changes

- [#458](https://github.com/drumkitai/vulcan/pull/458) [`cab6179`](https://github.com/drumkitai/vulcan/commit/cab6179f455e14d351708f2f36d9c96d12dab13f) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Show warnings for multi-stop loads

- [#456](https://github.com/drumkitai/vulcan/pull/456) [`963509a`](https://github.com/drumkitai/vulcan/commit/963509a911f1588173075e148c0e01441bb49e28) Thanks [@Sophie1142](https://github.com/Sophie1142)! - Fixes and improvements to Aljex side panel

## 0.22.0

### Minor Changes

- [#451](https://github.com/drumkitai/vulcan/pull/451) [`bb8dcd5`](https://github.com/drumkitai/vulcan/commit/bb8dcd58d3481f8b54193145cdee5bc89b4898f5) Thanks [@Sophie1142](https://github.com/Sophie1142)! - Adding Drumkit as a SidePanel for Relay

- [#454](https://github.com/drumkitai/vulcan/pull/454) [`c5e916b`](https://github.com/drumkitai/vulcan/commit/c5e916bd761da96dae1c3eb8722e85b2090382e3) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Add specifications section Load Info and Appt tab - Sophie

- [#453](https://github.com/drumkitai/vulcan/pull/453) [`d06f64d`](https://github.com/drumkitai/vulcan/commit/d06f64d3bcf58f86df813f5e6356a8380fbe89d7) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Autopopulate In datetime for Relay milestone check calls

### Patch Changes

- [#455](https://github.com/drumkitai/vulcan/pull/455) [`d55b4fd`](https://github.com/drumkitai/vulcan/commit/d55b4fd7e447c807165f61180a3fe2adbf75787b) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fixing tabs for Load IDs breaking line incorrectly

- [#448](https://github.com/drumkitai/vulcan/pull/448) [`213ba65`](https://github.com/drumkitai/vulcan/commit/213ba65ab218fb11944f57c6129f56c9d31b736f) Thanks [@lsouza4](https://github.com/lsouza4)! - Relay CSS Fixes - Quick Quote, Appointment Scheduling and Error Pages

- [#450](https://github.com/drumkitai/vulcan/pull/450) [`da22b59`](https://github.com/drumkitai/vulcan/commit/da22b59227987d02fc8a3bc2463f71503818c09c) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Remove emails after deletion in Outbox tab.

## 0.21.4

### Patch Changes

- [#446](https://github.com/drumkitai/vulcan/pull/446) [`3b67c92`](https://github.com/drumkitai/vulcan/commit/3b67c927a152c2c2a234046b555ad92339fa3a03) Thanks [@lsouza4](https://github.com/lsouza4)! - Implementing Feature Flag to disable Aljex

## 0.21.3

### Patch Changes

- [#442](https://github.com/drumkitai/vulcan/pull/442) [`c06ff2d`](https://github.com/drumkitai/vulcan/commit/c06ff2dc54fbb95c83d347e2c338a3cdfe71225b) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fixing issue where sidebar would always revalidate information upon focus

- [#444](https://github.com/drumkitai/vulcan/pull/444) [`35646e7`](https://github.com/drumkitai/vulcan/commit/35646e7e5c15445ff6cb54c57b5293abdec16515) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Relay CSS Fixes and removing Timepicker library imports

- [#445](https://github.com/drumkitai/vulcan/pull/445) [`4c8481a`](https://github.com/drumkitai/vulcan/commit/4c8481a7e459a02f6634f50f824121922f6aa044) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Use backend email templates and add CC field to form.

## 0.21.2

### Patch Changes

- [#440](https://github.com/drumkitai/vulcan/pull/440) [`a38a06f`](https://github.com/drumkitai/vulcan/commit/a38a06fc3386b6813a930502f8fa61943a65cd1b) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Jin - fix track and trace bugs in check call form

## 0.21.1

### Patch Changes

- [#438](https://github.com/drumkitai/vulcan/pull/438) [`27cec9b`](https://github.com/drumkitai/vulcan/commit/27cec9b59f6f364e7ff46a8eb90d9229e7775e2b) Thanks [@jinyanzang](https://github.com/jinyanzang)! - add changeset

## 0.21.0

### Minor Changes

- [#434](https://github.com/drumkitai/vulcan/pull/434) [`3fc7eff`](https://github.com/drumkitai/vulcan/commit/3fc7effd1abb4aec0d332e1cd7662f641d74aeb9) Thanks [@Sophie1142](https://github.com/Sophie1142)! - Auto-detect loads on additional Relay pages

- [#426](https://github.com/drumkitai/vulcan/pull/426) [`7961dd9`](https://github.com/drumkitai/vulcan/commit/7961dd9ba2d4036f4b426c1b5d57bce4fb83872e) Thanks [@Sophie1142](https://github.com/Sophie1142)! - Support OpenDock for Relay loads, consolidate appt scheduling views

### Patch Changes

- [#427](https://github.com/drumkitai/vulcan/pull/427) [`6e57c5a`](https://github.com/drumkitai/vulcan/commit/6e57c5abc390fdea85423764f550f2f3b524180f) Thanks [@lsouza4](https://github.com/lsouza4)! - Fixing padding for Load View on RelayTMS

- [#430](https://github.com/drumkitai/vulcan/pull/430) [`bd64552`](https://github.com/drumkitai/vulcan/commit/bd6455283bdc6e117d1da5ee76284386a44e8e6f) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Improving spacing between Header buttons

- [#432](https://github.com/drumkitai/vulcan/pull/432) [`ace34f0`](https://github.com/drumkitai/vulcan/commit/ace34f089ff3ed367b2bd971713ce6cd2b19dd43) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Re-order Aljex check call enums and show helpful message in Outbox

- [#433](https://github.com/drumkitai/vulcan/pull/433) [`ea8d60f`](https://github.com/drumkitai/vulcan/commit/ea8d60fcaf85a493387b22c63a50026bbb3b5f87) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Show link to Outbox tab after scheduling emails.

- [#429](https://github.com/drumkitai/vulcan/pull/429) [`a2d8d30`](https://github.com/drumkitai/vulcan/commit/a2d8d300cce4bbf390ff1ea3a02cd209c241da32) Thanks [@lsouza4](https://github.com/lsouza4)! - Fixing background color issue on Inputs for RelayTMS

## 0.20.2

### Patch Changes

- [#424](https://github.com/drumkitai/vulcan/pull/424) [`41a3221`](https://github.com/drumkitai/vulcan/commit/41a3221b98fad306aca2c0a29f3780e19eda1c9b) Thanks [@jinyanzang](https://github.com/jinyanzang)! - new release version

## 0.20.1

### Patch Changes

- [#419](https://github.com/drumkitai/vulcan/pull/419) [`f9c32dc`](https://github.com/drumkitai/vulcan/commit/f9c32dcd701a6a123c2492239b32dae4c98c1169) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fixing issue with warehouse default subscribed emails

- [#421](https://github.com/drumkitai/vulcan/pull/421) [`e807080`](https://github.com/drumkitai/vulcan/commit/e8070800b03b043fd6a01a06ad5dbc779b5fdb7d) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fixing bug preventing loads to load on Relay, and visual fix for AI Sparkle Icon on two-column input rows

## 0.20.0

### Minor Changes

- [#416](https://github.com/drumkitai/vulcan/pull/416) [`4d90141`](https://github.com/drumkitai/vulcan/commit/4d901417a3d3c9ab8b461978301ee209d055e4a3) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Support notes and exceptions outside of check calls for Relay

### Patch Changes

- [#410](https://github.com/drumkitai/vulcan/pull/410) [`9a41fd0`](https://github.com/drumkitai/vulcan/commit/9a41fd01f24259a5f95c8acf32c8e2e38b5995ee) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Don't show Relay multi-stop loads in sidebar

- [#414](https://github.com/drumkitai/vulcan/pull/414) [`e1a3422`](https://github.com/drumkitai/vulcan/commit/e1a3422ecd702983e9e58d740e4ca25d55b00f74) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fix duplicate PROs/POs in OpenDock form

- [#412](https://github.com/drumkitai/vulcan/pull/412) [`45a8bb2`](https://github.com/drumkitai/vulcan/commit/45a8bb278a8ea139e2b6fbe773716dded411fb11) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Support assigning operators on Relay

- [#417](https://github.com/drumkitai/vulcan/pull/417) [`90e7eec`](https://github.com/drumkitai/vulcan/commit/90e7eece4e145886b7cac217b75f8be7f0fec294) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Adding button on Opendock form to swap PRO and BOL values

## 0.19.0

### Minor Changes

- [#408](https://github.com/drumkitai/vulcan/pull/408) [`fe96267`](https://github.com/drumkitai/vulcan/commit/fe962671916ad00615604091371455493e5b492a) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Release Relay!

- [#403](https://github.com/drumkitai/vulcan/pull/403) [`c1b3438`](https://github.com/drumkitai/vulcan/commit/c1b3438a90fcd39debc494acce5b6598df204621) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Extending Quick Quote for to allow submitting Quotes to TMS

### Patch Changes

- [#405](https://github.com/drumkitai/vulcan/pull/405) [`b256b31`](https://github.com/drumkitai/vulcan/commit/b256b310b65ed4985266c4669c4f72892b2cb7b1) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Improving customer selection when submiting quotes to TMS

- [#391](https://github.com/drumkitai/vulcan/pull/391) [`7875840`](https://github.com/drumkitai/vulcan/commit/78758403a2b969f5460e01e07fc8652404cc7a29) Thanks [@lsouza4](https://github.com/lsouza4)! - Rework on Truck List View

## 0.18.2

### Patch Changes

- [#401](https://github.com/drumkitai/vulcan/pull/401) [`bb3f7b6`](https://github.com/drumkitai/vulcan/commit/bb3f7b6d803a3f58259e06dde59c2a298d21feaa) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - update env vars

## 0.18.1

### Patch Changes

- [#399](https://github.com/drumkitai/vulcan/pull/399) [`6165f0a`](https://github.com/drumkitai/vulcan/commit/6165f0aa4b8e53684c353724c9446df3dfee2263) Thanks [@dhruv4](https://github.com/dhruv4)! - update from beacon to drumkit

## 0.17.0

### Minor Changes

- [#394](https://github.com/axleapi/vulcan/pull/394) [`f218aeb`](https://github.com/axleapi/vulcan/commit/f218aebd577a6b9e3c6fd97e46dd6b28ca733101) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Add support for Relay TMS

## 0.16.5

### Patch Changes

- [#383](https://github.com/axleapi/vulcan/pull/383) [`975da68`](https://github.com/axleapi/vulcan/commit/975da68bd784a7959f2695fbcd1710a135982f2e) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Improving visual hierarchy on forms throughout Beacon

- [#381](https://github.com/axleapi/vulcan/pull/381) [`cf55ada`](https://github.com/axleapi/vulcan/commit/cf55ada00ee64b7bb9b44bf9d38a62f7d6e09749) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Add Truck List section to Quote View and gate with feature flag.

- [#384](https://github.com/axleapi/vulcan/pull/384) [`03c6c50`](https://github.com/axleapi/vulcan/commit/03c6c50e44d90df8b4c78725735491437da03338) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Show when emails would be sent out in Contact Carrier section.

- [#385](https://github.com/axleapi/vulcan/pull/385) [`2a94f82`](https://github.com/axleapi/vulcan/commit/2a94f82bad7afad03dcc2fea2a83a6b45f190f8d) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Miscellaneous Quick Quote UI/UX improvements

- [#387](https://github.com/axleapi/vulcan/pull/387) [`95c2d8a`](https://github.com/axleapi/vulcan/commit/95c2d8a67a3679a0454c0fe93a9e11ad4e2e5d45) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fix logout button being disabled by feature flag - Sophie

## 0.16.4

### Patch Changes

- [#373](https://github.com/axleapi/vulcan/pull/373) [`af91117`](https://github.com/axleapi/vulcan/commit/af91117977e9a26fddf4d46b8750e011f24d27f2) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Auto-update load with OpenDock details by adding load invalidation function to context

- [#372](https://github.com/axleapi/vulcan/pull/372) [`94b2861`](https://github.com/axleapi/vulcan/commit/94b2861c5eac474ae02b3827c03b16e13fd9564a) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fix display of timestamps in contact carrier section

- [#372](https://github.com/axleapi/vulcan/pull/372) [`94b2861`](https://github.com/axleapi/vulcan/commit/94b2861c5eac474ae02b3827c03b16e13fd9564a) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fix display of timestamps in contact carrier and outbox

## 0.16.3

### Patch Changes

- [#369](https://github.com/axleapi/vulcan/pull/369) [`41a8141`](https://github.com/axleapi/vulcan/commit/41a8141080b19f603ca453ff3256392590b649d2) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Disable rescheduling of pending emails if the date is in the past.

- [#370](https://github.com/axleapi/vulcan/pull/370) [`f512cc1`](https://github.com/axleapi/vulcan/commit/f512cc1966f893ad01771a49e8fe3e79773fe547) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Auto-detect PRO and PO custom appointment fields

- [#368](https://github.com/axleapi/vulcan/pull/368) [`e233bfd`](https://github.com/axleapi/vulcan/commit/e233bfdafe06bb22fc0677ac48a112f45e74e411) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Make milestone icons in Outbox tab gray if no corresponding pending emails found.

- [#366](https://github.com/axleapi/vulcan/pull/366) [`375df38`](https://github.com/axleapi/vulcan/commit/375df386d856b1e5ab856a5827a52ffe10f3e012) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Rename "Carrier Check-ins" section to "Contact Carrier" and move to top.

- [#371](https://github.com/axleapi/vulcan/pull/371) [`e85afd3`](https://github.com/axleapi/vulcan/commit/e85afd334b563def22f07750b40d53d2ef129425) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Disable "Save edits" button to update pending email if no edits have been made yet.

## 0.16.2

### Patch Changes

- [#363](https://github.com/axleapi/vulcan/pull/363) [`98e13d2`](https://github.com/axleapi/vulcan/commit/98e13d27274a1f5ae7f02e307ac75583aa02acfb) Thanks [@Sophie1142](https://github.com/Sophie1142)! - Remove tabs permission from manifest

- [#365](https://github.com/axleapi/vulcan/pull/365) [`e94695e`](https://github.com/axleapi/vulcan/commit/e94695e999205b348f3df66e7b979d2f93bf1713) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Invalidate loads after updates, add operator help text, and reword OpenDock labels

## 0.16.1

### Patch Changes

- [#361](https://github.com/axleapi/vulcan/pull/361) [`cdcc08d`](https://github.com/axleapi/vulcan/commit/cdcc08def08d6c557808235008031056bc89c7f7) Thanks [@Sophie1142](https://github.com/Sophie1142)! - Use fuzzy search and other bug fixes in Relay AI suggestions

- [#357](https://github.com/axleapi/vulcan/pull/357) [`97b4e96`](https://github.com/axleapi/vulcan/commit/97b4e9650d74bdb500e382109ca094d414956801) Thanks [@Sophie1142](https://github.com/Sophie1142)! - Reset OpenDock form when user opens/exits Relay modal

## 0.16.0

### Minor Changes

- [#354](https://github.com/axleapi/vulcan/pull/354) [`4c2371d`](https://github.com/axleapi/vulcan/commit/4c2371d8e563f0b6dfae802fef6186e745ab3447) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Support MD 7/11 warehouse for OpenDock

### Patch Changes

- [#356](https://github.com/axleapi/vulcan/pull/356) [`acda8d0`](https://github.com/axleapi/vulcan/commit/acda8d0fc3c0bf1af0e92f3d23223facf1fc5ce8) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - fix bug with advanced search feature flag

- [#352](https://github.com/axleapi/vulcan/pull/352) [`5c034e4`](https://github.com/axleapi/vulcan/commit/5c034e43e4fe8f634b2d269439f0c03960700861) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Adding Pickup/Dropoff appt date filters on Advanced Search

- [#352](https://github.com/axleapi/vulcan/pull/352) [`5c034e4`](https://github.com/axleapi/vulcan/commit/5c034e43e4fe8f634b2d269439f0c03960700861) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Adding Starred Loads and Viewed Loads behind feature flag

- [#355](https://github.com/axleapi/vulcan/pull/355) [`b3493e6`](https://github.com/axleapi/vulcan/commit/b3493e6a7b3928860bfa9b9acb647b14caec2453) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fixing read only state for TimePickers, hiding empty Bill To section

## 0.15.0

### Minor Changes

- [#342](https://github.com/axleapi/vulcan/pull/342) [`83afa1a`](https://github.com/axleapi/vulcan/commit/83afa1a473a0099ee61e5fbd0cf1286a8c77d088) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Add Outbox email tab for viewing and managing pending carrier emails.

### Patch Changes

- [#350](https://github.com/axleapi/vulcan/pull/350) [`7a0f00c`](https://github.com/axleapi/vulcan/commit/7a0f00c6199db6f2b6280d5e977be4f6d61cdd30) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Permit manual rescheduling of pending emails.

- [#348](https://github.com/axleapi/vulcan/pull/348) [`cb02af3`](https://github.com/axleapi/vulcan/commit/cb02af34ea86660f7cbc7ee8b2c4fed3f7dd19d8) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Adding Advanced Load Search behind feature flag

## 0.14.8

### Patch Changes

- [#340](https://github.com/axleapi/vulcan/pull/340) [`12c33ff`](https://github.com/axleapi/vulcan/commit/12c33ffbdb50846e3fada5d5d82ccd49a47be1a1) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Adding Dock field for opendock Load Types that allow it's selection

- [#339](https://github.com/axleapi/vulcan/pull/339) [`e765f39`](https://github.com/axleapi/vulcan/commit/e765f39bb0cb5c7f9f77b664597016c682466cfe) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Changing Sentry to use inboxSDK's email on authenticated_user tag

- [#337](https://github.com/axleapi/vulcan/pull/337) [`c7c32bc`](https://github.com/axleapi/vulcan/commit/c7c32bc5bad056ea76b02c93581d6d19f34b52e6) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Load sidebar refactor to prevent horizontal scrollbar

## 0.14.7

### Patch Changes

- [#333](https://github.com/axleapi/vulcan/pull/333) [`6a635bc`](https://github.com/axleapi/vulcan/commit/6a635bc1945f6f8630cb6bdfedbf04d0c313b36d) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Don't require city and state for Aljex milestone check calls

- [#335](https://github.com/axleapi/vulcan/pull/335) [`30e5aca`](https://github.com/axleapi/vulcan/commit/30e5aca645d1742c24a54d7c4f353386bee5c3f5) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Adding CCed Email as optional on appointment scheduling

- [#332](https://github.com/axleapi/vulcan/pull/332) [`1aa5f21`](https://github.com/axleapi/vulcan/commit/1aa5f21552057f49b1205e1ea5ac1bc4afdad364) Thanks [@lsouza4](https://github.com/lsouza4)! - Adding minimize button to sidebar header, moving logout button to footer

## 0.14.6

### Patch Changes

- [#331](https://github.com/axleapi/vulcan/pull/331) [`8fd4617`](https://github.com/axleapi/vulcan/commit/8fd4617557d1bce61f896d5a4b5d8577bc5376d5) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Improving appointment scheduling confirmation message

- [#324](https://github.com/axleapi/vulcan/pull/324) [`b771ffe`](https://github.com/axleapi/vulcan/commit/b771ffef427e7b83f2a1158558e6b7efe62448d2) Thanks [@lsouza4](https://github.com/lsouza4)! - Adding filter to remove unwanted InboxSDK error from Sentry

- [#329](https://github.com/axleapi/vulcan/pull/329) [`445ba1e`](https://github.com/axleapi/vulcan/commit/445ba1e9407d93403ac5d72466ea1745793edd8f) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Add separate feature flags for Carrier and Shipper SOP subsections.

- [#326](https://github.com/axleapi/vulcan/pull/326) [`6e4a4aa`](https://github.com/axleapi/vulcan/commit/6e4a4aa316a812edd56ee8e5cde95bb4e79fd56f) Thanks [@lsouza4](https://github.com/lsouza4)! - Including fix for antd's warehouse select dropdown

- [#327](https://github.com/axleapi/vulcan/pull/327) [`e994f4d`](https://github.com/axleapi/vulcan/commit/e994f4d3aaff10863d7b28f73ee6c94734220be9) Thanks [@lsouza4](https://github.com/lsouza4)! - Adding the platform beacon is being loaded on to the sidebar state

- [#330](https://github.com/axleapi/vulcan/pull/330) [`876955c`](https://github.com/axleapi/vulcan/commit/876955c060549984b64c2fc524ad914d524e48f1) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Making CTAs on speech bubbles properly redirect to the suggested tab

- [#328](https://github.com/axleapi/vulcan/pull/328) [`abba5ad`](https://github.com/axleapi/vulcan/commit/abba5ad4ab584360dc9afad5192576f1e9b03d37) Thanks [@lsouza4](https://github.com/lsouza4)! - Allowing Beacon extension icon to toggle the sidebar's visibility

## 0.14.5

### Patch Changes

- [#323](https://github.com/axleapi/vulcan/pull/323) [`cd36854`](https://github.com/axleapi/vulcan/commit/cd368544e3d0bf5627649917e044ff63b30168f2) Thanks [@lsouza4](https://github.com/lsouza4)! - Including search functionality for warehouse select

- [#321](https://github.com/axleapi/vulcan/pull/321) [`ef2ec74`](https://github.com/axleapi/vulcan/commit/ef2ec74fb15be8eb41fefdf8a1c021648940aadc) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Add support for multiple check calls

- [#322](https://github.com/axleapi/vulcan/pull/322) [`14d3c56`](https://github.com/axleapi/vulcan/commit/14d3c56079e1163f7cf1252c8530148267c9cad8) Thanks [@boogsbunny](https://github.com/boogsbunny)! - Reorder milestones of Track & Trace Carrier email drafts and add templates.

- [#319](https://github.com/axleapi/vulcan/pull/319) [`d34b5b6`](https://github.com/axleapi/vulcan/commit/d34b5b69cfebd1cfe7f3fde710866de2fbb6c5b7) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fix bug when warehouse list is empty but respective feature flag is on

- [#317](https://github.com/axleapi/vulcan/pull/317) [`7bb4e9c`](https://github.com/axleapi/vulcan/commit/7bb4e9c2415af26a202e2bfe44269fb77ccaf347) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fixing endless loop issue on Sentry for auth exceptions

## 0.14.4

### Patch Changes

- [#315](https://github.com/axleapi/vulcan/pull/315) [`9d4aa5e`](https://github.com/axleapi/vulcan/commit/9d4aa5e6d2d9c7d83df07c54ff29aa3c999e2ff3) Thanks [@jinyanzang](https://github.com/jinyanzang)! - update alexandria

## 0.14.3

### Patch Changes

- [#313](https://github.com/axleapi/vulcan/pull/313) [`d6cda5a`](https://github.com/axleapi/vulcan/commit/d6cda5a98d8d5c900d19a121b63b9b7f640fcc14) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - minor bug fix to Search for a load message

## 0.14.2

### Patch Changes

- [#311](https://github.com/axleapi/vulcan/pull/311) [`1512400`](https://github.com/axleapi/vulcan/commit/1512400891477c711f12f1b51d35a0bb47cde823) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Including CSS fixes for TMS Wrapper conflicts with hostpage styles

## 0.14.1

### Patch Changes

- [#310](https://github.com/axleapi/vulcan/pull/310) [`1ba4dc1`](https://github.com/axleapi/vulcan/commit/1ba4dc1775acd78ac01037d1ac2a62dfb8e91e22) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Adding filters for pickup/dropoff load types

- [#307](https://github.com/axleapi/vulcan/pull/307) [`c7506e4`](https://github.com/axleapi/vulcan/commit/c7506e4dd8cbde6058cb3da96f6e7a7e1fdfffc4) Thanks [@lsouza4](https://github.com/lsouza4)! - Allowing Relay to use Aljex wrapper and enabling their feature flags

- [#309](https://github.com/axleapi/vulcan/pull/309) [`030c9f1`](https://github.com/axleapi/vulcan/commit/030c9f1bc7ff711304835d76b7d057e22d314931) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fixing Quick Quote feature flag check

## 0.14.0

### Minor Changes

- [#302](https://github.com/axleapi/vulcan/pull/302) [`62e4fdd`](https://github.com/axleapi/vulcan/commit/62e4fdd8d55afd3375c88fc477571fee704fce6b) Thanks [@dhruv4](https://github.com/dhruv4)! - Update permissions for staging

### Patch Changes

- [#305](https://github.com/axleapi/vulcan/pull/305) [`8267119`](https://github.com/axleapi/vulcan/commit/8267119bcfc5c7f06aeecd026fc8990c4f3f9b9c) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Adding appointment confirmation info section to 7-Eleven and improving it's UI

## 0.13.1

### Patch Changes

- [#297](https://github.com/axleapi/vulcan/pull/297) [`13b57da`](https://github.com/axleapi/vulcan/commit/13b57da1050fd569cdfc96b6de8dd4bdfec2ae85) Thanks [@lsouza4](https://github.com/lsouza4)! - Adding pageview log on sign-in page for visibility on users facing login issues

- [#296](https://github.com/axleapi/vulcan/pull/296) [`b0407c8`](https://github.com/axleapi/vulcan/commit/b0407c80c305e5941b42eff6d855275deb9076c4) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fixing issue with router's navigation while unauthenticated

- [#295](https://github.com/axleapi/vulcan/pull/295) [`f2edba0`](https://github.com/axleapi/vulcan/commit/f2edba0e3e366403b4a83c42f71a3c1ed5571f29) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Adding Amplitude events to track the whole scheduling funnel

- [#292](https://github.com/axleapi/vulcan/pull/292) [`9fde221`](https://github.com/axleapi/vulcan/commit/9fde22138c6c35528c0a7b80030b304a80a1dcc0) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Scroll current tab into view, particularly when there are multiple suggestions

- [#300](https://github.com/axleapi/vulcan/pull/300) [`c99cf75`](https://github.com/axleapi/vulcan/commit/c99cf75a84489c9df65e55544bb84191560f3bc3) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Allowing users to type times on the Timepicker input without the hour-minute colon separator

- [#301](https://github.com/axleapi/vulcan/pull/301) [`d02d9bd`](https://github.com/axleapi/vulcan/commit/d02d9bd4b09a5e2c2394dc5c63e91509bfa3b501) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fix submit check call and exceptions

- [#289](https://github.com/axleapi/vulcan/pull/289) [`fe8783a`](https://github.com/axleapi/vulcan/commit/fe8783a5e9f9b936706b0bcca8fb24f5f9d682c2) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Show most recent suggestions and fix timestamps for appointment suggestions

- [#298](https://github.com/axleapi/vulcan/pull/298) [`fd4c16d`](https://github.com/axleapi/vulcan/commit/fd4c16d4ca15b72c0b3ae1ace2999d3e0565cf6b) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Replacing appointment scheduling confirmation toast with fixed information section

- [#299](https://github.com/axleapi/vulcan/pull/299) [`9bbc3f0`](https://github.com/axleapi/vulcan/commit/9bbc3f036e039e499754185122462da894d8dbd0) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fixing typo on appointment scheduling view's feature flag

- [#291](https://github.com/axleapi/vulcan/pull/291) [`1777240`](https://github.com/axleapi/vulcan/commit/17772404091ac0b55a17b6a9fa2420ca9002ec57) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Add email drafts for Carrier Checkins in Check Call subsection of Track and Trace tab.

- [#294](https://github.com/axleapi/vulcan/pull/294) [`5edc750`](https://github.com/axleapi/vulcan/commit/5edc7504f747edac52d444cf4241ad71c04d4fae) Thanks [@lsouza4](https://github.com/lsouza4)! - Adding Amplitude event for tracking the usage of load-independent appointment scheduling

## 0.13.0

### Minor Changes

- [#285](https://github.com/axleapi/vulcan/pull/285) [`46f582e`](https://github.com/axleapi/vulcan/commit/46f582e2ffbad03f85a308be71c736edffc97ef0) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Support dispatched time in carrier info AI suggestions

- [#288](https://github.com/axleapi/vulcan/pull/288) [`a81db95`](https://github.com/axleapi/vulcan/commit/a81db9525f38c354d2770de56c3f504f9ce9508b) Thanks [@lsouza4](https://github.com/lsouza4)! - Adding support for multiple integrations on opendock

## 0.12.3

### Patch Changes

- [#281](https://github.com/axleapi/vulcan/pull/281) [`ca01e76`](https://github.com/axleapi/vulcan/commit/ca01e760ce1f987ed0321c64a8987bda9576dc33) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fixing issue where empty date inputs would show as Invalid Date

- [#283](https://github.com/axleapi/vulcan/pull/283) [`f3d41fc`](https://github.com/axleapi/vulcan/commit/f3d41fc0125caed480176453d98e765e69b6784d) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fix Aljex mismatch errors due to trailing whitespace and character limits

- [#284](https://github.com/axleapi/vulcan/pull/284) [`48bdc91`](https://github.com/axleapi/vulcan/commit/48bdc9137caaf59925e3aea10c6b198e46a2163b) Thanks [@lsouza4](https://github.com/lsouza4)! - Integrating with non-native Timepicker to enforce 24h-clock format

## 0.12.2

### Patch Changes

- [#278](https://github.com/axleapi/vulcan/pull/278) [`a252a3c`](https://github.com/axleapi/vulcan/commit/a252a3ca4c02404989c88f53bbd94402cf10bba7) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - rename virtus to verona

- [#275](https://github.com/axleapi/vulcan/pull/275) [`a74b092`](https://github.com/axleapi/vulcan/commit/a74b092af420544eea0a1c66e32b08e8adb9d120) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Improvements for the warehouse timezone display on the Opendock form

- [#279](https://github.com/axleapi/vulcan/pull/279) [`adf742b`](https://github.com/axleapi/vulcan/commit/adf742b624ef7f925e75f3981957e0bfe43da5e1) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Wrap each tab section in an error boundary.

- [#254](https://github.com/axleapi/vulcan/pull/254) [`57f89b5`](https://github.com/axleapi/vulcan/commit/57f89b5d02a622113bc4523125d092d44b05babb) Thanks [@lsouza4](https://github.com/lsouza4)! - Adding support for Feature Flags based on ServiceID

- [#280](https://github.com/axleapi/vulcan/pull/280) [`0fee330`](https://github.com/axleapi/vulcan/commit/0fee33056fc3b766bde777618638a738bb0e34e1) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Including check to enable all feature flags locally

- [#277](https://github.com/axleapi/vulcan/pull/277) [`68da717`](https://github.com/axleapi/vulcan/commit/68da717af7cd4139fe10c157e11ed69eead6ef3c) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Add ai auto-fill suggestion for check calls.

## 0.12.1

### Patch Changes

- [#270](https://github.com/axleapi/vulcan/pull/270) [`e499352`](https://github.com/axleapi/vulcan/commit/e49935260c52846f19343d84bfcff8da09c34570) Thanks [@lsouza4](https://github.com/lsouza4)! - Improving logs for Sentry panics on the frontend

- [#267](https://github.com/axleapi/vulcan/pull/267) [`49497f1`](https://github.com/axleapi/vulcan/commit/49497f1bea323357356a110550a943a1d9832772) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Including changes to parse user's ServiceID from the JWT Auth token

- [#264](https://github.com/axleapi/vulcan/pull/264) [`ca7fe76`](https://github.com/axleapi/vulcan/commit/ca7fe76fac61c894df2050c4ddd08c701fac1dc2) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fix validation on read-only phone numbers

- [#273](https://github.com/axleapi/vulcan/pull/273) [`52a2b40`](https://github.com/axleapi/vulcan/commit/52a2b4044626f74b085af08e278d78d7465651b2) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fixing incorrect AI Filled hint on Appointent Scheduling timedate input

- [#274](https://github.com/axleapi/vulcan/pull/274) [`0842d91`](https://github.com/axleapi/vulcan/commit/0842d91f66edb5ba3c305646c156cd559a6d2dc5) Thanks [@lsouza4](https://github.com/lsouza4)! - Improving time parsing for Opendock form and including sidebar state context

- [#271](https://github.com/axleapi/vulcan/pull/271) [`706a21a`](https://github.com/axleapi/vulcan/commit/706a21a1653b1d96a424911acc0093fdc05ffa60) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Defaulting all clocks to 24h format, including datetime pickers on appt scheduling and improving usability on time inputs by allowing users to type

- [#272](https://github.com/axleapi/vulcan/pull/272) [`72da7a6`](https://github.com/axleapi/vulcan/commit/72da7a6bdbd45c63684dfcd8fdf642c0847d111d) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Adding OpenDock usability improvements

## 0.12.0

### Minor Changes

- [#242](https://github.com/axleapi/vulcan/pull/242) [`23084bd`](https://github.com/axleapi/vulcan/commit/23084bd886636fb7659c807a3517344e19c9d4cd) Thanks [@lsouza4](https://github.com/lsouza4)! - adding support for sidebar view switching and quick quote form submission

### Patch Changes

- [#257](https://github.com/axleapi/vulcan/pull/257) [`6daf188`](https://github.com/axleapi/vulcan/commit/6daf1888ca9d8af742adeacfc6b96e01089cb3ec) Thanks [@lsouza4](https://github.com/lsouza4)! - Adding changes to prevent layout shift caused by horizontal scrollbar on TabList components

- [#262](https://github.com/axleapi/vulcan/pull/262) [`f8e3b5a`](https://github.com/axleapi/vulcan/commit/f8e3b5ae72f28e1d389b3228e8e52e606376f4e5) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fix broken combobox component.

- [#256](https://github.com/axleapi/vulcan/pull/256) [`f99e426`](https://github.com/axleapi/vulcan/commit/f99e4266275fa4bd86fa4e9e6bc12e8d231f07bd) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Skip email suggestions when using Vulcan outside of Gmail e.g., a TMS like Aljex.

- [#263](https://github.com/axleapi/vulcan/pull/263) [`c35420f`](https://github.com/axleapi/vulcan/commit/c35420fd093381c1c29257fb7e71684cfcc54991) Thanks [@lsouza4](https://github.com/lsouza4)! - Fixing line wrap issue for same-column timedate inputs on Appointment Scheduling forms

## 0.11.1

### Patch Changes

- [#250](https://github.com/axleapi/vulcan/pull/250) [`467060e`](https://github.com/axleapi/vulcan/commit/467060e345e29a66aee30903457a592234619bad) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fix appointment suggestion timezone bug

- [#248](https://github.com/axleapi/vulcan/pull/248) [`c024b0d`](https://github.com/axleapi/vulcan/commit/c024b0db7cf65c08faa2b0f289bdcda3c96ce5dc) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - bumping alexandria with fixed require error

## 0.11.0

### Minor Changes

- [#240](https://github.com/axleapi/vulcan/pull/240) [`fc3c64f`](https://github.com/axleapi/vulcan/commit/fc3c64f202b0e5c718deac10227d1871133725b8) Thanks [@Sophie1142](https://github.com/Sophie1142)! - Add AI-filled labels to prepopulated form inputs

### Patch Changes

- [#245](https://github.com/axleapi/vulcan/pull/245) [`0d90ef0`](https://github.com/axleapi/vulcan/commit/0d90ef041c32ad396030131f947e39f687bc4ef0) Thanks [@jinyanzang](https://github.com/jinyanzang)! - add operator back

## 0.10.8

### Patch Changes

- [#236](https://github.com/axleapi/vulcan/pull/236) [`04261c3`](https://github.com/axleapi/vulcan/commit/04261c35b239223d36cc6af52c923b79ffcd6a77) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - updating events enum to be more readable

- [#238](https://github.com/axleapi/vulcan/pull/238) [`0cc2cef`](https://github.com/axleapi/vulcan/commit/0cc2cef0a6446760b573b715319bab47729bbe6c) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Look up load suggestions by thread

## 0.10.7

### Patch Changes

- [#227](https://github.com/axleapi/vulcan/pull/227) [`bc764f6`](https://github.com/axleapi/vulcan/commit/bc764f62fbcb33da5a60be3bafbaabf21caf3a47) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fix apply appointment suggestion flow

## 0.10.6

### Patch Changes

- [#226](https://github.com/axleapi/vulcan/pull/226) [`e4ee7a9`](https://github.com/axleapi/vulcan/commit/e4ee7a950e9f9e04daadd27957f87313e1b58aa9) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Prepopulate load info in the exception section under the track and trace tab.

- [#225](https://github.com/axleapi/vulcan/pull/225) [`17f54ef`](https://github.com/axleapi/vulcan/commit/17f54ef2b8e3fc325a7290e829f5e22bda368254) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Set the default tab from track and trace back to load information.

- [#223](https://github.com/axleapi/vulcan/pull/223) [`25f4d6d`](https://github.com/axleapi/vulcan/commit/25f4d6d2bfc0a1d1f3c694d97fbdeb568e795be2) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Remove redundant datetime picker in POST exception

## 0.10.5

### Patch Changes

- [#209](https://github.com/axleapi/vulcan/pull/209) [`c6759c4`](https://github.com/axleapi/vulcan/commit/c6759c4665b2011ad051bce2e0d56b6c5c9993e8) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fix select dropdown primarily in Vesta

- [#211](https://github.com/axleapi/vulcan/pull/211) [`7e6d9d8`](https://github.com/axleapi/vulcan/commit/7e6d9d8d17738d2e869c536d079d7239afb309c0) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - add aljex exception notification

## 0.10.4

### Patch Changes

- [#206](https://github.com/axleapi/vulcan/pull/206) [`231f962`](https://github.com/axleapi/vulcan/commit/231f962d496d45a142a49e3af2b0d7b9999c3fa7) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Add AI suggestion button and hide older suggestions in the email thread.

## 0.10.3

### Patch Changes

- [#204](https://github.com/axleapi/vulcan/pull/204) [`87b3c9a`](https://github.com/axleapi/vulcan/commit/87b3c9a435f1d2bee762f1d4b1b58b84331dcd01) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Fix extension breaking on Chrome Updates

- [#200](https://github.com/axleapi/vulcan/pull/200) [`2e7f164`](https://github.com/axleapi/vulcan/commit/2e7f164c6d38fe197ac8545d61c715dd1c73139f) Thanks [@kaykyb](https://github.com/kaykyb)! - Fix ErrorBoundary outside router

- [#205](https://github.com/axleapi/vulcan/pull/205) [`126dee3`](https://github.com/axleapi/vulcan/commit/126dee32782f2a6377834e7a1408783dad617b29) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Add button to skip AI suggestion and only display actual changes

- [#202](https://github.com/axleapi/vulcan/pull/202) [`fd22b07`](https://github.com/axleapi/vulcan/commit/fd22b07741485f4e63cd5a00f24e965135fee6ad) Thanks [@kaykyb](https://github.com/kaykyb)! - Use SWR for data fetching

## 0.10.2

### Patch Changes

- [#198](https://github.com/axleapi/vulcan/pull/198) [`bb91cb9`](https://github.com/axleapi/vulcan/commit/bb91cb98d20ac5144b919f0074f286b61cf53890) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - make AI Suggestion box collapsible

## 0.10.1

### Patch Changes

- [#196](https://github.com/axleapi/vulcan/pull/196) [`93f69ed`](https://github.com/axleapi/vulcan/commit/93f69ed33cb4101f8d8d172065312ee59d206d9b) Thanks [@AxleGithubBot](https://github.com/AxleGithubBot)! - Move suggested carrier info to Load info

## 0.10.0

### Minor Changes

- [#194](https://github.com/axleapi/vulcan/pull/194) [`7dccd46`](https://github.com/axleapi/vulcan/commit/7dccd46aaa34134546020bfd3ed8951013fcb261) Thanks [@h-morgan](https://github.com/h-morgan)! - Add parsing for carrier information from emails

### Patch Changes

- [#193](https://github.com/axleapi/vulcan/pull/193) [`ebad217`](https://github.com/axleapi/vulcan/commit/ebad21731771b0a6dadf05dfd5ecbff4394ccacb) Thanks [@kaykyb](https://github.com/kaykyb)! - Fix issues with ErrorBoundary and ensure sentry issues are being logged

- [#188](https://github.com/axleapi/vulcan/pull/188) [`6f8cc67`](https://github.com/axleapi/vulcan/commit/6f8cc6765cfe0cb1636ae365b43125d7b43e0571) Thanks [@kaykyb](https://github.com/kaykyb)! - Fix rendering glitch affecting the sidebar background on Aljex

- [#185](https://github.com/axleapi/vulcan/pull/185) [`9322fd3`](https://github.com/axleapi/vulcan/commit/9322fd3a2746c852849132b981a00bff688c81d5) Thanks [@h-morgan](https://github.com/h-morgan)! - update inboxsdk/core version to latest, 2.1.28

- [#193](https://github.com/axleapi/vulcan/pull/193) [`ebad217`](https://github.com/axleapi/vulcan/commit/ebad21731771b0a6dadf05dfd5ecbff4394ccacb) Thanks [@kaykyb](https://github.com/kaykyb)! - Fix import for FieldAttributes ctx instead of ReadOnlyContext

- [#192](https://github.com/axleapi/vulcan/pull/192) [`116f41e`](https://github.com/axleapi/vulcan/commit/116f41ec57de99546f7aab67d3a6132cd75a2921) Thanks [@kaykyb](https://github.com/kaykyb)! - Fix issues with ErrorBoundary and ensure sentry issues are being logged

## 0.9.0

### Minor Changes

- [#174](https://github.com/axleapi/vulcan/pull/174) [`bd20c94`](https://github.com/axleapi/vulcan/commit/bd20c9472f00d0eb8649cc722496588568226ff6) Thanks [@kaykyb](https://github.com/kaykyb)! - Consolidate Outlook and Chrome extensions with same features

- [#178](https://github.com/axleapi/vulcan/pull/178) [`4125cc4`](https://github.com/axleapi/vulcan/commit/4125cc428b93e6dfec4b8d02f45f46299e48eb83) Thanks [@h-morgan](https://github.com/h-morgan)! - Add PO numbers to the Overview Section in the sidebar, if we have them

### Patch Changes

- [#177](https://github.com/axleapi/vulcan/pull/177) [`70914d8`](https://github.com/axleapi/vulcan/commit/70914d8f2bf57a2e9b2c3934f2728b6c72aae493) Thanks [@boogsbunny](https://github.com/boogsbunny)! - consolidate opendock and email request in appointment scheduling

## 0.8.1

### Patch Changes

- [#165](https://github.com/axleapi/vulcan/pull/165) [`fdc66e5`](https://github.com/axleapi/vulcan/commit/fdc66e553110764b38cad27d94fffa9535efe9ba) Thanks [@kaykyb](https://github.com/kaykyb)! - Fix null initial freight tracking ids

- [#170](https://github.com/axleapi/vulcan/pull/170) [`2ed8475`](https://github.com/axleapi/vulcan/commit/2ed8475dbeb54e9cd6408e6e9f2d459839c61185) Thanks [@boogsbunny](https://github.com/boogsbunny)! - format phone numbers automatically on user input

- [#172](https://github.com/axleapi/vulcan/pull/172) [`2a248e9`](https://github.com/axleapi/vulcan/commit/2a248e96ebb742bc23fcfc87e4bb475d4b46098a) Thanks [@boogsbunny](https://github.com/boogsbunny)! - disable typing on time picker component

- [#167](https://github.com/axleapi/vulcan/pull/167) [`09d0e32`](https://github.com/axleapi/vulcan/commit/09d0e32743b068f00950d518861ee9d8134984e9) Thanks [@Sophie1142](https://github.com/Sophie1142)! - Improve defaults for open slots search

- [#168](https://github.com/axleapi/vulcan/pull/168) [`65281ea`](https://github.com/axleapi/vulcan/commit/65281eaa539913a977b70fea44a3deaac403e03e) Thanks [@kaykyb](https://github.com/kaykyb)! - Handle invalid/undefined dates in date time pickers

- [#169](https://github.com/axleapi/vulcan/pull/169) [`96ef8de`](https://github.com/axleapi/vulcan/commit/96ef8de91708de3d58c1279d1e6d14b59fe43399) Thanks [@kaykyb](https://github.com/kaykyb)! - Implement safety measure against possible incomplete updates

## 0.8.0

### Minor Changes

- [#161](https://github.com/axleapi/vulcan/pull/161) [`62ea7a3`](https://github.com/axleapi/vulcan/commit/62ea7a3f0f49c8ab59f1c7ac7f27df60191d44cc) Thanks [@kaykyb](https://github.com/kaykyb)! - Add date time picker components

- [#155](https://github.com/axleapi/vulcan/pull/155) [`88a507a`](https://github.com/axleapi/vulcan/commit/88a507a97e1188a91a3d9d4ca35a28b44288b219) Thanks [@kaykyb](https://github.com/kaykyb)! - Implement DatePicker component in Smart Reply

- [#163](https://github.com/axleapi/vulcan/pull/163) [`c012f2c`](https://github.com/axleapi/vulcan/commit/c012f2c38b1d7d479a430e8e945d9994228c9a81) Thanks [@kaykyb](https://github.com/kaykyb)! - add ability to clear date fields

- [#158](https://github.com/axleapi/vulcan/pull/158) [`a9c15d0`](https://github.com/axleapi/vulcan/commit/a9c15d0a4b7d5fc124f99f429a049e2fe664320c) Thanks [@kaykyb](https://github.com/kaykyb)! - Adds the ability to have multiple loads open in tabs on Beacon

- [#160](https://github.com/axleapi/vulcan/pull/160) [`d4d6ce3`](https://github.com/axleapi/vulcan/commit/d4d6ce34472bf21ce141a224c0ae98873af55771) Thanks [@dhruv4](https://github.com/dhruv4)! - Add date and time pickers to check call page

- [#159](https://github.com/axleapi/vulcan/pull/159) [`296d7aa`](https://github.com/axleapi/vulcan/commit/296d7aa8e75eab63a9ee3c896c564b0d0563c5c5) Thanks [@kaykyb](https://github.com/kaykyb)! - Add ability to save llm suggestionas dropoff

### Patch Changes

- [#164](https://github.com/axleapi/vulcan/pull/164) [`00c482b`](https://github.com/axleapi/vulcan/commit/00c482b6ba2756268079f86787b82db67ff010b0) Thanks [@Sophie1142](https://github.com/Sophie1142)! - Fix Opendock integration

- [#154](https://github.com/axleapi/vulcan/pull/154) [`5f8834c`](https://github.com/axleapi/vulcan/commit/5f8834c8c5296450597cd85de1091c83c0bd5c40) Thanks [@jinyanzang](https://github.com/jinyanzang)! - remove workflow that's not working

## 0.7.0

### Minor Changes

- [#153](https://github.com/axleapi/vulcan/pull/153) [`e1a499c`](https://github.com/axleapi/vulcan/commit/e1a499ce79a721b3e0fed49268e19347c4e5f919) Thanks [@kaykyb](https://github.com/kaykyb)! - Autocomplete smart reply form

- [#151](https://github.com/axleapi/vulcan/pull/151) [`9c937df`](https://github.com/axleapi/vulcan/commit/9c937df9f8271d2ba46c8c16c8cd7bb07e922c1f) Thanks [@kaykyb](https://github.com/kaykyb)! - Rename appt schedulig e-mail request form fields

## 0.6.0

### Minor Changes

- [#146](https://github.com/axleapi/vulcan/pull/146) [`8544ca4`](https://github.com/axleapi/vulcan/commit/8544ca49aaf149e246e93e7074a2f333f498f87e) Thanks [@kaykyb](https://github.com/kaykyb)! - Add ability to generate NFI-structured appointment request e-mails

- [#149](https://github.com/axleapi/vulcan/pull/149) [`a5b8f34`](https://github.com/axleapi/vulcan/commit/a5b8f342b217492870c2864e1380f2dcaec9a56d) Thanks [@kaykyb](https://github.com/kaykyb)! - Add error messages when load not found

- [#147](https://github.com/axleapi/vulcan/pull/147) [`454dc65`](https://github.com/axleapi/vulcan/commit/454dc6594fcdacb32c74d29729a152aa21f9fadd) Thanks [@kaykyb](https://github.com/kaykyb)! - Add searchbar to switch between loads

- [#144](https://github.com/axleapi/vulcan/pull/144) [`e6f097e`](https://github.com/axleapi/vulcan/commit/e6f097e242605633178c5c19fae526c8cd327c2e) Thanks [@kaykyb](https://github.com/kaykyb)! - Allow Beacon to work in ALjex website, when viewing a PRO

### Patch Changes

- [#145](https://github.com/axleapi/vulcan/pull/145) [`635d9a3`](https://github.com/axleapi/vulcan/commit/635d9a33729a40a8e232747c9333f633fcb7a23c) Thanks [@Sophie1142](https://github.com/Sophie1142)! - Read and write check call timestamps in local time

- [#140](https://github.com/axleapi/vulcan/pull/140) [`49a04ba`](https://github.com/axleapi/vulcan/commit/49a04ba43659c8df28744f16fafcdfc27ee198fb) Thanks [@jinyanzang](https://github.com/jinyanzang)! - fix workflow automation

- [#150](https://github.com/axleapi/vulcan/pull/150) [`2a1ebca`](https://github.com/axleapi/vulcan/commit/2a1ebcac85c0a630192f8fc654d76fe860cc4b85) Thanks [@kaykyb](https://github.com/kaykyb)! - Comment out OpenDock integration until we resolve backend issues

- [#142](https://github.com/axleapi/vulcan/pull/142) [`21a4a70`](https://github.com/axleapi/vulcan/commit/21a4a70337a5edb82b96d69c2b59f3d4cf3844bc) Thanks [@boogsbunny](https://github.com/boogsbunny)! - add operator removal option to dropdown menu

- [#143](https://github.com/axleapi/vulcan/pull/143) [`7766192`](https://github.com/axleapi/vulcan/commit/7766192e0b221f54a2919589a6726afbb9f2b7f4) Thanks [@dependabot](https://github.com/apps/dependabot)! - update dependencies

- [#140](https://github.com/axleapi/vulcan/pull/140) [`49a04ba`](https://github.com/axleapi/vulcan/commit/49a04ba43659c8df28744f16fafcdfc27ee198fb) Thanks [@jinyanzang](https://github.com/jinyanzang)! - change workflow

## 0.5.0

### Minor Changes

- [#137](https://github.com/axleapi/vulcan/pull/137) [`8f53a9e`](https://github.com/axleapi/vulcan/commit/8f53a9ea1dc2d8ceee0372c0cfea75820c5b41e4) Thanks [@kaykyb](https://github.com/kaykyb)! - Add AI appointment confirmation detection/suggestion

### Patch Changes

- [#138](https://github.com/axleapi/vulcan/pull/138) [`61c7d85`](https://github.com/axleapi/vulcan/commit/61c7d8514fbd92c7567bbdc585f004f308a32384) Thanks [@Sophie1142](https://github.com/Sophie1142)! - Fix handling of no check calls

## 0.4.1

### Patch Changes

- [#135](https://github.com/axleapi/vulcan/pull/135) [`1bd58e4`](https://github.com/axleapi/vulcan/commit/1bd58e49865d4780f52dcc3b919234266193129e) Thanks [@Sophie1142](https://github.com/Sophie1142)! - Fix Alexandria pointer

## 0.3.0

### Minor Changes

- [#122](https://github.com/axleapi/vulcan/pull/122) [`bd42a17`](https://github.com/axleapi/vulcan/commit/bd42a1750320a8007b73aec39fe6975e61d742f6) Thanks [@Sophie1142](https://github.com/Sophie1142)! - Feature: Post check calls to TMS

- [#127](https://github.com/axleapi/vulcan/pull/127) [`f3f8b8e`](https://github.com/axleapi/vulcan/commit/f3f8b8e24f7741f9b79f9b6840a589f0b25fa8b5) Thanks [@boogsbunny](https://github.com/boogsbunny)! - add operator assignment for loads feature

### Patch Changes

- [#121](https://github.com/axleapi/vulcan/pull/121) [`5a02d00`](https://github.com/axleapi/vulcan/commit/5a02d00995c4ab43c80b5dd519e9dfb0f69bb6de) Thanks [@boogsbunny](https://github.com/boogsbunny)! - provide more context on API request failures in Sentry breadcrumbs

- [#125](https://github.com/axleapi/vulcan/pull/125) [`2f1d866`](https://github.com/axleapi/vulcan/commit/2f1d866cc3465aa95a99865bfaad2a710c1a7614) Thanks [@kaykyb](https://github.com/kaykyb)! - Refactors AuthService to use a hook pattern

- [#128](https://github.com/axleapi/vulcan/pull/128) [`c80f840`](https://github.com/axleapi/vulcan/commit/c80f840b8eb3b5eefc1dec6689e9069edc32eb26) Thanks [@dhruv4](https://github.com/dhruv4)! - Switch from MomentJS to DayJS which is 10x smaller

## 0.2.0

### Minor Changes

- [#117](https://github.com/axleapi/vulcan/pull/117) [`e530859`](https://github.com/axleapi/vulcan/commit/e53085970978e0f1a381d6a249dd671d4e2612a7) Thanks [@kaykyb](https://github.com/kaykyb)! - Better handle login errors and chrome extension updates

- [#105](https://github.com/axleapi/vulcan/pull/105) [`ef25dac`](https://github.com/axleapi/vulcan/commit/ef25dac71fc799fd37a3ab5f2eb5dfb924946d73) Thanks [@kaykyb](https://github.com/kaykyb)! - Extract Alexandria component library

### Patch Changes

- [#115](https://github.com/axleapi/vulcan/pull/115) [`da8dfc6`](https://github.com/axleapi/vulcan/commit/da8dfc6d90913a0cce5a2dd07818cebd93cc2961) Thanks [@Sophie1142](https://github.com/Sophie1142)! - Revise PATCH load request to support operator functions

- [#118](https://github.com/axleapi/vulcan/pull/118) [`ca99d5a`](https://github.com/axleapi/vulcan/commit/ca99d5adbdde898af55406544e883e47f70152c5) Thanks [@kaykyb](https://github.com/kaykyb)! - Upgrade InboxSDK and UI components

- [#107](https://github.com/axleapi/vulcan/pull/107) [`3ec9c3d`](https://github.com/axleapi/vulcan/commit/3ec9c3de5ca57bc6ae1ca367dce4a8f4260cd534) Thanks [@kaykyb](https://github.com/kaykyb)! - Remove unused refreshToken logic

- [#109](https://github.com/axleapi/vulcan/pull/109) [`b87d5cd`](https://github.com/axleapi/vulcan/commit/b87d5cd6936b4efd1c5212eef9e47a1d929e039f) Thanks [@Sophie1142](https://github.com/Sophie1142)! - Preserve Aljex timestamps by not converting UTC to user's locale

- [#106](https://github.com/axleapi/vulcan/pull/106) [`cf76690`](https://github.com/axleapi/vulcan/commit/cf7669065e6fc647d42079758b1c3873c7719a25) Thanks [@kaykyb](https://github.com/kaykyb)! - Implement a temporary fix for Chrome extension updates breaking the content script

## 0.1.0

### Minor Changes

- [#100](https://github.com/axleapi/vulcan/pull/100) [`962d583`](https://github.com/axleapi/vulcan/commit/962d583ddaa5848a0d37d432704b9698ef231915) Thanks [@kaykyb](https://github.com/kaykyb)! - Implement a new design and refactors part of the appointment scheduling page

### Patch Changes

- [#101](https://github.com/axleapi/vulcan/pull/101) [`990e273`](https://github.com/axleapi/vulcan/commit/990e2737565db00b5fc7812e42c6eef433c0f91e) Thanks [@kaykyb](https://github.com/kaykyb)! - Do not attempt to initialize InboxSDK on Gmail popouts or outside Gmail

- [#103](https://github.com/axleapi/vulcan/pull/103) [`f8f2758`](https://github.com/axleapi/vulcan/commit/f8f27583164421c342229c9d4b806e5678c5a1b8) Thanks [@boogsbunny](https://github.com/boogsbunny)! - Fix the GitHub release script

## 0.0.11

### Patch Changes

- [#95](https://github.com/axleapi/vulcan/pull/95) [`eef742f`](https://github.com/axleapi/vulcan/commit/eef742f629678cecba32e25b37e4b1146491cf17) Thanks [@kaykyb](https://github.com/kaykyb)! - Remove the injection of Sentry release id to ensure transpilation of JS/TS works in browser

- [#97](https://github.com/axleapi/vulcan/pull/97) [`fbc9704`](https://github.com/axleapi/vulcan/commit/fbc970448490d9ee20c7d1d23dd1751a13d41e75) Thanks [@boogsbunny](https://github.com/boogsbunny)! - include Sentry auth token in the environment

## 0.0.10

### Patch Changes

- [#86](https://github.com/axleapi/vulcan/pull/86) [`9323d38`](https://github.com/axleapi/vulcan/commit/9323d388c436b39c251e08267ad5b5cdfd972b26) Thanks [@kaykyb](https://github.com/kaykyb)! - Redesign collapsibles accordingly to new design

- [#85](https://github.com/axleapi/vulcan/pull/85) [`208baf9`](https://github.com/axleapi/vulcan/commit/208baf92acd8776f1e3c94a0a1a7c7d45a21a19d) Thanks [@kaykyb](https://github.com/kaykyb)! - Scope execution of the Beacon sidebar to only inside the Gmail website

- [#82](https://github.com/axleapi/vulcan/pull/82) [`7e48419`](https://github.com/axleapi/vulcan/commit/7e484192bb157fece998323dfd573830e4a0e201) Thanks [@kaykyb](https://github.com/kaykyb)! - Upgraded InboxSDK to version 2.1.17, in attempt to fix issues on loading Beacon sidebar for some users

- [#84](https://github.com/axleapi/vulcan/pull/84) [`98c3bb6`](https://github.com/axleapi/vulcan/commit/98c3bb6b3c48976d0cd430142f417c0c14689a5e) Thanks [@kaykyb](https://github.com/kaykyb)! - Improves error logging by generating sourcemaps and uploading them to Sentry

- [#88](https://github.com/axleapi/vulcan/pull/88) [`e120b93`](https://github.com/axleapi/vulcan/commit/e120b93cba42e0e6ba8c65b8bdd09a097e54c280) Thanks [@kaykyb](https://github.com/kaykyb)! - Adds a logout button

- [#89](https://github.com/axleapi/vulcan/pull/89) [`2f028f1`](https://github.com/axleapi/vulcan/commit/2f028f174f69eac856f24c43360c68286322cd51) Thanks [@austinbyers](https://github.com/austinbyers)! - Fix date placeholder
