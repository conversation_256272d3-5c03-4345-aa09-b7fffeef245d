/* Antd overrides */

/* Overriding some of antd default styles to make it concise */
.ant-picker-input {
  font-size: 14px !important;
}

.ant-picker-suffix {
  display: none !important;
}

.ant-select {
  color: hsl(var(--neutral-input-text)) !important;
  background-color: hsl(var(--neutral-input-bg)) !important;
  border: 1px solid hsl(var(--neutral-input-border)) !important;
  width: 100%;
  height: 32px !important;
  position: relative !important;
  margin-top: 4px !important;
  border-radius: 4px !important;

  &.ant-select-disabled {
    border: 1px solid hsl(var(--neutral-input-disabled-border)) !important;
  }
}

.ant-select-selection-placeholder {
  color: hsl(var(--neutral-input-placeholder)) !important;
}

.ant-select .ant-select-selector,
.ant-select .ant-select-selection-search-input {
  color: hsl(var(--neutral-input-text)) !important;
  background-color: hsl(var(--neutral-input-bg)) !important;
  cursor: pointer !important;
  border: none !important;
  border-radius: 4px !important;
}

.ant-select .ant-select-selection-item,
.ant-select .ant-select-selection-search {
  color: hsl(var(--neutral-input-text)) !important;
  cursor: pointer !important;
  font-size: 14px !important;
  border-radius: 4px !important;
}

.search-filter .ant-select-selector {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  margin-right: 16px !important;
}

.search-filter .ant-select-focused .ant-select-selector {
  box-shadow: none !important;
}

.search-filter .ant-select-selection-item {
  background: transparent !important;
  padding: 0 !important;
  font-size: 14px !important;
}

.search-filter .ant-select-arrow {
  right: 0px !important;
}

.search-filter .ant-select-arrow svg {
  width: 12px !important;
}

.search-container .ant-picker-suffix {
  display: flex !important;
}

.search-container .ant-picker.ant-picker-outlined {
  border: 1px solid hsl(var(--neutral-input-border)) !important;
  border-radius: 4px !important;
  padding-left: 6px !important;
  padding-right: 6px !important;
}

.search-container .ant-input.ant-input-outlined {
  font-size: 12px !important;
  border: 1px solid hsl(var(--neutral-input-border)) !important;
  padding: 4px 4px 4px 6px !important;
  border-radius: 4px !important;
}

.search-container .ant-picker-input {
  font-size: 10px !important;
}

.search-container .ant-select-selector,
.search-container .ant-select-selection-search-input {
  height: 100% !important;
}

.ant-carousel .slick-track {
  display: flex !important;
  align-items: center !important;
}

.ant-carousel .slick-dots {
  bottom: -16px !important;
}

.ant-carousel .slick-slider {
  width: calc(100% - 16px);
  margin: 0px auto;
}

.ant-carousel .single-card.slick-slider {
  width: 100%;
}

.ant-carousel .slick-slide {
  transition: all 0.2s ease;
}

.ant-carousel .slick-slide:has(.carousel-card-hover:hover) {
  transform: scale(1.025);
}

.ant-carousel .slick-dots li button {
  font-size: 0 !important;
  background: hsl(var(--neutral-800)) !important;
}

.ant-carousel .slick-dots li.slick-active::after,
.ant-carousel .slick-dots li.slick-active button {
  background: hsl(var(--neutral-600)) !important;
  opacity: 1;
}

.ant-carousel .slick-prev {
  inset-inline-start: -12px !important;
}

.ant-carousel .slick-next {
  inset-inline-end: 0 !important;
}

.ant-carousel .slick-prev,
.ant-carousel .slick-next {
  position: absolute;
  top: 50%;
  width: 12px !important;
  height: 12px !important;
  transform: translateY(-50%);
  color: hsl(var(--neutral-500)) !important;
  opacity: 0.4 !important;
  font-size: 0 !important;
  line-height: 0 !important;
  transition: opacity 0.2s ease;
}

.ant-carousel .slick-prev:hover,
.ant-carousel .slick-next:hover,
.ant-carousel .slick-prev:focus,
.ant-carousel .slick-next:focus {
  opacity: 1 !important;
}

.ant-carousel .slick-prev::after,
.ant-carousel .slick-next::after {
  box-sizing: border-box;
  position: absolute;
  display: inline-block;
  width: 12px !important;
  height: 12px !important;
  font-size: 14px !important;
  border-color: hsl(var(--neutral-500)) !important;
  border-inline-width: 2px 0 !important;
  border-block-width: 2px 0 !important;
  border-radius: 1px;
  content: '';
}

.ant-carousel .slick-prev::after {
  transform: rotate(-45deg) !important;
}

.ant-carousel .slick-next::after {
  transform: rotate(135deg) !important;
}

.ant-carousel .carousel-card h3.text-md {
  font-size: 14px !important;
}

.ant-carousel .carousel-card .font-medium,
.ant-carousel .carousel-card .text-xs div {
  font-size: 12px !important;
}

.ant-divider {
  border-block-start: 1px solid hsl(var(--neutral-700)) !important;
}

.ant-float-btn-body {
  border-radius: 4px !important;
  background-color: hsl(var(--brand)) !important;
}

.ant-float-btn-icon {
  display: none;
}

.ant-float-btn-description {
  font-family: 'Plus Jakarta Sans', sans-serif !important;
  font-size: 16px !important;
  font-weight: 600 !important;
}

/* select dropdown fights for z-index with drumkit-root element on TMS Wrapper */
.ant-select-dropdown {
  z-index: 9999999 !important;
  min-width: 200px !important;
  background-color: hsl(var(--neutral-input-bg)) !important;
  border-color: hsl(var(--neutral-input-border)) !important;

  .ant-select-item-option-content {
    color: hsl(var(--neutral-input-text)) !important;
  }

  .ant-select-item-option-selected .ant-select-item-option-content {
    color: invert(hsl(var(--neutral-input-text))) !important;
  }
}

/* Make select option groups more readable */
.ant-select-item-group {
  font-weight: 700 !important;
  color: hsl(var(--neutral-500)) !important;
  padding-bottom: 0px !important;
  line-height: 2 !important;
}

/* Add a border that behaves as a separator to the top of select option group */
.ant-select-item-group:not(:first-child) {
  margin-top: 8px !important;
  border-top-right-radius: 0px !important;
  border-top-left-radius: 0px !important;
  border-top: 1.5px solid hsl(var(--neutral-200)) !important;
}

.ant-select-item-option-content {
  .flex { display: flex !important }
  .flex-col { flex-direction: column !important }
  .font-medium { font-weight: 500 !important }
  .text-xs { font-size: 12px !important }
  .text-gray-500{ color: hsl(var(--neutral-500)) !important }
}

.ant-picker-dropdown {
  z-index: 9999999 !important;
}
