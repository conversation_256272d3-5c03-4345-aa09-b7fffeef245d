import { createContext, useMemo, useState } from 'react';

import {
  IngestionMessage,
  initialIngestionMessage,
} from 'types/IngestionMessage';

const InboxContext = createContext({
  threadId: '',
  ingestionMessage: initialIngestionMessage,
});

type InboxProviderProps = {
  initialThreadId: string;
  ingestionMessage: IngestionMessage;
  children: React.ReactNode;
};

const InboxProvider = (props: InboxProviderProps) => {
  // service information
  const [threadId, setThreadId] = useState(props.initialThreadId);
  const [ingestionMessage, setIngestionMessage] = useState(
    props.ingestionMessage
  );

  // The state that we'll be storing the Inbox into because we will be
  // providing an object to the provider. It is better to put the value
  // inside a useMemo so that the component will only re-render when
  // there's a change in the value.
  const value = useMemo(
    () => ({
      threadId,
      ingestionMessage,
      setThreadId,
      setIngestionMessage,
    }),
    [threadId]
  );

  return (
    <InboxContext.Provider value={value}>
      {props.children}
    </InboxContext.Provider>
  );
};
export { InboxContext, InboxProvider };
