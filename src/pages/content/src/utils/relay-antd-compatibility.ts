export default function getRelayAntdCompatibilityStyles() {
  return `
    form {
      .ant-select-selector {
        padding: 0 11px;
        width: 100%;
        height: 100%;
      }

      .ant-select-selection-wrap {
        display: flex;
        width: 100%;
        height: 100%;
        position: relative;
      }

      .ant-select-selection-search {
        padding-inline-end: 18px;
        position: absolute;
        inset: 0;
        width: 100%;
      }

      .ant-select-selection-search-input {
        height: 100%;
        width: 100%;
      }

      .ant-select-selection-placeholder {
        display: block;
        align-self: center;
      }

      .ant-select-selection-item {
        padding-inline-end: 18px;
        display: block;
        transition: all 0.3s, visibility 0s;
        align-self: center;
        flex: 1;
        position: relative;
        user-select: none;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .ant-select-arrow {
        display: flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.25);
        font-style: normal;
        line-height: 1;
        text-align: center;
        text-transform: none;
        vertical-align: -0.125em;
        position: absolute;
        top: 50%;
        inset-inline-start: auto;
        inset-inline-end: 11px;
        height: 12px;
        margin-top: -6px;
        font-size: 12px;
        pointer-events: none;
        transition: opacity 0.3s ease;
      }
    }
  `;
}
