import { render, screen, waitFor } from '@testing-library/react';

import { initialIngestionMessage } from 'types/IngestionMessage';

import { AppGmail } from './app';

jest.mock('@constants/DrumkitApiUrl', () => ({
  DRUMKIT_API_URL: 'mock_url',
}));

jest.mock('@constants/DrumkitAuthUrl', () => ({
  DRUMKIT_AUTH_URL: 'mock_url',
}));

jest.mock('@constants/SentryDsn', () => ({
  SENTRY_DSN: 'mock_sentry_dsn',
}));

jest.mock('@constants/SkipIngestEmail', () => ({
  SKIP_INGEST_EMAIL: 'mock_skip_ingest_email',
}));

jest.mock('@constants/PosthogApiKey', () => ({
  POSTHOG_API_KEY: 'mock_posthog_api_key',
}));

jest.mock('@constants/Environment', () => ({
  ENVIRONMENT: 'development',
}));

jest.mock('@constants/AppVersion', () => ({
  APP_VERSION: '0.0.1',
}));

jest.mock('@auth/AuthService', () => ({
  getCurrentUser: jest.fn().mockResolvedValue({ email: '<EMAIL>' }),
  hasValidUserAuth: jest.fn().mockResolvedValue(true),
}));

describe('appTest', () => {
  test('content loaded', async () => {
    render(
      <AppGmail
        inboxEmailAddress={'<EMAIL>'}
        threadId='123'
        ingestionMessage={initialIngestionMessage}
      />
    );
    await waitFor(() => {
      screen.getByText('Loading...');
    });
  });
});
