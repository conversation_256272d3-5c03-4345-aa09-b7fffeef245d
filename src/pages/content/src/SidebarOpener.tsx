import { useEffect, useRef, useState } from 'react';

import { GripVertical, XIcon } from 'lucide-react';

import Logo from '@src/alexandria/assets/drumkit-34.png';

import { Button } from 'components/Button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from 'components/Tooltip';
import { Typography } from 'components/typography';
import { Maybe } from 'types/UtilityTypes';

export type SidePanelStateMessage = {
  command: 'SidePanelStateUpdated';
  open: boolean;
};

export type ProStateMessage = {
  command: 'ProStatusUpdated';
  hasPro: boolean;
};

export const SidebarOpenerElementID = 'drumkit-popup-view-root';

// Set to 24 hours by default
const BUTTON_SNOOZE_TIME = 24 * 60 * 60 * 1000;
// Cookie helpers
const COOKIE_HIDE_UNTIL = 'drumkitOpenerHideUntil';
const COOKIE_TOP_POSITION = 'drumkitOpenerTopPosition';

function setCookie(name: string, value: string, days = 30) {
  const expires = new Date(Date.now() + days * 864e5).toUTCString();
  document.cookie = `${name}=${encodeURIComponent(value)}; expires=${expires}; path=/`;
}

function getCookie(name: string): Maybe<string> {
  const match = document.cookie
    .split('; ')
    .find((row) => row.startsWith(name + '='));
  return match ? decodeURIComponent(match.split('=')[1]) : null;
}

export default function Popup() {
  const [isPinned, setIsPinned] = useState<Maybe<boolean>>(null);
  const [isSidePanelOpen, setIsSidePanelOpen] = useState<boolean>(true);
  const [hasPro, setHasPro] = useState<Maybe<boolean>>(null);
  const [isHovered, setIsHovered] = useState<boolean>(false);
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [dragOffset, setDragOffset] = useState<{ y: number }>({ y: 0 });
  const dragRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    async function fetchExtensionInfo() {
      try {
        const response: boolean = await chrome.runtime.sendMessage({
          command: 'CheckExtensionPin',
        });
        setIsPinned(response);

        const tab: chrome.tabs.Tab = await chrome.runtime.sendMessage({
          command: 'GetCurrentTab',
        });

        if (tab?.id) {
          const isOpen: boolean = await chrome.runtime.sendMessage({
            command: 'CheckSidePanelOpen',
            tabId: tab.id,
          });
          setIsSidePanelOpen(isOpen);

          const checkProResponse = await chrome.runtime.sendMessage({
            command: 'CheckProAvailable',
          });
          setHasPro(checkProResponse?.hasPro);
        }
      } catch (error) {
        console.error('Error fetching extension info:', error);
      }
    }

    fetchExtensionInfo();
  }, []);

  function handleProAndSidePanelStatus(
    msg: SidePanelStateMessage | ProStateMessage
  ) {
    if (msg.command === 'SidePanelStateUpdated') {
      if (msg.open !== undefined) {
        setIsSidePanelOpen(msg.open);
      }
    } else if (msg.command === 'ProStatusUpdated') {
      if (msg.hasPro !== undefined) {
        setHasPro(msg.hasPro);
      }
    }
  }

  // Restore saved position on mount
  useEffect(() => {
    const elt = document.getElementById(SidebarOpenerElementID);
    if (!elt) return;

    const topPosition = getCookie(COOKIE_TOP_POSITION);
    if (topPosition) {
      const savedPosition = parseInt(topPosition, 10);
      if (!isNaN(savedPosition)) {
        const maxY = Math.max(0, window.innerHeight - elt.offsetHeight);
        const y = Math.max(0, Math.min(savedPosition, maxY));
        elt.style.top = `${y}px`;
      }
    }
  }, []);

  useEffect(() => {
    chrome.runtime.onMessage.addListener(handleProAndSidePanelStatus);

    return () => {
      chrome.runtime.onMessage.removeListener(handleProAndSidePanelStatus);
    };
  }, []);

  // Show/hide with cookie-based suppression
  useEffect(() => {
    const elt = document.getElementById(SidebarOpenerElementID);
    if (elt) {
      const hideUntilStr = getCookie(COOKIE_HIDE_UNTIL);
      const now = Date.now();
      const isSuppressed =
        hideUntilStr != null && now < Number(hideUntilStr || 0);

      // Only show popup if sidepanel is closed AND we have an Aljex PRO ID AND not within snooze window
      const shouldShow = !isSidePanelOpen && hasPro !== false && !isSuppressed;

      if (shouldShow) {
        elt.style.display = 'block';
      } else {
        elt.style.display = 'none';
      }
    }
  }, [isSidePanelOpen, hasPro]);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (isDragging && dragRef.current) {
        const rootElement = document.getElementById(SidebarOpenerElementID);
        if (rootElement) {
          const newY = e.clientY - dragOffset.y;
          const maxY = window.innerHeight - rootElement.offsetHeight;

          const constrainedY = Math.max(0, Math.min(newY, maxY));

          rootElement.style.top = `${constrainedY}px`;
        }
      }
    };

    const handleMouseUp = () => {
      setIsDragging(false);

      const rootElement = document.getElementById(SidebarOpenerElementID);
      if (rootElement) {
        const topPosition =
          parseInt(rootElement.style.top || '0', 10) ||
          Math.max(0, Math.floor(rootElement.getBoundingClientRect().top));
        setCookie(COOKIE_TOP_POSITION, String(topPosition));
      }
    };

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, dragOffset]);

  const handleDragStart = (e: React.MouseEvent) => {
    e.preventDefault();

    const rootElement = document.getElementById(SidebarOpenerElementID);
    if (rootElement) {
      const rect = rootElement.getBoundingClientRect();
      setDragOffset({
        y: e.clientY - rect.top,
      });
      setIsDragging(true);
    }
  };

  function handleClose(e: React.MouseEvent): void {
    e.stopPropagation();
    const elt = document.getElementById(SidebarOpenerElementID);
    if (elt) {
      const hideUntil = Date.now() + BUTTON_SNOOZE_TIME;
      setCookie(COOKIE_HIDE_UNTIL, String(hideUntil));
      elt.style.display = 'none';
    }
  }

  const handleMainClick = async () => {
    chrome.runtime.sendMessage({ command: 'openSidePanel' });
  };

  return (
    <section
      className='cursor-pointer transition-all duration-300 ease-in-out'
      style={{
        width: isHovered ? '220px' : '50px', // max-w-md is 220px, square is 50px
        maxWidth: '220px',
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={handleMainClick}
    >
      <TooltipProvider>
        <style
          dangerouslySetInnerHTML={{
            __html:
              "@import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@400;500;600&display=swap');",
          }}
        />

        <div
          className='p-[12px] transition-all duration-300 ease-in-out rounded-l-lg'
          style={{
            backgroundColor: 'white',
            border: '1px solid #FE9659',
            height:
              isHovered && isPinned
                ? '100px'
                : isHovered && !isPinned
                  ? '168px'
                  : '50px',
            width: '100%',
            overflow: 'hidden',
          }}
        >
          {/* Always visible logo section */}
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-2'>
              <img
                src={Logo}
                loading='lazy'
                alt='Drumkit Logo'
                className='w-[24px] h-auto flex-shrink-0'
              />
              {/* Title only shows on hover */}
              {isHovered && (
                <Typography variant='body-sm' className='font-semibold'>
                  Load detected!
                </Typography>
              )}
            </div>

            {/* Right side controls - only show on hover */}
            {isHovered && (
              <div className='flex items-center gap-1'>
                {/* Drag handle */}
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div
                      ref={dragRef}
                      data-drag-handle
                      className='p-1 rounded cursor-grab active:cursor-grabbing'
                      style={{
                        color: 'black',
                        transition: 'opacity 0.2s',
                      }}
                      onMouseDown={handleDragStart}
                      onClick={(e) => {
                        e.stopPropagation();
                      }}
                      onMouseEnter={(e) =>
                        (e.currentTarget.style.opacity = '0.8')
                      }
                      onMouseLeave={(e) =>
                        (e.currentTarget.style.opacity = '1')
                      }
                    >
                      <GripVertical size={16} />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent
                    side='left'
                    className='text-black text-xs border-black'
                  >
                    Drag to move
                  </TooltipContent>
                </Tooltip>

                {/* Close button */}
                <Tooltip>
                  <TooltipTrigger asChild>
                    <XIcon
                      style={{
                        width: '20px',
                        height: '20px',
                        cursor: 'pointer',
                        color: 'black',
                        transition: 'opacity 0.2s',
                      }}
                      onClick={handleClose}
                      onMouseEnter={(e) =>
                        (e.currentTarget.style.opacity = '0.8')
                      }
                      onMouseLeave={(e) =>
                        (e.currentTarget.style.opacity = '1')
                      }
                    />
                  </TooltipTrigger>
                  <TooltipContent
                    side='left'
                    className='text-black text-xs border-black'
                  >
                    Clear Suggestion
                  </TooltipContent>
                </Tooltip>
              </div>
            )}
          </div>

          {/* Expanded content only shows on hover */}
          {isHovered && (
            <>
              <Button
                buttonNamePosthog='Click to open Drumkit'
                className='w-full text-center h-[44px] mt-[8px] mb-[4px] text-sm'
              >
                Open Drumkit
              </Button>

              {!isPinned && (
                <p className='text-xs italic mt-1' style={{ color: 'black' }}>
                  💡 Pro Tip: Pin the extension by clicking the 🧩 extension
                  icon on the top right and then clicking the 📌 icon next to
                  Drumkit.
                </p>
              )}
            </>
          )}
        </div>
      </TooltipProvider>
    </section>
  );
}
