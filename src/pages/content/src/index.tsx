import { createRoot } from 'react-dom/client';

import * as InboxSDK from '@inboxsdk/core';
import { animate } from 'animejs';
import refreshOnUpdate from 'virtual:reload-on-update-in-view';

import isProd from '@utils/isProd';
import { initializeSentry } from '@utils/sentry/getSentry';

import { DrumkitPlatform } from 'contexts/sidebarStateContext';
import getEmail from 'lib/api/getEmail';
import { createTMSInstance, determineTMSWebsite } from 'lib/hosts/interface';
import {
  enforceStyles,
  removeStyles,
  startObserving,
  stopObserving,
} from 'lib/hosts/relayStyling';
import { CustomHost } from 'types/DrumkitHosts';
import { initialIngestionMessage } from 'types/IngestionMessage';
import { Maybe } from 'types/UtilityTypes';
import { TMS } from 'types/enums/Integrations';
import captureException from 'utils/captureException';

import SidebarOpener, { SidebarOpenerElementID } from './SidebarOpener';
import { AppGmail, AppTMS } from './app';
import getRelayAntdCompatibilityStyles from './utils/relay-antd-compatibility';

// Global var so show/hide sidebar functions can modify DOM's classes/styling
let pageContainer: Maybe<HTMLElement>;

const isGmail = location.href.startsWith('https://mail.google.com');

// We have to wait for Gmail's DOM to load in order to check if it's a delegated inbox
if (isGmail) {
  document.onreadystatechange = () => {
    if (document.readyState == 'complete') {
      init();

      const isDelegated = document.querySelectorAll(
        "a[aria-label*='Delegated']"
      ).length;
      if (isDelegated) {
        // Here have to manually watch for navigation changes since there's no InboxSDK event
        window.navigation.addEventListener('navigate', () => {
          const hasDrumkit = document.getElementById(
            'drumkit-content-view-root'
          );
          if (hasDrumkit) {
            hasDrumkit.remove();
          }

          // Reload Drumkit on the next JS event loop tick
          setTimeout(() => init());
        });
      }
    }
  };
} else {
  init();
}

function findAndExtractTurvoProNumber(): string | null {
  const spans = document.querySelectorAll(
    'span.tu-breadcrumbs-caption.fleet-body-bold'
  );

  for (const span of spans) {
    const textContent = span.textContent?.trim() || '';
    const match = textContent.match(/^#(.+)$/);
    if (match) {
      return match[1];
    }
  }
  return null;
}

function findAndExtractNewAljexProNumber(): string | null {
  if (document.querySelector('div[data-v-4faa5280]')) {
    const allDivs = document.querySelectorAll('div[data-v-4faa5280]');
    for (const div of allDivs) {
      if (div.textContent?.includes('Pro#')) {
        const textContent = div.textContent || '';
        const match = textContent.match(/Pro#\s*(\d+)/);
        return match ? match[1] : null;
      }
    }
  }
  return null;
}

function startProNumberObserver(
  extractProNumber: () => string | null,
  sourceName: string
): () => void {
  let currentProNumber: string | null = null;
  let observer: MutationObserver | null = null;

  function handleProChange(newProNumber: string | null) {
    if (newProNumber !== currentProNumber) {
      currentProNumber = newProNumber;

      chrome.runtime
        .sendMessage({
          command: 'UpdateParsedIdFromContentScript',
          parsedId: newProNumber,
          source: sourceName,
        })
        .catch((err) => console.error('Error sending Pro# update:', err));
    }
  }

  const stableContainer = document.body;

  observer = new MutationObserver((mutations) => {
    for (const mutation of mutations) {
      if (mutation.type === 'childList') {
        const newProNumber = extractProNumber();
        handleProChange(newProNumber);
      }
    }
  });

  observer.observe(stableContainer, {
    childList: true,
    subtree: true,
    characterData: false,
  });

  return () => {
    if (observer) observer.disconnect();
  };
}

async function init() {
  const isDelegatedAccount = document.querySelectorAll(
    "a[aria-label*='Delegated']"
  ).length;
  let drumkitSidebarElement = document.getElementById(
    'drumkit-content-view-root'
  );

  if (
    isGmail &&
    !isDelegatedAccount &&
    !drumkitSidebarElement &&
    !location.href.includes('popout')
  ) {
    try {
      await startDrumkitUsingInboxSDK();
    } catch {
      console.log('unhandled!!');
    }

    return;
  }

  // Automatically start Drumkit on certain host pages, such as Relay planning board
  const currentHost = determineTMSWebsite();
  if (
    currentHost &&
    createTMSInstance(currentHost).shouldAutomaticallyOpenDrumkit()
  ) {
    // this will inject sidebar into sites that require injection instead of Google sidepanel (Currently Relay, Delegated Gmail)
    startDrumkitWithoutInboxSDK();

    // Aljex PRO number observer, only for the new Aljex website (aljex.descartes.com). Old Aljex has separate logic
    if (
      currentHost === TMS.Aljex &&
      location.href.includes('aljex.descartes.com')
    ) {
      const cleanup = startProNumberObserver(
        findAndExtractNewAljexProNumber,
        'NewAljexMutationObserver'
      );
      (window as any).cleanupNewAljexProNumberObserver = cleanup;
    }

    if (currentHost === TMS.Turvo) {
      const cleanup = startProNumberObserver(
        findAndExtractTurvoProNumber,
        'TurvoMutationObserver'
      );
      (window as any).cleanupTurvoProNumberObserver = cleanup;
    }

    // TODO: if gmail or delegated gmail, do not do this
    injectDrumkitSidebarOpener();
  }

  chrome.runtime.onMessage.addListener((msg, _send, sendResponse) => {
    if (isDelegatedAccount) {
      drumkitSidebarElement =
        document.getElementById('drumkit-content-view-root') ?? null;
    }

    if (msg.command === 'DrumkitIconClicked') {
      if (!drumkitSidebarElement) {
        startDrumkitWithoutInboxSDK();
        return;
      }

      const isSidebarHidden =
        window
          .getComputedStyle(drumkitSidebarElement)
          .getPropertyValue('visibility') === 'hidden';

      if (!isSidebarHidden) {
        hideSidebar();
      } else {
        animate(drumkitSidebarElement, {
          translateX: 0,
          duration: 150,
          easing: 'cubicBezier(.5, .05, .1, .3)',
          begin: () => {
            if (drumkitSidebarElement) {
              drumkitSidebarElement.style.visibility = 'visible';
            }
          },
        });

        if (pageContainer && !isGmail) {
          enforceStyles(pageContainer);
          startObserving(pageContainer);
        }
      }
    }

    // Sent from background script
    if (msg.command === 'ParseNewAljexPro') {
      const parsedId = findAndExtractNewAljexProNumber();

      chrome.runtime
        .sendMessage({
          command: 'UpdateParsedIdFromContentScript',
          parsedId: parsedId,
          source: 'NewAljexManualParse',
        })
        .catch((err) => console.error('Error sending parsed Pro#:', err));
    }

    if (msg.command === 'ParseTurvoPro') {
      const parsedId = findAndExtractTurvoProNumber();

      chrome.runtime
        .sendMessage({
          command: 'UpdateParsedIdFromContentScript',
          parsedId: parsedId,
          source: 'TurvoManualParse',
        })
        .catch((err) => console.error('Error sending parsed Pro#:', err));
    }

    // Needed due to chrome know the message was handled
    sendResponse();
  });
}

function injectDrumkitSidebarOpener() {
  // Create root element for the popup
  const rootElement = document.createElement('div');
  rootElement.id = SidebarOpenerElementID;
  rootElement.classList.add('drumkit-popup-discrete-root-element');

  rootElement.style.position = 'fixed';
  rootElement.style.top = '10px';
  rootElement.style.right = '0px';
  rootElement.style.zIndex = '10000';
  rootElement.style.display = 'none';

  // Create Shadow DOM for isolation from host page
  const shadowRoot = rootElement.attachShadow({ mode: 'open' });

  document.body.append(rootElement);

  // Inject Tailwind CSS into the Shadow DOM, otherwise the shadown DOM won't have our styling
  const tailwindLink = document.createElement('link');
  tailwindLink.rel = 'stylesheet';
  tailwindLink.href = chrome.runtime.getURL('assets/css/App.chunk.css');
  shadowRoot.appendChild(tailwindLink);

  const container = document.createElement('div');
  container.id = 'drumkit-popup-view-root';
  container.classList.add('drumkit-popup-discrete-root-element');

  shadowRoot.appendChild(container);

  createRoot(container).render(<SidebarOpener />);
}

/**
 * @description
 * Renders Drumkit sidebar in Gmail using InboxSDK
 */
async function startDrumkitUsingInboxSDK() {
  if (isProd()) {
    initializeSentry();
  }

  const sdk = await InboxSDK.load(2, 'sdk_beacon_c31f77b1e0', {
    appName: 'Drumkit',
  });

  const inboxEmailAddress = sdk.User.getEmailAddress();

  sdk.Conversations.registerThreadViewHandler(async (threadView) => {
    try {
      refreshOnUpdate('pages/content');

      const root = document.createElement('div');
      root.id = 'drumkit-content-view-root';

      const rootIntoShadow = document.createElement('div');
      rootIntoShadow.id = 'shadow-root';

      const shadowRoot = root.attachShadow({ mode: 'open' });
      shadowRoot.appendChild(rootIntoShadow);

      const thread = await threadView.getThreadIDAsync();

      const ingestionMessage = initialIngestionMessage;

      // If in dev, then send the last message in the thread for ingestion purposes.
      // Disable in prod as it would be slow.
      let lastMessageId = '';
      if (!isProd()) {
        const messages = await threadView.getMessageViewsAll();
        if (messages.length > 0) {
          const lastMessage = messages.pop()!;
          lastMessageId = await lastMessage
            .getMessageIDAsync()
            .then((value) => {
              return document
                .querySelector(`[data-legacy-message-id='${value}']`)!
                .getAttribute('data-legacy-message-id')!;
            });
          const recipients = await lastMessage.getRecipientsFull();
          const sender = lastMessage.getSender();

          ingestionMessage.messageID = lastMessageId;
          ingestionMessage.fromName = sender.name;
          ingestionMessage.fromEmail = sender.emailAddress;
          ingestionMessage.toName = recipients[0].name;
          ingestionMessage.toEmail = recipients[0].emailAddress;
          ingestionMessage.subject = threadView.getSubject();
          ingestionMessage.body = lastMessage.getBodyElement().textContent!;
          ingestionMessage.date = lastMessage.getDateString();
        }
      }

      createRoot(rootIntoShadow).render(
        <AppGmail
          threadId={thread}
          ingestionMessage={ingestionMessage}
          inboxEmailAddress={inboxEmailAddress}
        />
      );
      threadView.addSidebarContentPanel({
        id: '1',
        title: 'Drumkit',
        iconUrl: 'https://drumkit-public.s3.amazonaws.com/Drumkit+Icon.png',
        el: rootIntoShadow,
        orderHint: 1,
        hideTitleBar: true,
      });
    } catch (error) {
      captureException(error);
    }
  });
}

/**
 * @description
 * (Legacy) Renders Drumkit sidebar in Relay TMS and Gmail Delegated Inboxes via HTML injection.
 */
async function startDrumkitWithoutInboxSDK() {
  if (isProd()) {
    initializeSentry();
  }

  const currentTMSSite = determineTMSWebsite();
  const isRelay = currentTMSSite === TMS.Relay;
  const isDelegatedGmail = currentTMSSite === CustomHost.DelegatedGmail;

  // Only allow Relay for now
  if (!isRelay && !isDelegatedGmail) {
    return;
  }

  const tms = createTMSInstance(currentTMSSite);
  const id: string[] = [];
  if (isDelegatedGmail) {
    const threadId = document
      .querySelector('h2[data-legacy-thread-id]')
      ?.getAttribute('data-legacy-thread-id');
    if (!threadId) return;

    const res = await getEmail(threadId);
    if (res.isOk() && res?.value?.freightTrackingIDs?.length) {
      id.push(...res.value.freightTrackingIDs);
    }
  } else {
    // Try parsing both IDs because depending on the TMS webpage we're on,
    // one may be available and the other night
    const parsedId = tms.parseExternalTMSID() ?? tms.parseFreightTrackingID;
    if (parsedId) {
      id.push(parsedId);
    }
  }

  if (!isDelegatedGmail) {
    // This root element should be adapted depending on the website
    // We can have something more sophiscated later. We don't need fanciness atm.
    pageContainer = document.querySelector('body');

    if (!pageContainer) return;

    pageContainer.classList.add('drumkit-sidebar-modifier');
    // Initial enforcement of styles except for Gmail
    enforceStyles(pageContainer);
    startObserving(pageContainer);
  }

  const rootElement = document.createElement('div');
  rootElement.id = 'drumkit-content-view-root';
  rootElement.classList.add('drumkit-discrete-root-element');

  // Attach a shadow DOM to the root element
  const shadowRoot = rootElement.attachShadow({ mode: 'open' });

  document.body.append(rootElement);

  // Inject Tailwind CSS into the Shadow DOM
  const tailwindLink = document.createElement('link');
  tailwindLink.rel = 'stylesheet';
  tailwindLink.href = chrome.runtime.getURL('assets/css/App.chunk.css');
  shadowRoot.appendChild(tailwindLink);

  // Inject Ant Design compatibility styles, since full Antd CSS isn't injectable
  const antdStyle = document.createElement('style');
  antdStyle.textContent = getRelayAntdCompatibilityStyles();
  shadowRoot.appendChild(antdStyle);

  createRoot(shadowRoot).render(
    <AppTMS
      proNumbers={id}
      wrapperPlatform={
        isRelay ? DrumkitPlatform.Relay : DrumkitPlatform.DelegatedGmail
      }
    />
  );
}

export function hideSidebar() {
  const drumkitSidebarElement = document.getElementById(
    'drumkit-content-view-root'
  );

  if (!drumkitSidebarElement) {
    return;
  }

  const drumkitSidebarWidth = drumkitSidebarElement.offsetWidth;

  animate(drumkitSidebarElement, {
    translateX: drumkitSidebarWidth,
    duration: 150,
    easing: 'cubicBezier(.5, .05, .1, .3)',
    complete: () => {
      drumkitSidebarElement.style.visibility = 'hidden';
    },
  });

  // Remove custom class and styling when hiding sidebar
  stopObserving();
  removeStyles(pageContainer);
}
