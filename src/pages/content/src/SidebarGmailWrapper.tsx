import { useContext, useEffect } from 'react';

import SearchBar from 'components/SearchBar';
import Titlebar, { TitlebarButtons } from 'components/Titlebar';
import SidebarLoader from 'components/loading/SidebarLoader';
import { SidebarViewContext } from 'contexts/sidebarViewContext';
import useFetchEmail from 'hooks/useFetchEmail';
import { useServiceFeatures } from 'hooks/useServiceContext';
import LoadsSidebar from 'pages/LoadView/LoadsSidebar';
import QuoteSidebar from 'pages/QuoteView/QuoteSidebar';
import SidebarView from 'types/enums/SidebarView';
import { getViewBasedOnEmail } from 'utils/getViewBasedOnEmail';

import { LoadSearchProvider } from '../../../alexandria/contexts/loadSearchContext';
import { InboxContext } from './InboxContext';

export default function SidebarGmailWrapper() {
  const { currentView, setCurrentView } = useContext(SidebarViewContext);
  const { threadId, ingestionMessage } = useContext(InboxContext);
  const { email, isLoading } = useFetchEmail(threadId, ingestionMessage);

  const { isLoading: isServiceFeaturesLoading, serviceFeaturesEnabled } =
    useServiceFeatures();

  useEffect(() => {
    // Skip getting/setting default view if service features are still loading
    if (isServiceFeaturesLoading) {
      return;
    }

    const defaultView = getViewBasedOnEmail(email, serviceFeaturesEnabled);

    if (currentView !== defaultView) {
      setCurrentView(defaultView);
    }
  }, [email, serviceFeaturesEnabled]);

  if (isLoading || isServiceFeaturesLoading) {
    return <SidebarLoader />;
  }

  return (
    <LoadSearchProvider
      initialFreightTrackingIDs={email?.freightTrackingIDs || []}
    >
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          overflowY: 'auto',
          overflowX: 'hidden',
        }}
        id='drumkit-content-view-root'
      >
        <Titlebar>
          <TitlebarButtons hideSearchBar />
          {currentView === SidebarView.Loads && <SearchBar />}
        </Titlebar>

        {currentView === SidebarView.Quote ? (
          <QuoteSidebar email={email} />
        ) : (
          currentView === SidebarView.Loads && <LoadsSidebar email={email} />
        )}
      </div>
    </LoadSearchProvider>
  );
}
