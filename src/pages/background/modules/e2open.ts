import * as Sentry from '@sentry/browser';

import {
  QuoteSidebarPortName,
  UpdateQuoteRequestDataAction,
} from 'constants/BackgroundScript';
import { CreateQuoteRequestSuggestionRequest } from 'lib/api/createQuoteRequestSuggestion';
import { E2OpenSubmitAction } from 'lib/hosts/quoting/e2open';
import { PortalParseQuoteRequestResult } from 'types/ChromeScript';
import { Address, TransportType } from 'types/QuoteRequest';
import { Maybe, Undef } from 'types/UtilityTypes';
import {
  PortalActionResult,
  SubmitQuoteToPortalData,
} from 'types/chromescript/QuotingPortal';
import { SidepanelMessage } from 'types/chromescript/util';
import { QuotingPortals } from 'types/enums/Integrations';
import { SuggestionSourceCategories } from 'types/suggestions/QuoteSuggestions';

import { isSuppressedSidepanelError } from './util';

let e2openParsedQuoteRequestData: PortalParseQuoteRequestResult = {
  data: null,
};

// When the sidepanel first connects, send the initial data to QuoteSidebar
chrome.runtime.onConnect.addListener(async (port) => {
  if (port.name.startsWith(QuoteSidebarPortName)) {
    // Extract tab ID from port name (format: "QuoteSidebarPortName-{tabId}")
    const tabIdMatch = port.name.match(/-(\d+)$/);
    const targetTabId = tabIdMatch ? parseInt(tabIdMatch[1], 10) : undefined;

    if (targetTabId) {
      await chrome.runtime.sendMessage({
        action: UpdateQuoteRequestDataAction,
        data: e2openParsedQuoteRequestData.data,
        targetTabId: targetTabId,
      } as SidepanelMessage);
    }
  }
});

// When user navigates to E2Open tab, inject script to parse quote request data
chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
  try {
    // IMPORTANT: Only continue when the page is fully loaded
    if (changeInfo.status !== 'complete') {
      return;
    }

    if (!isE2OpenQuotePage(tab.url)) {
      return;
    }

    // Call parseQuoteRequestData() to get the quote request data
    // Unlike FreightView, we don't need to attach the script to the window then call it since we need it only once
    const result = await chrome.scripting.executeScript({
      target: { tabId },
      func: parseQuoteRequestData,
    });
    const resultValue = result?.[0]?.result;

    if (
      resultValue &&
      resultValue instanceof Error &&
      !isSuppressedSidepanelError(resultValue.message.toLowerCase())
    ) {
      Sentry.captureException(
        new Error('Error parsing E2open quote request data: ' + resultValue)
      );

      return;
    }

    if (result?.[0]?.result !== undefined) {
      // Store the initially parsed quote request data for if the sidepanel is opened after the tab changes
      // We allow null to handle when user navigates away from page with a quote request
      e2openParsedQuoteRequestData = result[0]
        .result as PortalParseQuoteRequestResult;

      await chrome.runtime.sendMessage({
        action: UpdateQuoteRequestDataAction,
        data: result?.[0]?.result ?? null,
        targetTabId: tabId,
      } as SidepanelMessage);
    }
  } catch (error) {
    if (
      error instanceof Error &&
      !isSuppressedSidepanelError(error.message.toLowerCase())
    ) {
      Sentry.captureException(
        'Error parsing E2Open quote request data: ' + error
      );
    }
  }
});

/**
 * Listen for messages to submit a quote to E2Open.
 */
chrome.runtime.onMessage.addListener(
  (message: SidepanelMessage, _, sendResponse) => {
    if (message.action === E2OpenSubmitAction) {
      handleE2OpenSubmit(message, sendResponse);
      return true; // Indicates async response
    }
  }
);

async function handleE2OpenSubmit(
  message: SidepanelMessage,
  sendResponse: (response: any) => void
) {
  try {
    const targetTabId = message.targetTabId;
    if (targetTabId === undefined) {
      sendResponse({
        success: false,
        error: 'No target tab ID provided.',
      });
      return;
    }

    const tab = await chrome.tabs.get(targetTabId as number);
    if (!tab?.id || !isE2OpenQuotePage(tab.url)) {
      sendResponse({ success: false, error: 'E2Open tab not found.' });
      return;
    }

    // Inject script to submit the quote
    const result = await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      args: [message.data as SubmitQuoteToPortalData],
      func: submitQuoteToE2open,
    });

    const scriptResult = result?.[0]?.result;
    if (scriptResult) {
      sendResponse(scriptResult);
    } else {
      sendResponse({
        success: false,
        error: 'Script executed but returned no result.',
      });
    }
  } catch (err: any) {
    sendResponse({ success: false, error: err?.message ?? String(err) });
  }
}

export const parseQuoteRequestData = (): PortalParseQuoteRequestResult => {
  try {
    /**
     * Parses quote request data from E2Open's simple offer rate submission form
     * (demo: https://www.loom.com/share/314fca3924104e30a01b73c2a0912024?sid=4cf36c48-82b8-4578-b383-14eb087f156d)
     */
    function parseQuoteRequestData_Version1Simple(): PortalParseQuoteRequestResult {
      try {
        /**
         * Finds an element by its ID suffix, regardless of prefix
         * @param suffix - The suffix of the ID to find (e.g. 'loadid_company_offerdate0')
         * @returns The found element or null
         */
        function findElementBySuffix(suffix: string): Maybe<Element> {
          const elt = document.querySelector(`[id$="${suffix}"]`);
          return elt;
        }

        /**
         * Parses a location string from E2Open format into an Address object.
         * Format: "COMPANY NAME<br>CITY<br>STATE ZIP US"
         */
        function parseLocationString(
          locationHtml: Undef<string>
        ): Partial<Address> {
          if (!locationHtml) return {};

          // Split by <br> and clean up HTML entities
          const lines = locationHtml
            .split('<br>')
            .map((line) => line.replace(/&nbsp;/g, ' ').trim());

          if (lines.length < 3) return { addressLine1: locationHtml };

          // First line is company name
          const companyName = lines[0];

          // Second line is city
          const city = lines[1];

          // Third line contains state and zip
          const stateZipMatch = lines[2].match(
            /([A-Z]{2})\s*(\d{5}|[a-zA-Z0-9]{3}\s*[a-zA-Z0-9]{3})/
          );

          if (stateZipMatch) {
            return {
              addressLine1: companyName,
              city,
              state: stateZipMatch[1],
              zip: stateZipMatch[2],
            };
          }

          return { addressLine1: locationHtml };
        }

        /**
         * Parses a date string from E2Open format.
         * Format: "MM/DD"
         */
        function parseDateString(dateText: Undef<string>): Undef<Date> {
          if (!dateText) return undefined;

          const [month, day] = dateText.split('/').map(Number);
          if (!month || !day) return undefined;

          const year = new Date().getFullYear();
          const date = new Date(year, month - 1, day);

          // If the date has already passed this year, roll over to next year
          const now = new Date();
          if (date.getTime() < now.setHours(0, 0, 0, 0)) {
            date.setFullYear(year + 1);
          }

          return date;
        }

        /**
         * Maps E2Open equipment and temperature types to our TransportType enum
         */
        function mapEquipmentType(
          equipmentText: Undef<string>,
          tempText: Undef<string>
        ): TransportType {
          tempText = tempText?.toLowerCase().trim();
          // Check temp text first
          if (tempText && tempText !== '--' && tempText !== '') {
            if (/amb(ient)?|dry/i.test(tempText)) return 'VAN' as TransportType;
            // Assume reefer otherwise (known formats: "FROZEN", "REF 35F")
            return 'REEFER' as TransportType;
          }

          if (!equipmentText) return 'VAN' as TransportType;
          equipmentText = equipmentText.toLowerCase();

          if (equipmentText.includes('box'))
            return 'BOX TRUCK' as TransportType;
          if (/reef(er)?/i.test(equipmentText))
            return 'REEFER' as TransportType;
          if (/frozen?/i.test(equipmentText)) return 'REEFER' as TransportType;
          if (/hot[-_\s]*shot/i.test(equipmentText))
            return 'HOTSHOT' as TransportType;
          if (/flat[-_\s]*(bed)?/i.test(equipmentText))
            return 'FLATBED' as TransportType;

          return 'VAN' as TransportType;
        }

        // Find the main table row containing the quote request data
        const row = document.querySelector('td.resultrow2')?.closest('tr');
        if (!row) {
          return {
            data: null,
            error: 'Row containing td.resultrow2 not found',
          };
        }

        // Parse TMS ID and Shipper
        const tmsIdShipperCell = findElementBySuffix(
          'loadid_company_offerdate0'
        );
        const tmsIdShipperText = tmsIdShipperCell?.textContent?.trim() || '';
        const [tmsId, shipper] = tmsIdShipperText
          .split('\n')
          .map((s) => s.trim());
        const sourceExternalID = `${shipper}-${tmsId}`;

        // Parse equipment type
        const equipmentText =
          findElementBySuffix('equipment_pallet0')?.textContent?.trim() || '';

        const [_, _w, _d, tempText] =
          findElementBySuffix('weight_dist_temp0')
            ?.textContent?.split('\n')
            .map((s) => s.trim())
            .filter((s) => s.length > 0) || [];
        const transportType = mapEquipmentType(equipmentText, tempText);

        // Parse dates
        const pickDateCell = findElementBySuffix('pickdate0');
        const dropDateCell = findElementBySuffix('dropdate0');
        const pickupDate = parseDateString(pickDateCell?.textContent?.trim());
        const deliveryDate = parseDateString(dropDateCell?.textContent?.trim());

        // Parse locations
        const originCell = findElementBySuffix('origin0');
        const destinationCell = findElementBySuffix('destination0');
        const pickup = parseLocationString(originCell?.innerHTML);
        const delivery = parseLocationString(destinationCell?.innerHTML);

        // Parse distance
        const weightDistCell = findElementBySuffix('stop_weight_dist_temp0');
        const weightDistText = weightDistCell?.textContent?.trim() || '';
        const [_numStops, _weight, distanceStr] = weightDistText
          .split('\n')
          .map((s) => s.trim())
          .filter((s) => s.length > 0)
          .map((s) => {
            const match = s.match(/[\d,.]+/);
            return match ? match[0].replace(/[^\d.]/g, '') : '';
          });

        const distance = parseFloat(distanceStr?.split(' ')?.[0]) || null;

        const res: CreateQuoteRequestSuggestionRequest = {
          customer: { name: shipper },
          customerExternalTMSID: '',
          transportType,
          pickupCity: pickup.city || '',
          pickupState: pickup.state || '',
          pickupZip: pickup.zip || '',
          pickupDate: pickupDate ? pickupDate.toISOString() : null,
          deliveryCity: delivery.city || '',
          deliveryState: delivery.state || '',
          deliveryZip: delivery.zip || '',
          deliveryDate: deliveryDate ? deliveryDate.toISOString() : null,
          sourceCategory:
            'quoting-portal' as SuggestionSourceCategories.QuotingPortal,
          source: 'e2open-quoting' as QuotingPortals,
          sourceExternalID,
          sourceURL: window.location.href,
          htmlSnippet: row.innerHTML,
          distanceMiles: distance,
          fuelSurchargePerMile: null,
          fuelSurchargeTotal: null,
        };

        return { data: res };
      } catch (error: any) {
        return { data: null, error };
      }
    }

    function parseQuoteRequestData_Version2Detailed(): PortalParseQuoteRequestResult {
      /**
       * Parses a location string from E2Open format into an Address object.
       * Format: "COMPANY NAME - LOCATION CITY, STATE ZIP US"
       */
      function parseLocationString(
        locationHtml: Undef<string>
      ): Partial<Address> {
        if (!locationHtml) return {};

        // Extract the city, state, and zip from the second line after <br>
        const lines = locationHtml.split('<br>');
        if (lines.length < 2) return { addressLine1: locationHtml };

        // Get the location line and clean up HTML entities
        const locationLine = lines[1].replace(/&nbsp;/g, ' ').trim();

        // Match city, state, zip format
        // Example: "GRAND RAPIDS, MI 49507 US"
        const match = locationLine.match(
          /([^,]+),\s*([A-Z]{2})\s*(\d{5}|[a-zA-Z0-9]{3}\s*[a-zA-Z0-9]{3})/
        );
        if (match) {
          return {
            city: match[1].trim(),
            state: match[2].trim(),
            zip: match[3].trim(),
          };
        }
        return { addressLine1: locationHtml };
      }

      /**
       * Parses a date string from E2Open format.
       * Format: "MM/DD"
       */
      function parseDateString(dateText: Undef<string>): Undef<Date> {
        if (!dateText) return undefined;

        const [month, day] = dateText.split('/').map(Number);
        if (!month || !day) return undefined;

        const year = new Date().getFullYear();
        const date = new Date(year, month - 1, day);

        // If the date has already passed this year, roll over to next year
        const now = new Date();
        if (date.getTime() < now.setHours(0, 0, 0, 0)) {
          date.setFullYear(year + 1);
        }

        return date;
      }

      /**
       * Maps E2Open equipment and temperature types to our TransportType enum
       */
      function mapEquipmentType(
        equipmentText: Undef<string>,
        tempText: Undef<string>
      ): TransportType {
        tempText = tempText?.toLowerCase().trim();
        // Check temp text first
        if (tempText && tempText !== '--' && tempText !== '') {
          if (/amb(ient)?|dry/i.test(tempText)) return 'VAN' as TransportType;
          // Assume reefer otherwise (known formats: "FROZEN", "REF 35F")
          return 'REEFER' as TransportType;
        }

        if (!equipmentText) return 'VAN' as TransportType;
        equipmentText = equipmentText.toLowerCase();

        if (equipmentText.includes('box')) return 'BOX TRUCK' as TransportType;
        if (/reef(er)?/i.test(equipmentText)) return 'REEFER' as TransportType;
        if (/frozen?/i.test(equipmentText)) return 'REEFER' as TransportType;
        if (/hot[-_\s]*shot/i.test(equipmentText))
          return 'HOTSHOT' as TransportType;
        if (/flat[-_\s]*(bed)?/i.test(equipmentText))
          return 'FLATBED' as TransportType;

        return 'VAN' as TransportType;
      }

      try {
        // Find the main table row containing the quote request data
        const row = document.querySelector('tr.resultrow2');
        if (!row)
          return {
            data: null,
            error: 'Row containing tr.resultrow2 not found',
          };

        // Parse TMS ID and Shipper
        const tmsIdShipperCell = row.querySelector(
          '#test_loadid_company_offerdate'
        );
        const tmsIdShipperText = tmsIdShipperCell?.textContent?.trim() || '';
        const [tmsId, shipper] = tmsIdShipperText
          .split('\n')
          .map((s) => s.trim());
        const sourceExternalID = `${shipper}-${tmsId}`;

        // Parse equipment type
        const equipmentText =
          row.querySelector('#test_equipment_pallet')?.textContent?.trim() ||
          '';
        const [_stops, _w, _d, tempText] =
          row
            .querySelector('#test_stop_weight_dist_temp')
            ?.textContent?.split('\n')
            .map((s) => s.trim())
            .filter((s) => s.length > 0) || [];

        const transportType = mapEquipmentType(equipmentText, tempText);

        // Parse dates
        const pickDateCell = row.querySelector('#test_pickdate');
        const dropDateCell = row.querySelector('#test_dropdate');
        const pickupDate = parseDateString(pickDateCell?.textContent?.trim());
        const deliveryDate = parseDateString(dropDateCell?.textContent?.trim());

        // Parse locations
        const originCell = row.querySelector('#test_origin');
        const destinationCell = row.querySelector('#test_destination');
        const pickup = parseLocationString(originCell?.innerHTML);
        const delivery = parseLocationString(destinationCell?.innerHTML);

        // Parse weight and distance for additional context
        const weightDistCell = row.querySelector('#test_stop_weight_dist_temp');
        const weightDistText = weightDistCell?.textContent?.trim() || '';
        // e.g. Weight = 8,123 lb\nDistance = 2,595 mi
        const [_, _weight, distanceStr] = weightDistText
          .split('\n')
          .map((s) => s.trim())
          .filter((s) => s.length > 0)
          .map((s) => {
            const match = s.match(/[\d,.]+/);
            return match ? match[0].replace(/[^\d.]/g, '') : '';
          });

        // Parse fuel surcharge if basis is CPM
        let fuelSurchargePerMile: Maybe<number> = null;
        let fuelSurchargeTotal: Maybe<number> = null;
        const fuelSurchargeRow = Array.from(
          document.querySelectorAll('tr.resultrow1')
        ).find((row) =>
          row
            .querySelector('td:first-child')
            ?.textContent?.trim()
            .toUpperCase()
            .includes('FUEL SURCHARGE')
        );

        if (fuelSurchargeRow) {
          const basisSelect: Maybe<HTMLSelectElement> =
            fuelSurchargeRow.querySelector(
              'select[name="accessorialCharges[0].chargeBasis"]'
            );
          const rateInput: Maybe<HTMLInputElement> =
            fuelSurchargeRow.querySelector(
              'input[name="accessorialCharges[0].rate"]'
            );
          const totalInput: Maybe<HTMLInputElement> =
            fuelSurchargeRow.querySelector(
              'input[name="accessorialCharges[0].grossAmt"]'
            );

          if (basisSelect?.value === 'CPM' && rateInput && totalInput) {
            fuelSurchargePerMile = parseFloat(rateInput.value) || 0;
            fuelSurchargeTotal = parseFloat(totalInput.value) || 0;
          }
        }

        const res: CreateQuoteRequestSuggestionRequest = {
          customer: { name: shipper },
          customerExternalTMSID: '',
          transportType,
          pickupCity: pickup.city || '',
          pickupState: pickup.state || '',
          pickupZip: pickup.zip || '',
          pickupDate: pickupDate ? pickupDate.toISOString() : null,
          deliveryCity: delivery.city || '',
          deliveryState: delivery.state || '',
          deliveryZip: delivery.zip || '',
          deliveryDate: deliveryDate ? deliveryDate.toISOString() : null,
          sourceCategory:
            'quoting-portal' as SuggestionSourceCategories.QuotingPortal,
          source: 'e2open-quoting' as QuotingPortals,
          sourceExternalID,
          sourceURL: window.location.href,
          htmlSnippet: row.innerHTML,
          fuelSurchargePerMile,
          fuelSurchargeTotal,
          distanceMiles: distanceStr ? parseFloat(distanceStr) : null,
        };

        return { data: res };
      } catch (error: any) {
        return { data: null, error };
      }
    }

    if (window.location.href.toLowerCase().includes('makeanoffer')) {
      return parseQuoteRequestData_Version1Simple();
    }

    if (
      window.location.href.toLowerCase().includes('detailedspotmarketoffer.do')
    ) {
      return parseQuoteRequestData_Version2Detailed();
    }

    return {
      data: null,
      error: 'Unsupported quote page: ' + window.location.href,
    };
  } catch (error: any) {
    return { data: null, error };
  }
};

/**
 * Submits a quote to E2open.
 * This function is injected into the page context.
 */
async function submitQuoteToE2open({
  flatRate,
  distance,
  isSubmitOnPortalEnabled,
}: SubmitQuoteToPortalData): Promise<PortalActionResult> {
  /**
   * Waits for a success or error message to appear on the page.
   * @param timeout - The maximum time to wait for a message in milliseconds.
   * @returns A promise that resolves to a PortalActionResult.
   * FIXME: Lagging
   */
  function _waitForConfirmation(timeout = 2000): Promise<PortalActionResult> {
    return new Promise((resolve) => {
      const start = Date.now();
      const interval = setInterval(() => {
        const pageErrorMsg = document.querySelector(
          '.page-message--error.page-message--shown'
        );
        // TODO: Educated guess, determine success component
        const successMsg = document.querySelector(
          '.page-message--success.page-message--shown'
        );

        if (pageErrorMsg) {
          clearInterval(interval);
          resolve({
            success: false,
            partialSuccess: true,
            error:
              pageErrorMsg
                .querySelector('.page-message__message--primary')
                ?.textContent?.trim() ?? 'E2Open returned an error.',
          });
        } else if (successMsg || Date.now() - start > timeout) {
          clearInterval(interval);
          resolve({ success: true });
        }
      }, 200);
    });
  }

  /**
   * Submits a quote to E2Open using the simple offer rate submission form
   * (demo: https://www.loom.com/share/314fca3924104e30a01b73c2a0912024?sid=4cf36c48-82b8-4578-b383-14eb087f156d)
   * @returns A promise that resolves to a PortalActionResult.
   */
  async function submitQuote_Version1Simple(): Promise<PortalActionResult> {
    try {
      if (!flatRate || flatRate <= 0) {
        throw new Error('Flat rate is required.');
      }

      // Find the rate input field - looking for any offerRateBox input
      const rateInput =
        // Make Offer page
        (document.querySelector('input[id^="loadRateBox_"]') ??
          // Modify offer page
          document.querySelector(
            'input[id^="offerRateBox_"]'
          )) as HTMLInputElement;
      if (!rateInput) {
        throw new Error('Rate input field not found.');
      }

      // Format the rate with commas for thousands
      const formattedRate = flatRate.toLocaleString('en-US', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2,
      });

      // Fill in the rate
      rateInput.value = formattedRate;
      rateInput.dispatchEvent(new Event('input', { bubbles: true }));
      rateInput.dispatchEvent(new Event('change', { bubbles: true }));
      rateInput.dispatchEvent(new Event('blur', { bubbles: true }));

      if (!isSubmitOnPortalEnabled) {
        return { success: true };
      }

      // Find and click the submit button
      const submitBtn = document.querySelector(
        'button[name="submitbutton"]'
      ) as HTMLButtonElement;
      if (!submitBtn) {
        return {
          success: false,
          partialSuccess: true,
          error: 'Could not find submit button',
        };
      }

      submitBtn.dispatchEvent(
        new MouseEvent('click', {
          bubbles: true,
          cancelable: true,
          view: window,
        })
      );
      return { success: true };
    } catch (err: any) {
      return { success: false, error: err };
    }
  }

  /**
   * Submits a quote to E2Open using the detailed offer rate submission form
   * (demo: https://www.loom.com/share/f1621c5864004ace9c0008c3c91f61ad?sid=be377d45-22be-4cfd-92c1-0804aab6dfde)
   * @returns A promise that resolves to a PortalActionResult.
   */
  async function submitQuote_Version2Detailed(): Promise<PortalActionResult> {
    try {
      if (!flatRate || flatRate <= 0) {
        throw new Error('Linehaul quote is required.');
      }

      // Find the rate input field
      const rateInput = document.querySelector(
        'input[name="baseCharges[0].rate"]'
      ) as HTMLInputElement;
      if (!rateInput) {
        throw new Error('Rate input field not found.');
      }

      // Find the charge basis select element
      const basisSelect = document.querySelector(
        'select[name="baseCharges[0].chargeBasis"]'
      ) as HTMLSelectElement;
      if (!basisSelect) {
        throw new Error('Charge basis select not found.');
      }

      // Get the selected basis value
      const basis = basisSelect.value;
      let rate: number;

      switch (basis) {
        case 'CPM':
          if (!distance || distance <= 0) {
            throw new Error('Distance is required for CPM basis.');
          }
          // Calculate rate per mile
          rate = flatRate / distance;
          break;
        case 'FLAT':
        case 'FLT':
          // Use flat line haul as is
          rate = flatRate;
          break;
        default:
          throw new Error(
            `Unsupported charge basis: ${basis}. Drumkit supports FLAT and CPM.`
          );
      }

      // Fill in the rate
      rateInput.value = rate.toFixed(2);
      rateInput.dispatchEvent(new Event('input', { bubbles: true }));
      rateInput.dispatchEvent(new Event('change', { bubbles: true }));
      rateInput.dispatchEvent(new Event('blur', { bubbles: true }));

      if (!isSubmitOnPortalEnabled) {
        return { success: true };
      }

      // Find and click the submit button
      const submitBtn = document.querySelector(
        'input[type="button"]#bttnSaveRate'
      ) as HTMLInputElement;
      if (!submitBtn) {
        throw new Error('Could not find submit button');
      }

      submitBtn.dispatchEvent(
        new MouseEvent('click', {
          bubbles: true,
          cancelable: true,
          view: window,
        })
      );

      return { success: true };
    } catch (err: any) {
      return { success: false, error: err };
    }
  }

  if (window.location.href.toLowerCase().includes('makeanoffer')) {
    return submitQuote_Version1Simple();
  }
  if (
    window.location.href.toLowerCase().includes('detailedspotmarketoffer.do')
  ) {
    return submitQuote_Version2Detailed();
  }

  return {
    success: false,
    error: `Unsupported quote page: ${window.location.href}`,
  };
}

/**
 * Checks if a URL is an E2Open quote page.
 */
function isE2OpenQuotePage(url: Undef<string>): boolean {
  if (!url) return false;
  try {
    const parsedUrl = new URL(url);

    return (
      parsedUrl.origin?.includes('na-app.tms.e2open.com') &&
      (parsedUrl.pathname
        .toLowerCase()
        .includes('detailedspotmarketoffer.do') ||
        parsedUrl.pathname.toLowerCase().includes('makeanoffer'))
    );
  } catch (error) {
    Sentry.captureException(
      'Error detecting if URL is E2Open quote page: ' + error
    );
    return false;
  }
}
