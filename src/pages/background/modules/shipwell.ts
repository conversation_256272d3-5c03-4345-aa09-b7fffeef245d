import * as Sentry from '@sentry/browser';

import {
  QuoteSidebarPortName,
  UpdateQuoteRequestDataAction,
} from 'constants/BackgroundScript';
import { CreateQuoteRequestSuggestionRequest } from 'lib/api/createQuoteRequestSuggestion';
import { ShipwellSubmitAction } from 'lib/hosts/quoting/shipwell';
import { PortalParseQuoteRequestResult } from 'types/ChromeScript';
import { Address, QuoteRequest, TransportType } from 'types/QuoteRequest';
import { Maybe } from 'types/UtilityTypes';
import {
  PortalActionResult,
  SubmitQuoteToPortalData,
} from 'types/chromescript/QuotingPortal';
import { SidepanelMessage } from 'types/chromescript/util';
import { QuotingPortals } from 'types/enums/Integrations';
import { SuggestionSourceCategories } from 'types/suggestions/QuoteSuggestions';

/*
 * If user selects a load before opening side panel, we store initially selected quote request
 * and send it when QuoteSidebarPortName port connects (see chrome.runtime.onConnect below)
 */
let shipwellParsedQuoteRequest: Maybe<Partial<QuoteRequest>> = null;

// When the sidepanel first connects, send the initial data to QuoteSidebar
chrome.runtime.onConnect.addListener(async (port) => {
  if (port.name.startsWith(QuoteSidebarPortName)) {
    // Extract tab ID from port name (format: "QuoteSidebarPortName-{tabId}")
    const tabIdMatch = port.name.match(/-(\d+)$/);
    const targetTabId = tabIdMatch ? parseInt(tabIdMatch[1], 10) : undefined;

    if (shipwellParsedQuoteRequest && targetTabId) {
      await chrome.runtime.sendMessage({
        action: UpdateQuoteRequestDataAction,
        data: shipwellParsedQuoteRequest,
        targetTabId: targetTabId,
      } as SidepanelMessage);
    }
  }
});

// When user navigates to Shipwell tab, inject script to listen for quote request changes
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  // IMPORTANT: Only process when the page is fully loaded
  if (changeInfo.status === 'complete') {
    if (tab.url?.includes('app.shipwell.com/load-board/')) {
      chrome.scripting.executeScript({
        target: { tabId: tabId },
        args: [tabId],
        func: registerQuoteRequestChangeListener,
      });
    }
  }
});

/**
 * When user first navigates to Shipwell tab:
 * 1. Attach parseQuoteRequestData and tabId function to window
 * 2. Parse the initially selected quote request's data
 */
chrome.tabs.onUpdated.addListener(async (tabId, _, tab) => {
  try {
    if (!tab.url) return;
    const url = new URL(tab.url);

    const isShipwell =
      url.origin?.includes('app.shipwell.com') &&
      url.pathname?.includes('load-board');
    if (!isShipwell) {
      // If user navigates away from Shipwell tab, remove parseQuoteRequestData from window
      await chrome.scripting.executeScript({
        target: { tabId },
        func: () => {
          delete window.parseQuoteRequestData;
        },
      });
      return;
    }

    // Attach parsing function to window context for use by MutationObserver
    await chrome.scripting.executeScript({
      target: { tabId },
      args: [tabId],
      func: (tabId: number) => {
        window.__tabId = tabId;
        window.parseQuoteRequestData = (): any => {
          // Helper functions defined locally for the injected script
          function parseLocationString(locationText: string | undefined): any {
            if (!locationText) return {};
            const cleaned = locationText.trim();

            // Handle Shipwell format: "Hickory, NC 28602 US" or "Fort Mill, SC 29708 US"
            const shipwellMatch = cleaned.match(
              /^(.+?),\s*([A-Z]{2})\s*(\d{5})\s*US$/
            );
            if (shipwellMatch) {
              return {
                city: shipwellMatch[1].trim(),
                state: shipwellMatch[2],
                zip: shipwellMatch[3],
              };
            }

            // Fallback to general format: "City, ST" or "City, ST ZIP"
            const generalMatch = cleaned.match(
              /(.+),\s*([A-Z]{2})(?:\s*(\d{5}))?/
            );
            if (generalMatch) {
              return {
                city: generalMatch[1].trim(),
                state: generalMatch[2],
                zip: generalMatch[3] || '',
              };
            }

            return { addressLine1: cleaned };
          }

          function parseShipwellDate(
            dateText: string | undefined
          ): Date | undefined {
            if (!dateText) return undefined;
            try {
              const date = new Date(dateText);
              return isNaN(date.getTime()) ? undefined : date;
            } catch {
              return undefined;
            }
          }

          try {
            // Extract load ID from URL (e.g., app.shipwell.com/load-board/ABCD)
            const urlParts = window.location.pathname.split('/');
            const loadId = urlParts[urlParts.length - 1] || '';

            // Find pickup location from dashboard summary
            const pickupElements = document.querySelectorAll(
              '.dashboard-summary__stop-address'
            );
            if (pickupElements.length < 2) {
              return { data: null }; // Need at least pickup and delivery
            }

            const pickupElement = pickupElements[0]; // First element is pickup
            const pickupText = pickupElement?.textContent?.trim() || '';
            const pickupLocation = parseLocationString(pickupText);

            // Find delivery location from dashboard summary
            const deliveryElement = pickupElements[1]; // Second element is delivery
            const deliveryText = deliveryElement?.textContent?.trim() || '';
            const deliveryLocation = parseLocationString(deliveryText);

            // Parse dates from the page (look for date patterns)
            const dateElements = document.querySelectorAll(
              '[class*="date"], [class*="time"]'
            );
            let pickupDate: Date | undefined;
            let deliveryDate: Date | undefined;

            // Try to find pickup and delivery dates
            for (const element of dateElements) {
              const text = element.textContent?.trim();
              if (text) {
                if (text.includes('pickup')) {
                  pickupDate = parseShipwellDate(text);
                } else if (text.includes('delivery')) {
                  deliveryDate = parseShipwellDate(text);
                }
              }
            }

            // Try to find customer name from various possible locations
            const customerElement =
              document.querySelector('.customer-name') ||
              document.querySelector('[class*="customer"]') ||
              document.querySelector('[class*="shipper"]');
            const customerName =
              customerElement?.textContent?.trim() || 'Unknown Customer';

            return {
              data: {
                customerExternalTMSID: loadId,
                transportType: 'VAN',
                pickupCity: pickupLocation.city || '',
                pickupState: pickupLocation.state || '',
                pickupZip: pickupLocation.zip || '',
                pickupDate: pickupDate ? pickupDate.toISOString() : null,
                deliveryCity: deliveryLocation.city || '',
                deliveryState: deliveryLocation.state || '',
                deliveryZip: deliveryLocation.zip || '',
                deliveryDate: deliveryDate ? deliveryDate.toISOString() : null,
                fuelSurchargePerMile: null,
                fuelSurchargeTotal: null,
                distanceMiles: null,
                customer: { name: customerName },
                sourceCategory: 'quoting-portal',
                source: 'shipwell',
                sourceExternalID: `${customerName}-${loadId}`,
                sourceURL: window.location.href,
                htmlSnippet: document.body.innerHTML.substring(0, 1000) || '',
              },
            };
          } catch (error: any) {
            return {
              data: null,
              error: `Error parsing Shipwell quote request: ${error.message}`,
            };
          }
        };
      },
    });

    // Parse initial quote request data
    const parseResult = await chrome.scripting.executeScript({
      target: { tabId },
      func: () => {
        return window.parseQuoteRequestData?.();
      },
    });

    // Store the parsed data for when sidepanel connects
    if (parseResult?.[0]?.result?.data) {
      shipwellParsedQuoteRequest = parseResult[0].result.data;
    }

    // Send to sidepanel if it's already open
    if (parseResult?.[0]?.result?.data) {
      try {
        await chrome.runtime.sendMessage({
          action: UpdateQuoteRequestDataAction,
          data: parseResult[0].result.data,
          targetTabId: tabId,
        });
      } catch (error) {
        // "Receiving end does not exist error" expected if side panel isn't open yet
        if (
          error instanceof Error &&
          !error.message.includes('Receiving end does not exist')
        ) {
          Sentry.captureException('Error sending message to sidepanel' + error);
        }
      }
    }
  } catch (error: any) {
    Sentry.captureException('Error in Shipwell tab update handler: ' + error);
  }
});

/**
 * Registers a MutationObserver to detect when load data changes on the page
 */
function registerQuoteRequestChangeListener(_tabId: number) {
  const callback: MutationCallback = function () {
    try {
      // Check if we're on a load detail page and data is available
      const hasLoadData =
        document.querySelector('.dashboard-summary__stop-address') !== null;

      const parseResult =
        hasLoadData && window.parseQuoteRequestData
          ? (window.parseQuoteRequestData?.() as PortalParseQuoteRequestResult)
          : { data: null };

      if (parseResult?.error) {
        Sentry.captureException(
          'Error parsing Shipwell quote request data: ' + parseResult.error
        );
        return;
      }

      chrome.runtime.sendMessage({
        action: 'updateQuoteRequestData',
        data: parseResult?.data ?? null,
        targetTabId: window.__tabId,
      } as SidepanelMessage);
    } catch (error) {
      Sentry.captureException(
        'Shipwell MutationObserver callback error: ' + error
      );
    }
  };

  const observer = new MutationObserver(callback);

  // Watch for changes in the dashboard summary area
  const targetNode =
    document.querySelector('.dashboard-summary') ||
    document.querySelector('[class*="dashboard"]');

  if (targetNode) {
    observer.observe(targetNode, {
      childList: true,
      subtree: true,
      attributes: true,
    });
  }

  // Initial check after a short delay to ensure DOM is ready
  setTimeout(() => callback([], observer), 1000);
}

/**
 * Listen for messages to submit a quote to Shipwell.
 */
chrome.runtime.onMessage.addListener(
  (message: SidepanelMessage, _, sendResponse) => {
    if (message.action === ShipwellSubmitAction) {
      handleShipwellSubmit(message, sendResponse);
      return true; // Indicates async response
    }
  }
);

async function handleShipwellSubmit(
  message: SidepanelMessage,
  sendResponse: (response: any) => void
) {
  try {
    const targetTabId = message.targetTabId;
    if (targetTabId === undefined) {
      sendResponse({ success: false, error: 'No target tab ID provided.' });
      return;
    }

    const tab = await chrome.tabs.get(targetTabId as number);
    if (!tab?.id || !tab.url?.includes('app.shipwell.com')) {
      sendResponse({ success: false, error: 'Shipwell tab not found.' });
      return;
    }

    // Inject script to fill and submit the quote form
    const result = await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      args: [message.data as SubmitQuoteToPortalData],
      func: submitQuoteToShipwell,
    });

    const scriptResult = result?.[0]?.result;
    if (scriptResult) {
      sendResponse(scriptResult);
    } else {
      sendResponse({
        success: false,
        error: 'Script executed but returned no result.',
      });
    }
  } catch (err: any) {
    sendResponse({ success: false, error: err?.message ?? String(err) });
  }
}

async function submitQuoteToShipwell({
  flatRate,
  isSubmitOnPortalEnabled,
}: SubmitQuoteToPortalData): Promise<PortalActionResult> {
  try {
    // 1. Check if we're on a load detail page
    const hasLoadData = document.querySelector(
      '.dashboard-summary__stop-address'
    );
    if (!hasLoadData) {
      throw new Error('No load data found on this page.');
    }

    // 2. Look for the Quick Quote form or open it
    let quoteForm =
      document.querySelector('.quick-quote-form') ||
      document.querySelector('[data-testid*="quick-quote"]') ||
      document.querySelector('form[action*="quote"]');

    if (!quoteForm) {
      // Try to find and click a quote button to open the form
      const quoteButton =
        document.querySelector('button:contains("Get Quick Quote")') ||
        document.querySelector('[data-testid*="quote-button"]') ||
        document.querySelector('.quote-button') ||
        Array.from(document.querySelectorAll('button')).find((btn) =>
          btn.textContent?.toLowerCase().includes('quote')
        );

      if (quoteButton) {
        (quoteButton as HTMLElement).click();

        // Wait for form to appear
        await new Promise((resolve, reject) => {
          let attempts = 0;
          const maxAttempts = 20;
          const interval = setInterval(() => {
            quoteForm =
              document.querySelector('.quick-quote-form') ||
              document.querySelector('[data-testid*="quick-quote"]') ||
              document.querySelector('form[action*="quote"]');

            if (quoteForm) {
              clearInterval(interval);
              resolve(quoteForm);
            } else if (++attempts >= maxAttempts) {
              clearInterval(interval);
              reject(
                new Error(
                  'Quote form did not appear after clicking quote button.'
                )
              );
            }
          }, 150);
        });
      } else {
        throw new Error('Quote form not found and no quote button available.');
      }
    }

    if (!quoteForm) {
      throw new Error('Quote form not found.');
    }

    // 3. Find and fill the rate input
    const rateInput: HTMLInputElement | null =
      quoteForm.querySelector('input[name*="rate"]') ||
      quoteForm.querySelector('input[name*="price"]') ||
      quoteForm.querySelector('input[name*="amount"]') ||
      quoteForm.querySelector('input[type="number"]') ||
      quoteForm.querySelector('input[placeholder*="rate"]') ||
      quoteForm.querySelector('input[placeholder*="price"]');

    if (!rateInput) {
      throw new Error('Rate input field not found in quote form.');
    }

    // Clear and set the rate value
    rateInput.value = '';
    rateInput.value = flatRate.toString();

    // Trigger events to ensure the form recognizes the input
    rateInput.dispatchEvent(new Event('input', { bubbles: true }));
    rateInput.dispatchEvent(new Event('change', { bubbles: true }));
    rateInput.dispatchEvent(new Event('blur', { bubbles: true }));

    if (!isSubmitOnPortalEnabled) {
      return { success: true };
    }

    // 4. Find and click the submit button
    const submitButton: HTMLElement | null =
      quoteForm.querySelector('button[type="submit"]') ||
      quoteForm.querySelector('button:contains("Submit")') ||
      quoteForm.querySelector('button:contains("Get Quick Quote")') ||
      quoteForm.querySelector('[data-testid*="submit"]') ||
      (Array.from(quoteForm.querySelectorAll('button')).find(
        (btn) =>
          btn.textContent?.toLowerCase().includes('submit') ||
          btn.textContent?.toLowerCase().includes('quote')
      ) as HTMLElement);

    if (!submitButton) {
      return {
        success: false,
        error: 'Submit button not found in quote form.',
        partialSuccess: true,
      };
    }

    if (
      submitButton.hasAttribute('disabled') ||
      submitButton.getAttribute('aria-disabled') === 'true'
    ) {
      return {
        success: false,
        error: 'Submit button is disabled.',
        partialSuccess: true,
      };
    }

    // Click the submit button
    submitButton.dispatchEvent(
      new MouseEvent('click', {
        bubbles: true,
        cancelable: true,
        view: window,
      })
    );

    // 5. Wait for success indication
    function waitForSuccessIndicator(timeout = 5000) {
      return new Promise((resolve) => {
        const start = Date.now();
        const interval = setInterval(() => {
          // Look for success messages or indicators
          const successIndicator =
            document.querySelector('[role="status"]') ||
            document.querySelector('.success-message') ||
            document.querySelector('.quote-submitted') ||
            Array.from(document.querySelectorAll('*')).find(
              (el) =>
                el.textContent?.toLowerCase().includes('quote submitted') ||
                el.textContent?.toLowerCase().includes('bid submitted') ||
                el.textContent?.toLowerCase().includes('success')
            );

          if (successIndicator) {
            clearInterval(interval);
            resolve(true);
          } else if (Date.now() - start > timeout) {
            clearInterval(interval);
            resolve(false);
          }
        }, 200);
      });
    }

    const success = await waitForSuccessIndicator();

    if (success) {
      return { success: true };
    } else {
      return {
        success: false,
        error: 'Form submitted but did not detect success confirmation.',
        partialSuccess: true,
      };
    }
  } catch (err: any) {
    return {
      success: false,
      error: err.message || String(err),
    };
  }
}

/**
 * Exported parsing function for testing and window attachment
 * Parses the quote request data from the selected load on the Shipwell page.
 * @returns A PortalParseQuoteRequestResult containing either the parsed data or an error.
 */
export const parseQuoteRequestData = (): PortalParseQuoteRequestResult => {
  /**
   * Parses a location string into an Address object.
   */
  function parseLocationString(
    locationText: string | undefined
  ): Partial<Address> {
    if (!locationText) return {};
    const cleaned = locationText.trim();
    const match = cleaned.match(
      /(.+),\s*([A-Z]{2})\s*(\d{5}|[a-zA-Z0-9]{3}\s*[a-zA-Z0-9]{3})?/
    );
    if (match) {
      return { city: match[1].trim(), state: match[2], zip: match[3] || '' };
    }
    return { addressLine1: cleaned };
  }

  /**
   * Parses date from Shipwell date format
   */
  function parseShipwellDate(dateText: string | undefined): Date | undefined {
    if (!dateText) return undefined;
    try {
      const date = new Date(dateText);
      return isNaN(date.getTime()) ? undefined : date;
    } catch {
      return undefined;
    }
  }

  try {
    // Extract load ID from URL (e.g., app.shipwell.com/load-board/ABCD)
    const urlParts = window.location.pathname.split('/');
    const loadId = urlParts[urlParts.length - 1] || '';

    // Find pickup location from dashboard summary
    const pickupElements = document.querySelectorAll(
      '.dashboard-summary__stop-address'
    );
    if (pickupElements.length < 2) {
      return { data: null }; // Need at least pickup and delivery
    }

    const pickupElement = pickupElements[0]; // First element is pickup
    const pickupText = pickupElement?.textContent?.trim() || '';
    const pickupLocation = parseLocationString(pickupText);

    // Find delivery location from dashboard summary
    const deliveryElement = pickupElements[1]; // Second element is delivery
    const deliveryText = deliveryElement?.textContent?.trim() || '';
    const deliveryLocation = parseLocationString(deliveryText);

    // Parse dates from the page (look for date patterns)
    const dateElements = document.querySelectorAll(
      '[class*="date"], [class*="time"]'
    );
    let pickupDate: Date | undefined;
    let deliveryDate: Date | undefined;

    // Try to find pickup and delivery dates
    for (const element of dateElements) {
      const text = element.textContent?.trim();
      if (text && (text.includes('Tue Sep') || text.includes('pickup'))) {
        pickupDate = parseShipwellDate(text);
      } else if (
        text &&
        (text.includes('Thu Sep') || text.includes('delivery'))
      ) {
        deliveryDate = parseShipwellDate(text);
      }
    }

    // Try to find customer name from various possible locations
    const customerElement =
      document.querySelector('.customer-name') ||
      document.querySelector('[class*="customer"]') ||
      document.querySelector('[class*="shipper"]');
    const customerName =
      customerElement?.textContent?.trim() || 'Unknown Customer';

    const quoteRequestData: CreateQuoteRequestSuggestionRequest = {
      customerExternalTMSID: loadId,
      transportType: TransportType.VAN, // Default to VAN
      pickupCity: pickupLocation.city || '',
      pickupState: pickupLocation.state || '',
      pickupZip: pickupLocation.zip || '',
      pickupDate: pickupDate ? pickupDate.toISOString() : null,
      deliveryCity: deliveryLocation.city || '',
      deliveryState: deliveryLocation.state || '',
      deliveryZip: deliveryLocation.zip || '',
      deliveryDate: deliveryDate ? deliveryDate.toISOString() : null,
      fuelSurchargePerMile: null,
      fuelSurchargeTotal: null,
      distanceMiles: null,
      customer: { name: customerName },
      sourceCategory: SuggestionSourceCategories.QuotingPortal,
      source: QuotingPortals.Shipwell,
      sourceExternalID: `${customerName}-${loadId}`,
      sourceURL: window.location.href,
      htmlSnippet: document.body.innerHTML.substring(0, 1000) || '',
    };

    return { data: quoteRequestData };
  } catch (error: any) {
    return {
      data: null,
      error: `Error parsing Shipwell quote request: ${error.message}`,
    };
  }
};
