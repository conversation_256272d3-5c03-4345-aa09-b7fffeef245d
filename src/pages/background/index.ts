import {
  captureConsoleIntegration,
  captureException,
  getDefaultIntegrations,
  init,
} from '@sentry/browser';
import reloadOnUpdate from 'virtual:reload-on-update-in-background-script';

import APP_VERSION from '@constants/AppVersion';
import DRUMKIT_AUTH_URL from '@constants/DrumkitAuthUrl';
import EN<PERSON>RONMENT from '@constants/Environment';
import SENTRY_DSN from '@constants/SentryDsn';

import { registerRelayModalListener } from 'lib/hosts/relayStyling';
import { Maybe } from 'types/UtilityTypes';

import './modules/e2open';
import './modules/freightview';
import './modules/shipwell';
import './modules/util';
import { isSuppressedSidepanelError } from './modules/util';

reloadOnUpdate('pages/background');

function initializeSentryForBackground() {
  init({
    dsn: SENTRY_DSN,
    environment: ENVIRONMENT,
    release: `${APP_VERSION}-${ENVIRONMENT}`,
    integrations: [
      captureConsoleIntegration({
        levels: ['error', 'warn'],
      }),
      ...getDefaultIntegrations({}),
    ],
    tracesSampleRate: 0.5,
  });
}

initializeSentryForBackground();

let parsedLoadId: Maybe<string> = '';
const pagesWithoutPro: Set<number> = new Set(); // Track tabs without PRO IDs

const COOKIE_NAME = 'drumkit-auth';

// List of hosts that we disable the side panel on due to legacy HTML injection
// & custom webpage parsing.

const sidepanelBlacklist = ['relaytms.com', 'mail.google.com'];

const activeSidePanelTabs: Set<number> = new Set();

chrome.runtime.onMessage.addListener(function (message, sender) {
  if (message.command === 'openSidePanel') {
    chrome.sidePanel.setOptions({
      tabId: sender.tab!.id,
    });
    chrome.sidePanel.open({
      tabId: sender.tab!.id,
      windowId: sender.tab!.windowId,
    });
  }
  return true;
});

// Returns the extension pin status to SidebarOpener
chrome.runtime.onMessage.addListener(function (message, sender, callback) {
  if (message.command === 'CheckExtensionPin') {
    checkIsPinned(callback);
  }
  return true;
});

// Returns the current tab to SidebarOpener
chrome.runtime.onMessage.addListener(function (message, sender, callback) {
  if (message.command === 'GetCurrentTab') {
    getCurrentTab(callback);
  }
  return true;
});

// Returns the sidepanel open status on the passed in tab to SidebarOpener
chrome.runtime.onMessage.addListener(function (message, sender, callback) {
  if (message.command === 'CheckSidePanelOpen') {
    checkSidePanelOpenOnTab(message.tabId, callback);
  }
  return true;
});

function checkIsPinned(callback: (response?: boolean) => void) {
  chrome.action.getUserSettings((settings) => {
    callback(settings.isOnToolbar);
  });
}

function getCurrentTab(callback: (response?: chrome.tabs.Tab) => void) {
  chrome.tabs.query({ active: true, lastFocusedWindow: true }, (tabs) => {
    if (tabs.length > 0) {
      callback(tabs[0]);
    } else {
      callback();
    }
  });
}

function checkSidePanelOpenOnTab(
  tabId: number,
  callback: (response?: boolean) => void
) {
  callback(activeSidePanelTabs.has(tabId));
}

// When a sidepanel first connects, we parse the PRO ID from the Aljex tab (should work globally for all Aljex boards)
// and send the parsed ID to the sidepanel
// TODO (aaron): add PRO ID parsing for other TMS's here as well
chrome.runtime.onConnect.addListener(async (port) => {
  if (port.name.startsWith('drumkitSidepanel')) {
    const tabId = parseInt(port.name.split('-')[1], 10);
    activeSidePanelTabs.add(tabId);

    try {
      const tab = await chrome.tabs.get(tabId);
      const href = tab.url ?? '';
      if (href.includes('aljex.com')) {
        await handleParseForAljex(tabId);
      } else if (href.includes('aljex.descartes.com')) {
        // New Aljex website
        await handleParseForNewAljex(tabId);
      } else if (href.includes('turvo.com')) {
        await handleParseForTurvo(tabId);
      } else if (href.includes('taicloud.net')) {
        await handleParseForTai(tabId, href);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);

      if (!isSuppressedSidepanelError(errorMessage.toLowerCase())) {
        captureException(
          new Error('error determining host on connect: ' + errorMessage)
        );
      }
    }

    chrome.runtime
      .sendMessage({
        action: 'updateParsedId',
        parsedId: parsedLoadId,
        tabId: tabId,
      } as any)
      .catch((err) => console.error('Error sending initial parsed ID:', err));

    sendSidePanelMessage(true);

    port.onDisconnect.addListener(() => {
      activeSidePanelTabs.delete(tabId);
      sendSidePanelMessage(false);
    });
  }
});

// Notify tabs side panel when side panel is opened/close to toggle popup
function sendSidePanelMessage(isOpen: boolean) {
  chrome.tabs.query({ lastFocusedWindow: true, active: true }, (tabs) => {
    tabs.forEach((tab) => {
      if (tab.id) {
        chrome.tabs
          .sendMessage(tab.id, {
            command: 'SidePanelStateUpdated',
            open: isOpen,
          } as any)
          .catch((err) =>
            console.error(
              `Error sending side panel state to tab ${tab.id}:`,
              err
            )
          );
      }
    });
  });
}

/**
 * Extension reloading is necessary because the browser automatically caches the css.
 * If you do not use the css of the content script, please delete it.
 */

chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === 'inboxsdk__injectPageWorld' && sender.tab) {
    if (chrome.scripting) {
      // MV3
      chrome.scripting.executeScript({
        target: { tabId: sender.tab.id! },
        world: 'MAIN',
        files: ['src/pages/pageWorld/index.js'],
      });
      sendResponse(true);
    } else {
      // MV2 fallback. Tell content script it needs to figure things out.
      sendResponse(false);
    }
    return true;
  }

  if (message.command === 'GetAuthCookie') {
    checkAuthCookies(sendResponse);
    return true;
  }

  if (message.command === 'RemoveAuthCookie') {
    removeAuthCookies(sendResponse);
    return true;
  }

  if (message.command === 'CheckProAvailable' && sender.tab?.id) {
    const hasPro = !pagesWithoutPro.has(sender.tab.id);
    sendResponse({ hasPro });
    return true;
  }

  if (message.command === 'UpdateParsedIdFromContentScript' && sender.tab?.id) {
    const tabId = sender.tab.id;
    const newParsedId = message.parsedId;

    applyParsedIdUpdate(tabId, newParsedId);

    return true;
  }
});

function checkAuthCookies(authCallback: (response?: string) => void) {
  chrome.cookies.get({ url: DRUMKIT_AUTH_URL, name: COOKIE_NAME }, (cookie) => {
    if (cookie !== null) {
      authCallback(decodeURIComponent(cookie?.value));
      // Must send callback or extension will hang for several minutes
    } else {
      authCallback();
    }
  });
}

function removeAuthCookies(authCallback: (response?: string) => void) {
  chrome.cookies.remove(
    { url: DRUMKIT_AUTH_URL, name: COOKIE_NAME },
    (value) => {
      authCallback(value?.name);
    }
  );
}

chrome.action.onClicked.addListener(async (tab) => {
  if (!tab || !tab.id) {
    return;
  }

  chrome.tabs
    .sendMessage(tab.id, {
      command: 'DrumkitIconClicked',
    })
    .catch((err) => {
      const errorMessage = err instanceof Error ? err.message : String(err);

      if (!isSuppressedSidepanelError(errorMessage.toLowerCase())) {
        captureException(
          new Error(
            'error sending drumkit action icon clicked message: ' + errorMessage
          )
        );
      }
    });
});

// Injects the Relay script into the tab when the user navigates to the planning board or hub
// This is necessary because Relay uses a modal to render the appointment forms, which is not
// rendered in the main DOM hence the need to inject a listening script to detect when the modal is opened.
chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
  if (changeInfo.status !== 'complete') {
    return;
  }

  const urlStr = changeInfo.url ?? tab.url;
  if (!urlStr) {
    return;
  }

  const url = new URL(urlStr);

  // Relay script injection
  if (
    url.href.includes('relaytms.com/planning_board') ||
    url.href.includes('relaytms.com/hub')
  ) {
    chrome.scripting.executeScript({
      target: { tabId },
      func: registerRelayModalListener,
      args: [url.href],
    });
  }

  if (sidepanelBlacklist.some((host) => url.origin.includes(host))) {
    chrome.sidePanel.setOptions({ tabId, enabled: false });
    return;
  } else {
    try {
      chrome.sidePanel.setOptions({
        tabId,
        path: 'src/pages/sidepanel/index.html',
        enabled: true,
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);

      if (!isSuppressedSidepanelError(errorMessage.toLowerCase())) {
        captureException(
          new Error('error enabling side panel: ' + errorMessage)
        );
      }
    }
  }

  if (url.origin.includes('aljex.com')) {
    await handleParseForAljex(tabId);
  } else if (url.origin.includes('turvo.com')) {
    await handleParseForTurvo(tabId);
  } else if (url.origin.includes('taicloud.net')) {
    await handleParseForTai(tabId, urlStr);
  } else {
    pagesWithoutPro.add(tabId);
  }
});

function applyParsedIdUpdate(tabId: number, newParsedId: Maybe<string>) {
  if (!newParsedId) {
    pagesWithoutPro.add(tabId);
  } else {
    pagesWithoutPro.delete(tabId);
  }

  if (parsedLoadId !== newParsedId) {
    chrome.runtime
      .sendMessage({
        action: 'updateParsedId',
        parsedId: newParsedId,
        tabId: tabId,
      } as any)
      .catch(() => {
        // sidepanel may not be open yet
      });
  }

  parsedLoadId = newParsedId ?? '';

  const hasPro = !!newParsedId;
  chrome.tabs
    .sendMessage(tabId, {
      command: 'ProStatusUpdated',
      hasPro,
    })
    .catch(() => {});
}

async function handleParseForTurvo(tabId: number) {
  chrome.tabs
    .sendMessage(tabId, {
      command: 'ParseTurvoPro',
    })
    .catch((err) => {
      const errorMessage = err instanceof Error ? err.message : String(err);

      if (!isSuppressedSidepanelError(errorMessage.toLowerCase())) {
        captureException(
          new Error('error sending parse turvo pro message: ' + errorMessage)
        );
      }
    });
}

async function handleParseForNewAljex(tabId: number) {
  chrome.tabs
    .sendMessage(tabId, {
      command: 'ParseNewAljexPro',
    })
    .catch((err) => {
      const errorMessage = err instanceof Error ? err.message : String(err);

      if (!isSuppressedSidepanelError(errorMessage.toLowerCase())) {
        captureException(
          new Error(
            'error sending parse new aljex pro message: ' + errorMessage
          )
        );
      }
    });
}

// TODO (aaron): Move aljex specific logic to a separate file and import in as module
// Parse the PRO ID from the passed in Aljex tab (should work globally for all Aljex boards)
async function handleParseForAljex(tabId: number) {
  const aljexResult = await chrome.scripting.executeScript({
    target: { tabId },
    func: parseAljexPro,
  });

  const newParsedId = aljexResult?.[0]?.result ?? null;

  applyParsedIdUpdate(tabId, newParsedId);
}

function handleParseForTai(tabId: number, tabUrl: string) {
  const match = tabUrl.match(/\/shipment-details\/(\d+)/);
  const newParsedId = match?.[1] ?? null;
  applyParsedIdUpdate(tabId, newParsedId);
}

// Allows users to open the side panel on Aljex by clicking on the action toolbar icon
chrome.sidePanel
  .setPanelBehavior({ openPanelOnActionClick: true })
  .catch((error) => {
    const errorMessage = error instanceof Error ? error.message : String(error);

    if (!isSuppressedSidepanelError(errorMessage.toLowerCase())) {
      captureException(
        new Error('error setting panel behavior: ' + errorMessage)
      );
    }
  });

chrome.runtime.onInstalled.addListener(async () => {
  chrome.contextMenus.create({
    id: 'openSidePanel',
    title: 'Open Drumkit',
    contexts: ['all'],
  });
});

chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (
    info.menuItemId === 'openSidePanel' &&
    tab?.url &&
    !sidepanelBlacklist.some((host) => tab.url?.includes(host))
  ) {
    chrome.sidePanel.open({ tabId: tab.id, windowId: tab.windowId });
  } else {
    // Disables the side panel on all other sites
    chrome.sidePanel.setOptions({
      tabId: tab?.id,
      enabled: false,
    });
  }
});

chrome.cookies.onChanged.addListener((info) => {
  if (
    DRUMKIT_AUTH_URL?.includes(info.cookie.domain) &&
    info.cookie.name === COOKIE_NAME
  ) {
    chrome.runtime.sendMessage({ command: 'CookieUpdated' });
  }
});

export function parseAljexPro() {
  return document.querySelector('#headpro')?.textContent?.trim();
}
