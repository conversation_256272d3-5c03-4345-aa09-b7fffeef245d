import React, { useEffect, useState } from 'react';

import { AppTMS } from '@pages/content/src/app';

import { DrumkitPlatform } from 'contexts/sidebarStateContext';

const SidePanel: React.FC = () => {
  const [parsedId, setParsedId] = useState<string[]>([]);
  const [currentTabId, setCurrentTabId] = useState<number | null>(null);

  const handleMessage = (message: any) => {
    if (
      message.action === 'updateParsedId' &&
      message.parsedId &&
      message.tabId === currentTabId
    ) {
      setParsedId([message.parsedId]);
    }
  };

  useEffect(() => {
    async function connectPort() {
      try {
        const tab: chrome.tabs.Tab = await chrome.runtime.sendMessage({
          command: 'GetCurrentTab',
        });

        // Open a port connection to let background script know sidepanel has loaded
        // NOTE: background script relies on drumkitSidepanel-tab${tab.id} format. If changing, be sure to
        // also update chrome.runtime.onConnect.addListener
        if (tab.id) {
          setCurrentTabId(tab.id);
          chrome.runtime.connect({ name: `drumkitSidepanel-${tab.id}` });
        } else {
          console.error('Unable to connect port: missing tab id');
        }
      } catch (error) {
        console.error('Error fetching extension info:', error);
      }
    }

    connectPort();
  }, []);

  useEffect(() => {
    chrome.runtime.onMessage.addListener(handleMessage);

    return () => {
      chrome.runtime.onMessage.removeListener(handleMessage);
    };
  }, [currentTabId]);

  return (
    <>
      <AppTMS
        proNumbers={parsedId}
        wrapperPlatform={DrumkitPlatform.Sidepanel}
        isChromeSidePanel={true}
        disableCollapseButton={true}
      />
    </>
  );
};

export default SidePanel;
