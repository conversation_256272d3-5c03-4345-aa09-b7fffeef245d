import { useState } from 'react';

import { ChartSplineIcon, Loader2 } from 'lucide-react';

import { Button } from 'components/Button';
import { GenericLineChart } from 'components/GenericLineChart';
import { QuoteCardType } from 'components/QuoteCard';
import { Flex } from 'components/layout';
import { Typography } from 'components/typography';
import getDATLaneHistory from 'lib/api/getDATLaneHistory';
import { QuickQuoteResponseStop } from 'lib/api/getQuickQuote';
import { TransportType } from 'types/QuoteRequest';
import { Maybe } from 'types/UtilityTypes';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';
import { titleCase } from 'utils/formatStrings';

import { useHelperFunctions } from './helperFunctions';

interface DATLaneHistoryProps {
  quoteResponse: Maybe<{
    stops: QuickQuoteResponseStop[];
  }>;
  quoteCards: QuoteCardType[];
  transportType: TransportType;
  DATLaneHistoryData: Maybe<DATLaneHistory>;
  setDATLaneHistoryData: (DATLaneHistoryData: Maybe<DATLaneHistory>) => void;
  hasPulledDATLaneHistory: boolean;
  setHasPulledDATLaneHistory: (hasPulledDATLaneHistory: boolean) => void;
}

export type DATLaneHistoryChartEntry = {
  week: string;
  averageRate: number;
  maxRate: number;
  lowestRate: number;
};

export type DATLaneHistory = {
  rates: DATLaneHistoryChartEntry[];
};

export const DATLaneHistory = ({
  quoteResponse,
  quoteCards,
  transportType,
  DATLaneHistoryData,
  setDATLaneHistoryData,
  hasPulledDATLaneHistory,
  setHasPulledDATLaneHistory,
}: DATLaneHistoryProps) => {
  const [isLoadingDATLaneHistory, setIsLoadingDATLaneHistory] = useState(false);
  const [datLaneHistoryError, setDatLaneHistoryError] =
    useState<Maybe<string>>(null);

  const showDATLaneHistoryChart =
    !!DATLaneHistoryData?.rates && DATLaneHistoryData.rates.length > 0;

  const handlePullDATLaneHistory = async () => {
    if (!quoteResponse?.stops || quoteResponse.stops.length < 2) {
      return;
    }

    setIsLoadingDATLaneHistory(true);
    setHasPulledDATLaneHistory(true);

    const res = await getDATLaneHistory({
      transportType: transportType || '',
      originCity: quoteResponse.stops[0].city || '',
      originState: quoteResponse.stops[0].state || '',
      destinationCity:
        quoteResponse.stops[quoteResponse.stops.length - 1].city || '',
      destinationState:
        quoteResponse.stops[quoteResponse.stops.length - 1].state || '',
    });

    if (res.isOk()) {
      const formattedRates = useHelperFunctions.formatDATLaneHistoryRates(
        res.value.rates
      );

      const validRates = formattedRates.filter(
        (rate) =>
          rate.averageRate > 0 || rate.maxRate > 0 || rate.lowestRate > 0
      );

      // Get current DAT quote data to append as current month
      const currentDATQuote = quoteCards.find(
        (card) =>
          card.type === 'DAT_RATEVIEW' ||
          card.type === 'DAT_LONGEST_LEG' ||
          card.type === 'DAT_LEG_TO_LEG'
      );

      // Append current month data if DAT quote exists
      if (currentDATQuote && currentDATQuote.priceRange) {
        const date = new Date();
        const currentFullMonthYear = `${date.toLocaleString('default', { month: 'long' })} ${date.getFullYear()}`;

        validRates.push({
          week: currentFullMonthYear,
          averageRate: currentDATQuote.cost,
          maxRate: currentDATQuote.priceRange.highEstimate,
          lowestRate: currentDATQuote.priceRange.lowEstimate,
        });
      }

      if (datLaneHistoryError) {
        setDatLaneHistoryError(null);
      }

      setDATLaneHistoryData({ rates: validRates });
    } else {
      setDatLaneHistoryError(res.error.message);
    }

    setIsLoadingDATLaneHistory(false);
  };

  return (
    <div>
      {datLaneHistoryError && (
        <Typography
          variant='body-xs'
          align='center'
          className='text-error-500 mb-4'
        >
          {datLaneHistoryError}
        </Typography>
      )}

      {(!hasPulledDATLaneHistory || datLaneHistoryError) && (
        <Button
          buttonNamePosthog={ButtonNamePosthog.QuickQuotePullDATLaneHistory}
          variant='secondary'
          className='w-full h-9 text-sm'
          type='button'
          onClick={handlePullDATLaneHistory}
        >
          <ChartSplineIcon className='h-4 w-4 mr-2' />
          Get DAT Rate History
        </Button>
      )}

      {hasPulledDATLaneHistory && (
        <div className='mt-2'>
          {isLoadingDATLaneHistory ? (
            <Flex justify='center' align='center' className='py-4'>
              <Loader2 className='h-4 w-4 animate-spin mr-2' />
            </Flex>
          ) : (
            <>
              {showDATLaneHistoryChart && (
                <GenericLineChart
                  data={DATLaneHistoryData.rates}
                  key='year'
                  yAxisDomainMax={Math.max(
                    ...DATLaneHistoryData.rates.map((r) => r.maxRate),
                    1000
                  )}
                  yAxisWidth={50}
                  xAxisDomain={DATLaneHistoryData.rates.map((r) =>
                    r.week.substring(0, 3)
                  )}
                  dataKeys={['maxRate', 'averageRate', 'lowestRate']}
                  title='DAT Lane History'
                  subtitle={titleCase(transportType)}
                  description='Monthly rate trends for 6 previous months'
                  hideCarrierDetails
                  hideQuoteCount
                  chartConfig={{
                    maxRate: {
                      label: 'Max',
                      color: 'hsl(var(--error))',
                    },
                    averageRate: {
                      label: 'Avg',
                      color: 'hsl(var(--brand))',
                    },
                    lowestRate: {
                      label: 'Min',
                      color: 'hsl(var(--success))',
                    },
                  }}
                  thirdTooltipLabel='Rate Source'
                />
              )}
            </>
          )}
        </div>
      )}
    </div>
  );
};
