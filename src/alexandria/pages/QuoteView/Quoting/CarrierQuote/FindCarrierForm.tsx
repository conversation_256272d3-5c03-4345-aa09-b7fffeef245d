import { Controller, useFormContext } from 'react-hook-form';

import { ErrorMessage } from '@hookform/error-message';

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from 'components/Accordion';
import { Button } from 'components/Button';
import { Label } from 'components/Label';
import DateTimeInput from 'components/input/DateTimeInput';
import { Input } from 'components/input/Input';
import { RHFTextInput } from 'components/input/RHFTextInput';
import { Flex, Grid } from 'components/layout';
import ButtonLoader from 'components/loading/ButtonLoader';
import { Typography } from 'components/typography';
import { IntegrationCore } from 'contexts/serviceContext';
import { CarrierFormConfig } from 'pages/QuoteView/Quoting/CarrierQuote/types';
import { CompanyCoreInfo } from 'types/Load';
import { Maybe } from 'types/UtilityTypes';
import ButtonText from 'types/enums/ButtonText';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';
import { GenericCompanySearchableFields } from 'utils/loadInfoAndBuilding';

import LoadInformationForm from './LoadInformationForm';
import RefreshCarrierLocationsButton from './RefreshCarrierLocationsButton';

export interface FindCarrierFormProps {
  loading: boolean;
  config: CarrierFormConfig;
  customers: Maybe<CompanyCoreInfo[]>;
  handleRefreshCustomers: () => Promise<void>;
  handleResetCustomerSearch: () => void;
  handleCustomerSearch: (
    field: GenericCompanySearchableFields,
    value: string
  ) => Promise<any[]>;
  tmsIntegrations: IntegrationCore[];
}

export default function FindCarrierForm({
  loading,
  config,
  customers,
  handleRefreshCustomers,
  handleResetCustomerSearch,
  handleCustomerSearch,
  tmsIntegrations,
}: FindCarrierFormProps) {
  const {
    control,
    register,
    formState: { errors, isValid },
  } = useFormContext();

  return (
    <Grid gap='md' className='w-full mx-0'>
      {/* Load Information Form */}
      <LoadInformationForm
        config={config}
        customers={customers}
        handleRefreshCustomers={handleRefreshCustomers}
        handleResetCustomerSearch={handleResetCustomerSearch}
        handleCustomerSearch={handleCustomerSearch}
        tmsIntegrations={tmsIntegrations}
      />

      {/* Pickup */}
      <Flex direction='col' align='stretch' gap='sm' className='w-full'>
        <Flex align='center' justify='between' className='w-full'>
          <Typography variant='h6' weight='semibold'>
            Pickup
          </Typography>
          <RefreshCarrierLocationsButton />
        </Flex>

        {config.showPickupAddressLine1 && (
          <Flex direction='col'>
            <Label name='addressLine1' required={true}>
              Address Line 1
            </Label>
            <Input
              {...register('pickupLocation.addressLine1', {
                required: 'Address Line 1 is required',
              })}
              placeholder='e.g. 123 Main St'
              className='mt-1'
            />
            <ErrorMessage
              errors={errors}
              name='pickupLocation.addressLine1'
              render={({ message }: { message: string }) => (
                <Typography variant='body-xs' className='text-error-500'>
                  {message}
                </Typography>
              )}
            />
          </Flex>
        )}

        {/* Universal Location Input for Pickup */}
        <LocationInput
          name='pickupLocation.location'
          label='Location'
          placeholder='e.g. 02116 or Boston, MA'
          required={true}
        />

        <Flex direction='col' gap='xs'>
          <Label name='mileRadius' required={true}>
            Search Radius (miles)
          </Label>
          <Controller
            name='mileRadius'
            control={control}
            rules={{
              required: 'Search radius is required',
              min: { value: 1, message: 'Radius must be at least 1 mile' },
            }}
            render={({ field }) => (
              <Input {...field} placeholder='Enter radius (miles)' />
            )}
          />
          <ErrorMessage
            errors={errors}
            name='mileRadius'
            render={({ message }: { message: string }) => (
              <Typography variant='body-xs' className='text-error-500'>
                {message}
              </Typography>
            )}
          />
        </Flex>

        <Accordion type='single' collapsible defaultValue=''>
          <AccordionItem value='pickup-dates'>
            <AccordionTrigger className='text-sm py-3'>
              Pickup Time Window
            </AccordionTrigger>
            <AccordionContent>
              <Flex direction='col' gap='sm'>
                <Controller
                  name='pickupStartDate'
                  control={control}
                  render={({ field }) => (
                    <DateTimeInput
                      {...field}
                      control={control}
                      name='pickupStartDate'
                      label='Start Date and Time'
                      preventNormalizedLabelTZ={true}
                    />
                  )}
                />
                <Controller
                  name='pickupEndDate'
                  control={control}
                  render={({ field }) => (
                    <DateTimeInput
                      {...field}
                      control={control}
                      name='pickupEndDate'
                      label='End Date and Time'
                      preventNormalizedLabelTZ={true}
                    />
                  )}
                />
              </Flex>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </Flex>

      {/* Dropoff */}
      {config.showDeliverySection && (
        <Flex
          direction='col'
          align='stretch'
          gap='sm'
          className='flex-1 w-full mt-2'
        >
          <Typography
            variant='h5'
            className='text-neutral-800 whitespace-nowrap'
          >
            Dropoff
          </Typography>

          {/* Universal Location Input for Delivery */}
          <LocationInput
            name='deliveryLocation.location'
            label='Location'
            placeholder='e.g. 02116 or Boston, MA'
            required={config.requireDeliveryLocation}
          />

          <Accordion type='single' collapsible defaultValue=''>
            <AccordionItem value='delivery-dates'>
              <AccordionTrigger className='text-sm py-3'>
                Delivery Time Window
              </AccordionTrigger>
              <AccordionContent>
                <Flex direction='col' gap='sm'>
                  <Controller
                    name='deliveryStartDate'
                    control={control}
                    render={({ field }) => (
                      <DateTimeInput
                        {...field}
                        control={control}
                        name='deliveryStartDate'
                        label='Start Date and Time'
                        preventNormalizedLabelTZ={true}
                      />
                    )}
                  />
                  <Controller
                    name='deliveryEndDate'
                    control={control}
                    render={({ field }) => (
                      <DateTimeInput
                        {...field}
                        control={control}
                        name='deliveryEndDate'
                        label='End Date and Time'
                        preventNormalizedLabelTZ={true}
                      />
                    )}
                  />
                </Flex>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </Flex>
      )}

      {/* Find Carriers Button */}
      <Button
        buttonNamePosthog={ButtonNamePosthog.FindCarriers}
        type='submit'
        className='w-full'
        disabled={
          loading ||
          !isValid ||
          !!(errors.pickupLocation as any)?.location ||
          !!errors.mileRadius
        }
      >
        {loading ? <ButtonLoader /> : ButtonText.FindCarriers}
      </Button>
    </Grid>
  );
}

// Universal location input component with proper types
interface LocationInputProps {
  name: string;
  label: string;
  placeholder: string;
  required?: boolean;
}

const LocationInput = ({
  name,
  label,
  placeholder,
  required = false,
}: LocationInputProps) => {
  return (
    <Flex direction='col' gap='xs'>
      <RHFTextInput
        name={name}
        label={label}
        placeholder={placeholder}
        required={required}
      />
    </Flex>
  );
};
