import { useContext, useEffect, useMemo, useState } from 'react';
import {
  FieldPath,
  FormProvider,
  SubmitError<PERSON>andler,
  SubmitHandler,
  useForm,
} from 'react-hook-form';

import { Accordion } from '@radix-ui/react-accordion';
import {
  BoxIcon,
  Building2,
  CircleDollarSignIcon,
  PackageIcon,
  ReceiptIcon,
  SettingsIcon,
  TruckIcon,
  WarehouseIcon,
  Weight,
} from 'lucide-react';

import { Button } from 'components/Button';
import { FormatPhoneNumber, RHFTextInput } from 'components/input/RHFTextInput';
import { Flex } from 'components/layout';
import ButtonLoader from 'components/loading/ButtonLoader';
import { Typography } from 'components/typography/Typography';
import { ExtendedFormProvider } from 'contexts/extendedFormContext';
import { SidebarStateContext } from 'contexts/sidebarStateContext';
import { useServiceFeatures } from 'hooks/useServiceContext';
import { useToast } from 'hooks/useToaster';
import { CreateLoadRequest, createLoad } from 'lib/api/createLoad';
import { getCustomers } from 'lib/api/getCustomers';
import { updateTMS } from 'lib/api/updateTMS';
import { FormStorageService } from 'lib/services/FormStorage/service';
import { LoadSectionAccordionItem } from 'pages/LoadView/LoadInformation/Components';
import {
  AdditionalReference,
  Commodity,
  CompanyCoreInfo,
  Customer,
  Dropoff,
  Load,
  LoadCarrier,
  NormalizedLoad,
  Note,
  Pickup,
  RateData,
  TMSCustomer,
  createInitLoad,
  createInitRateData,
  createInitSpecs,
  normalizeLoad,
} from 'types/Load';
import { Maybe } from 'types/UtilityTypes';
import { TMSOperation } from 'types/api/UpdateTMS';
import ButtonText from 'types/enums/ButtonText';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';
import { TMS } from 'types/enums/Integrations';
import { SuggestionPipelines } from 'types/suggestions/CoreSuggestions';
import { SuggestedLoad } from 'types/suggestions/LoadBuildingSuggestions';
import { denormalizeDatesForTMSForm } from 'utils/parseDatesForTMSForm';

import AdditionalReferencesForm from './AdditionalReferences';
import BillToSectionForm from './BillTo';
import CarrierSectionForm from './Carrier';
import CustomerSectionForm from './Customer';
import EquipmentTypeForm from './EquipmentType';
import RatesForm from './Rates';
import SpecificationsForm from './Specifications';
import StopForm from './Stop';
import { getAljexCharacterLimit } from './characterLimits';

enum AvailableTabs {
  customer = 'customer',
  billTo = 'billTo',
  equipmentType = 'equipmentType',
  commodities = 'commodities',
  pickup = 'pickup',
  consignee = 'consignee',
  rates = 'rates',
  additionalReferences = 'additionalReferences',
  carrier = 'carrier',
}

type LoadBuildingTextInputProps = React.ComponentPropsWithoutRef<
  typeof RHFTextInput
> & { name: FieldPath<NormalizedLoad> };

export const LoadBuildingTextInput = (props: LoadBuildingTextInputProps) => {
  const maxLength = getAljexCharacterLimit(props.name);
  return <RHFTextInput {...props} maxLength={maxLength} />;
};

const initLoadBuildingForm = (tmsName: string): NormalizedLoad =>
  normalizeLoad(tmsName, {
    ...createInitLoad(),
    mode: 'Truckload',
    specifications: {
      ...createInitSpecs(),
      transportType: 'V',
    },
    rateData: {
      ...createInitRateData(),
      customerRateType: 'Flat Rate',
      carrierRateType: 'Flat Rate',
    },
  });

function mapRateTypeToCode(rateType: string): string {
  switch (rateType) {
    case 'Flat Rate':
      return 'F';
    case 'All In':
      return 'I';
    case 'Auto Rate':
      return 'A';
    case 'CWT':
      return 'C';
    case 'Ton':
      return 'T';
    case 'Pieces':
      return 'P';
    case 'Mileage':
      return 'M';
    case 'Hourly':
      return 'H';
    case 'Gainshare':
      return 'G';
    case 'Percent':
      return '%';
    default:
      return rateType;
  }
}

interface UnifiedAljexLoadFormProps {
  isCreateMode: boolean;
  load?: Load;
  onSubmit?: (data: NormalizedLoad) => void;
  onSuccess?: (result: any) => void;
  onError?: (error: any) => void;
}

export default function UnifiedAljexLoadForm({
  isCreateMode,
  load,
  onSubmit,
  onSuccess,
  onError,
}: UnifiedAljexLoadFormProps) {
  const { tmsIntegrations } = useServiceFeatures();
  const { toast } = useToast();
  const tmsName = TMS.Aljex;
  const [isLoading, setIsLoading] = useState(false);
  const [activeTabs, setActiveTabs] = useState<string[]>(
    Object.values(AvailableTabs)
  );
  const [isLoadBuildingSuggestionClicked, setIsLoadBuildingSuggestionClicked] =
    useState(false);
  const [showAIHints, setShowAIHints] = useState(false);

  const [isLoadingCustomers, setIsLoadingCustomers] = useState(true);
  const [customers, setCustomers] = useState<Maybe<TMSCustomer[]>>(null);
  const [builtLoad, setBuiltLoad] = useState<Maybe<Load>>(null);

  const {
    currentState: { clickedSuggestion, threadId, goToSuggestionInCarousel },
    setCurrentState,
  } = useContext(SidebarStateContext);

  const fetchCustomers = async () => {
    setIsLoadingCustomers(true);

    const res = await getCustomers(tmsIntegrations[0]?.id);
    if (res.isOk()) {
      setCustomers(res.value.customerList);
    } else {
      toast({
        description: res.error.message || 'Error while fetching customer list.',
        variant: 'destructive',
      });
    }
    setIsLoadingCustomers(false);
  };

  useEffect(() => {
    if (
      isCreateMode &&
      (!clickedSuggestion ||
        clickedSuggestion.pipeline !== SuggestionPipelines.LoadBuilding)
    ) {
      goToSuggestionInCarousel({
        suggestionPipeline: SuggestionPipelines.LoadBuilding,
      });
    }
    fetchCustomers();
  }, []);

  useEffect(() => {
    if (
      clickedSuggestion &&
      clickedSuggestion.pipeline === SuggestionPipelines.LoadBuilding
    ) {
      setIsLoadBuildingSuggestionClicked(true);
      setShowAIHints(true);
    } else {
      setIsLoadBuildingSuggestionClicked(false);
      setShowAIHints(false);
    }
  }, [clickedSuggestion]);

  const memoizedDefaultValues: NormalizedLoad = useMemo(() => {
    // Edit mode: use existing load data
    if (!isCreateMode && load) {
      return normalizeLoad(tmsName, load);
    }

    // Create mode: check for suggestions or use init data
    if (isCreateMode) {
      if (
        clickedSuggestion &&
        clickedSuggestion.pipeline === SuggestionPipelines.LoadBuilding
      ) {
        const casted = clickedSuggestion.suggested as SuggestedLoad;

        // convert object-with-numeric-keys back to arrays (Aljex API response issue)
        // Only convert if it's an object that's not already an array
        const commoditiesArray: Commodity[] =
          casted.commodities &&
          typeof casted.commodities === 'object' &&
          !Array.isArray(casted.commodities)
            ? Object.values(casted.commodities)
            : casted.commodities || [];

        const additionalReferencesArray: AdditionalReference[] =
          casted.additionalReferences &&
          typeof casted.additionalReferences === 'object' &&
          !Array.isArray(casted.additionalReferences)
            ? Object.values(casted.additionalReferences)
            : casted.additionalReferences || [];

        const customer: Customer = casted.customer
          ? {
              ...casted.customer,
              phone: casted.customer.phone
                ? FormatPhoneNumber(casted.customer.phone)
                : casted.customer.phone,
            }
          : casted.customer;

        const suggestedFields: Load = {
          ...createInitLoad(),
          ...casted,
          mode: 'Truckload',

          customer: casted.customer
            ? {
                ...casted.customer,
                phone: casted.customer.phone
                  ? FormatPhoneNumber(casted.customer.phone)
                  : casted.customer.phone,
              }
            : casted.customer,
          billTo: customer,
          pickup: casted.pickup
            ? {
                ...casted.pickup,
                phone: casted.pickup.phone
                  ? FormatPhoneNumber(casted.pickup.phone)
                  : casted.pickup.phone,
                apptStartTime: casted.pickup.apptStartTime
                  ? new Date(casted.pickup.apptStartTime).toISOString()
                  : null,
                apptEndTime: casted.pickup.apptEndTime
                  ? new Date(casted.pickup.apptEndTime).toISOString()
                  : null,
                readyTime: casted.pickup.readyTime
                  ? new Date(casted.pickup.readyTime).toISOString()
                  : null,
              }
            : casted.pickup,
          consignee: casted.consignee
            ? {
                ...casted.consignee,
                phone: casted.consignee.phone
                  ? FormatPhoneNumber(casted.consignee.phone)
                  : casted.consignee.phone,
                apptStartTime: casted.consignee.apptStartTime
                  ? new Date(casted.consignee.apptStartTime).toISOString()
                  : null,
                apptEndTime: casted.consignee.apptEndTime
                  ? new Date(casted.consignee.apptEndTime).toISOString()
                  : null,
                mustDeliver: casted.consignee.mustDeliver
                  ? new Date(casted.consignee.mustDeliver).toISOString()
                  : '',
              }
            : casted.consignee,
          specifications: {
            ...createInitSpecs(),
            ...casted.specifications,
          },
          rateData: {
            ...createInitRateData(),
            ...casted.rateData,
          },
          additionalReferences: additionalReferencesArray,
          commodities: commoditiesArray,
        };

        return {
          ...normalizeLoad(tmsName, createInitLoad()),
          ...normalizeLoad(tmsName, suggestedFields),
          mode: 'Truckload',
        } as NormalizedLoad;
      }

      if (builtLoad) {
        return normalizeLoad(tmsName, builtLoad);
      }

      return initLoadBuildingForm(tmsName);
    }

    return initLoadBuildingForm(tmsName);
  }, [clickedSuggestion, tmsName, builtLoad, isCreateMode, load]);

  const formMethods = useForm<NormalizedLoad>({
    defaultValues: memoizedDefaultValues,
  });

  const { handleSubmit } = formMethods;

  useEffect(() => {
    formMethods.reset(memoizedDefaultValues, {
      keepDefaultValues: false,
      keepTouched: false,
      keepDirtyValues: false,
      keepDirty: false,
    });
  }, [memoizedDefaultValues]);

  // Hook to cache load building form
  useEffect(() => {
    if (isCreateMode) {
      const subscription = formMethods.watch((value) => {
        FormStorageService.saveFormState(`${tmsName}_${threadId}`, {
          threadID: threadId,
          values: value,
          clickedSuggestion: isLoadBuildingSuggestionClicked
            ? clickedSuggestion
            : null,
          dirtyFields: formMethods.formState.dirtyFields,
        });
      });
      return () => subscription.unsubscribe();
    }
    return undefined;
  }, [
    formMethods,
    threadId,
    isLoadBuildingSuggestionClicked,
    clickedSuggestion,
    tmsName,
    isCreateMode,
  ]);

  function handleClearForm() {
    if (isCreateMode) {
      setBuiltLoad(null);
      FormStorageService.clearFormState(`${tmsName}_${threadId}`);
      formMethods.reset(initLoadBuildingForm(tmsName), {
        keepDefaultValues: false,
        keepDirty: false,
        keepDirtyValues: false,
        keepTouched: false,
      });
    }
  }

  const onInvalid: SubmitErrorHandler<NormalizedLoad> = () => {
    toast({
      description:
        'Some fields are invalid. Please fix the form errors before submitting.',
      variant: 'destructive',
    });
  };

  const onSubmitCreate: SubmitHandler<NormalizedLoad> = async (data) => {
    setIsLoading(true);
    data.freightTrackingID = '';
    data.externalTMSID = '';

    const updatedData = formMethods.getValues();

    // no more handling enum on the FE. BE does it already
    const commoditiesData = updatedData.commodities || [];
    const additionalReferencesData = updatedData.additionalReferences || [];

    const mappedRateData: RateData = {
      ...updatedData.rateData,
      customerRateType: updatedData.rateData?.customerRateType
        ? mapRateTypeToCode(updatedData.rateData.customerRateType)
        : '',
      carrierRateType: updatedData.rateData?.carrierRateType
        ? mapRateTypeToCode(updatedData.rateData.carrierRateType)
        : '',
    };

    const notesArray: Note[] = [];

    if (updatedData.pickup?.instructions?.trim()) {
      notesArray.push({
        note: `${updatedData.pickup.instructions.trim()}`,
        updatedBy: '',
        createdAt: new Date().toISOString(),
        isException: null,
        isOnTime: null,
        source: 'form',
      });
    }

    const reqData: CreateLoadRequest = {
      suggestionId: clickedSuggestion?.id,
      load: {
        ...updatedData,
        tmsID: tmsIntegrations[0]?.id,
        operator: updatedData.operator,
        mode: updatedData.mode,
        carrier: updatedData.carrier as LoadCarrier,
        customer: denormalizeDatesForTMSForm(
          tmsName,
          updatedData.customer
        ) as Customer,
        billTo: denormalizeDatesForTMSForm(
          tmsName,
          updatedData.billTo
        ) as CompanyCoreInfo,
        specifications: {
          ...updatedData.specifications,
        },
        pickup: {
          ...denormalizeDatesForTMSForm(tmsName, updatedData.pickup),
        } as Pickup,
        consignee: {
          ...denormalizeDatesForTMSForm(tmsName, updatedData.consignee),
        } as Dropoff,
        rateData: mappedRateData,
        commodities: commoditiesData,
        notes: notesArray,
        additionalReferences: additionalReferencesData
          .filter((ref) => ref.number && ref.number.trim())
          .map((ref) => ({
            qualifier: ref.qualifier || '',
            number: ref.number,
            weight: ref.weight || 0,
            pieces: ref.pieces || 0,
            shouldSendToDriver: ref.shouldSendToDriver || false,
          })),
      },
    };

    const res = await createLoad(reqData);

    if (res.isOk()) {
      setBuiltLoad(res.value.load);
      FormStorageService.clearFormState(`${tmsName}_${threadId}`);
      if (
        clickedSuggestion &&
        clickedSuggestion.pipeline === SuggestionPipelines.LoadBuilding
      ) {
        setCurrentState((prevState) => {
          const filteredList = prevState.curSuggestionList.filter(
            ({ id }) => id !== clickedSuggestion.id
          );
          return {
            ...prevState,
            clickedSuggestion: null,
            curSuggestionList: filteredList,
          };
        });
      }
      toast({
        title: res.value.message,
        description:
          'Load ID: ' +
          (res.value.load.externalTMSID ?? res.value.load.freightTrackingID),
        variant: 'success',
      });
      onSuccess?.(res.value);
    } else {
      toast({
        description: res.error.message || 'Failed to create load.',
        variant: 'destructive',
      });
      onError?.(res.error);
    }
    setIsLoading(false);
  };

  const onSubmitEdit: SubmitHandler<NormalizedLoad> = async () => {
    if (!load) return;

    setIsLoading(true);

    const updatedData = formMethods.getValues();

    const commoditiesData = updatedData.commodities || [];
    const additionalReferencesData = updatedData.additionalReferences || [];

    const notesArray: Note[] = [];

    if (updatedData.pickup?.instructions?.trim()) {
      notesArray.push({
        note: `${updatedData.pickup.instructions.trim()}`,
        updatedBy: '',
        createdAt: new Date().toISOString(),
        isException: null,
        isOnTime: null,
        source: 'form',
      });
    }

    // if (updatedData.consignee?.instructions?.trim()) {
    //   notesArray.push({
    //     note: `Delivery Instructions: ${updatedData.consignee.instructions.trim()}`,
    //   });
    // }

    const reqData = {
      operation: TMSOperation.UpdateLoad,
      load: {
        ...load,
        freightTrackingID: updatedData.freightTrackingID,
        externalTMSID: updatedData.externalTMSID,
        operator: updatedData.operator,
        customer: denormalizeDatesForTMSForm(
          tmsName,
          updatedData.customer
        ) as Customer,
        billTo: denormalizeDatesForTMSForm(
          tmsName,
          updatedData.billTo
        ) as CompanyCoreInfo,
        specifications: denormalizeDatesForTMSForm(
          tmsName,
          updatedData.specifications
        ),
        pickup: {
          ...denormalizeDatesForTMSForm(tmsName, updatedData.pickup),
          externalTMSID:
            updatedData.pickup.externalTMSID ?? updatedData.pickup.name,
        } as Pickup,
        consignee: {
          ...denormalizeDatesForTMSForm(tmsName, updatedData.consignee),
          externalTMSID:
            updatedData.consignee.externalTMSID ?? updatedData.consignee.name,
        } as Dropoff,
        carrier: {
          ...denormalizeDatesForTMSForm(tmsName, updatedData.carrier),
          rateConfirmationSent: updatedData.carrier.rateConfirmationSent
            ? true
            : false,
        } as LoadCarrier,
        rateData: {
          ...denormalizeDatesForTMSForm(
            tmsName,
            (() => {
              const {
                customerLineHaulCharge: _customerLineHaulCharge,
                customerTotalCharge: _customerTotalCharge,
                carrierLineHaulCharge: _carrierLineHaulCharge,
                ...cleanRateData
              } = updatedData.rateData || {};
              return cleanRateData;
            })()
          ),

          customerRateType: updatedData.rateData?.customerRateType || '',
          customerLineHaulRate:
            updatedData.rateData?.customerLineHaulRate || null,
          customerRateNumUnits:
            updatedData.rateData?.customerRateNumUnits || null,

          carrierRateType: updatedData.rateData?.carrierRateType || '',
          carrierLineHaulRate:
            updatedData.rateData?.carrierLineHaulRate || null,
          carrierRateNumUnits:
            updatedData.rateData?.carrierRateNumUnits || null,
          carrierMaxRate: updatedData.rateData?.carrierMaxRate || null,

          salesperson1: updatedData.rateData?.salesperson1 || '',
          salesperson1Percent: updatedData.rateData?.salesperson1Percent || 0,
          collectionMethod: updatedData.rateData?.collectionMethod || '',
          revenueCode: updatedData.rateData?.revenueCode || '',
          customerLineHaulCharge: null,
          customerLineHaulUnit:
            updatedData.rateData?.customerLineHaulUnit || '',
          customerTotalCharge: null,
          customerLineItems: updatedData.rateData?.customerLineItems || [],
          carrierLineHaulCharge: null,
          carrierLineHaulUnit: updatedData.rateData?.carrierLineHaulUnit || '',
          carrierTotalCost: null,
          carrierLineItems: updatedData.rateData?.carrierLineItems || [],
          fscPercent: updatedData.rateData?.fscPercent || null,
          fscPerMile: updatedData.rateData?.fscPerMile || null,
          fscFlatRate: updatedData.rateData?.fscFlatRate || null,
          netProfitUSD: updatedData.rateData?.netProfitUSD || null,
          profitPercent: updatedData.rateData?.profitPercent || null,
        },
        commodities: commoditiesData
          .filter((item) => item.description.trim())
          .map((item) => ({
            description: item.description,
            quantity: item.quantity || 0,
            packagingType: 'Pallets',
            weightTotal: item.weightTotal || 0,
            length: item.length || 0,
            width: item.width || 0,
            height: item.height || 0,
            freightClass: item.freightClass || '',
            referenceNumber: item.referenceNumber || '',
            handlingQuantity: 0,
            dimensionUnit: '',
            hazardousMaterial: false,
            nmfc: '',
            additionalMarkings: '',
            unNumber: '',
            packagingGroup: '',
            hazmatCustomClassDescription: '',
            hazmatPieceDescription: '',
            harmonizedCode: '',
            hazardClasses: null,
            loadID: 0,
          })),
        notes: notesArray,
        additionalReferences: additionalReferencesData
          .filter((ref) => ref.number && ref.number.trim())
          .map((ref) => ({
            qualifier: ref.qualifier || '',
            number: ref.number,
            weight: ref.weight || 0,
            pieces: ref.pieces || 0,
            shouldSendToDriver: ref.shouldSendToDriver || false,
          })),
      },
    };

    const res = await updateTMS(load.ID!, reqData);

    if (res.isOk()) {
      toast({
        description: res.value.message,
        variant: 'success',
      });
      onSuccess?.(res.value);
    } else {
      toast({
        description: res.error.message,
        variant: 'destructive',
      });
      onError?.(res.error);
    }

    setIsLoading(false);
  };

  const handleFormSubmit: SubmitHandler<NormalizedLoad> = async (data) => {
    if (onSubmit) {
      onSubmit(data);
    } else if (isCreateMode) {
      await onSubmitCreate(data);
    } else {
      await onSubmitEdit(data);
    }
  };

  const buttonText = isCreateMode ? ButtonText.BuildLoad : ButtonText.UpdateTMS;
  const buttonNamePosthog = isCreateMode
    ? ButtonNamePosthog.BuildLoad
    : ButtonNamePosthog.UpdateTMS;

  return (
    <Flex direction='col' className='mb-5'>
      <ExtendedFormProvider
        aiDefaultValues={showAIHints}
        aiIconOnly={true}
        highlightDirtyFields={!isCreateMode}
      >
        <FormProvider {...formMethods}>
          <form onSubmit={handleSubmit(handleFormSubmit, onInvalid)}>
            <Accordion
              type='multiple'
              value={activeTabs}
              onValueChange={setActiveTabs}
            >
              <LoadSectionAccordionItem
                label='Customer'
                icon={<Building2 className='h-6 w-6' strokeWidth={1} />}
                name={AvailableTabs.customer}
                activeTabs={activeTabs}
              >
                <CustomerSectionForm
                  formMethods={formMethods}
                  customers={customers}
                  setCustomers={setCustomers}
                  isLoadingCustomers={isLoadingCustomers}
                  setIsLoadingCustomers={setIsLoadingCustomers}
                  isCreateMode={isCreateMode}
                />
              </LoadSectionAccordionItem>

              <LoadSectionAccordionItem
                label='Bill To'
                icon={<ReceiptIcon className='h-6 w-6' strokeWidth={1} />}
                name={AvailableTabs.billTo}
                activeTabs={activeTabs}
              >
                <BillToSectionForm
                  formMethods={formMethods}
                  customers={customers}
                  setCustomers={setCustomers}
                  isLoadingCustomers={isLoadingCustomers}
                  setIsLoadingCustomers={setIsLoadingCustomers}
                  isCreateMode={isCreateMode}
                />
              </LoadSectionAccordionItem>

              <LoadSectionAccordionItem
                label='Equipment'
                icon={<SettingsIcon className='h-6 w-6' strokeWidth={1} />}
                name={AvailableTabs.equipmentType}
                activeTabs={activeTabs}
              >
                <EquipmentTypeForm formMethods={formMethods} />
              </LoadSectionAccordionItem>

              <LoadSectionAccordionItem
                label={isCreateMode ? 'Commodities' : 'Specs'}
                icon={<PackageIcon className='h-6 w-6' strokeWidth={1} />}
                name={AvailableTabs.commodities}
                activeTabs={activeTabs}
              >
                <SpecificationsForm formMethods={formMethods} />
              </LoadSectionAccordionItem>

              <LoadSectionAccordionItem
                label='Rates'
                icon={
                  <CircleDollarSignIcon className='h-6 w-6' strokeWidth={1} />
                }
                name={AvailableTabs.rates}
                activeTabs={activeTabs}
              >
                <RatesForm
                  formMethods={formMethods}
                  isCreateMode={isCreateMode}
                />
              </LoadSectionAccordionItem>

              <LoadSectionAccordionItem
                label='Pickup'
                icon={<BoxIcon className='h-6 w-6' strokeWidth={1} />}
                name={AvailableTabs.pickup}
                activeTabs={activeTabs}
              >
                <StopForm stop='pickup' formMethods={formMethods} />
              </LoadSectionAccordionItem>

              <LoadSectionAccordionItem
                label='Consignee'
                icon={<WarehouseIcon className='h-6 w-6' strokeWidth={1} />}
                name={AvailableTabs.consignee}
                activeTabs={activeTabs}
              >
                <StopForm stop='consignee' formMethods={formMethods} />
              </LoadSectionAccordionItem>

              {!isCreateMode && (
                <LoadSectionAccordionItem
                  label='Carrier'
                  icon={<TruckIcon className='h-6 w-6' strokeWidth={1} />}
                  name={AvailableTabs.carrier}
                  activeTabs={activeTabs}
                >
                  <CarrierSectionForm formMethods={formMethods} />
                </LoadSectionAccordionItem>
              )}

              <LoadSectionAccordionItem
                label='Additional Info'
                icon={<Weight className='h-6 w-6' strokeWidth={1} />}
                name={AvailableTabs.additionalReferences}
                activeTabs={activeTabs}
              >
                <AdditionalReferencesForm
                  formMethods={formMethods}
                  isCreateMode={isCreateMode}
                />
              </LoadSectionAccordionItem>
            </Accordion>

            <Flex direction='col' className='w-full mt-4'>
              <Button
                buttonNamePosthog={buttonNamePosthog}
                type='submit'
                className='w-full'
                disabled={isLoading}
                logProperties={
                  !isCreateMode && load
                    ? {
                        loadID: load.ID,
                        freightTrackingID: load.freightTrackingID,
                        serviceID: load.serviceID,
                      }
                    : undefined
                }
              >
                {isLoading ? <ButtonLoader /> : buttonText}
              </Button>

              {isCreateMode && formMethods.formState.isDirty && (
                <Flex direction='row' justify='center' align='center'>
                  <Button
                    buttonNamePosthog={ButtonNamePosthog.ClearForm}
                    type='button'
                    className='w-50% mt-4 h-8 text-sm text-neutral-700'
                    disabled={isLoading}
                    variant='outline'
                    onClick={handleClearForm}
                  >
                    {ButtonText.ClearForm}
                  </Button>
                </Flex>
              )}

              {isCreateMode && builtLoad?.externalTMSID && (
                <Flex
                  direction='col'
                  grow='1'
                  className='whitespace-pre-wrap my-3 rounded py-3 text-neutral-700 px-4 bg-success-50'
                >
                  <Typography variant='body' className='mb-2'>
                    Load Created 🎉
                  </Typography>
                  <Typography variant='body' className='mb-2'>
                    <Typography variant='body' weight='bold'>
                      Load ID #:{' '}
                    </Typography>
                    {builtLoad.externalTMSID}
                  </Typography>
                </Flex>
              )}
            </Flex>
          </form>
        </FormProvider>
      </ExtendedFormProvider>
    </Flex>
  );
}
