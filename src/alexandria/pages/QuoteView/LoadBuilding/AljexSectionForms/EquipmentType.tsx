import { Controller, UseFormReturn } from 'react-hook-form';

import { Select as AntdSelect } from 'antd';
import { BaseOptionType } from 'antd/es/select';

import { Label } from 'components/Label';
import { NormalizedLoad } from 'types/Load';

const transportTypes = [
  { value: 'V', label: 'VAN' },
  { value: 'VA', label: 'AIR-RIDE VAN' },
  { value: 'VC', label: 'CURTAIN VAN' },
  { value: 'VLG', label: 'VAN LIFTGATE' },
  { value: 'VP', label: 'VAN PARTIAL' },
  { value: 'VR', label: 'VAN OR REEFER' },
  { value: 'R', label: 'REEFER' },
  { value: 'F', label: 'FLATBED' },
  { value: 'FA', label: 'AIR-RIDE FLATBED' },
  { value: 'FOD', label: 'FLATBED OD' },
  { value: 'FOW', label: 'FLATBED OW' },
  { value: 'FSD', label: 'FLATBED - STEPDECK' },
  { value: 'FSDC', label: 'FLATBED -STEPDECK - CON.' },
  { value: 'HS', label: 'HOTSHOT' },
  { value: 'HSDT', label: 'HS PULLOUT OR DOVETAIL' },
  { value: 'HSOD', label: 'HOTSHOT OD' },
  { value: 'HSWR', label: 'HOTSHOT WRAMPS' },
  { value: 'HSWW', label: 'HOTSHOT W WINCH' },
  { value: 'STR', label: 'STRAIGHT TRUCK' },
  { value: 'SPRI', label: 'SPRINTER' },
  { value: 'STEP', label: 'STEPDECK' },
  { value: 'SDC', label: 'STEPDECK CONESTOGA' },
  { value: 'SDOD', label: 'STEPDECK OD' },
  { value: 'SDR', label: 'STEPDECK WRAMPS' },
  { value: 'STRD', label: 'STRETCH - STEPDECK' },
  { value: 'STRE', label: 'STRETCH - RGN' },
  { value: 'STRF', label: 'STRETCH - FLATBED' },
  { value: 'LB', label: 'LOWBOY' },
  { value: 'RGN', label: 'RGN' },
  { value: 'RGNW', label: 'RGN W WINCH' },
  { value: 'RGOD', label: 'RGN OD' },
  { value: 'RGOW', label: 'RGN OW' },
  { value: 'DD', label: 'DOUBLE DROP' },
  { value: 'LAND', label: 'LANDOLL' },
  { value: 'CON', label: 'CONESTOGA' },
  { value: 'PO', label: 'POWER ONLY' },
  { value: 'POOD', label: 'POWER ONLY OD' },
  { value: 'POOW', label: 'POWER ONLY OW' },
  { value: 'PH', label: 'POWER ONLY PINTLE HITCH' },
  { value: 'HOP', label: 'HOPPER' },
  { value: 'DRVR', label: 'DRIVE AWAY' },
  { value: 'WRE', label: 'WRECKER' },
  { value: 'ODS', label: 'OPEN DECK + SERVICE' },
  { value: 'PARF', label: 'PARTIAL - OPEN DECK' },
  { value: 'TORD', label: 'TRUCK ORDERED NOT USED' },
];

export default function EquipmentTypeForm({
  formMethods,
}: {
  formMethods: UseFormReturn<NormalizedLoad>;
}): React.JSX.Element {
  const { control } = formMethods;

  return (
    <div className='grid grid-cols-4 gap-4 mx-0 w-full'>
      <div className='col-span-4'>
        <Label name={'specifications.transportType'} required={true}>
          Equipment Type
        </Label>
        <Controller
          name='specifications.transportType'
          control={control}
          rules={{ required: 'Equipment type is required' }}
          render={({ field }) => (
            <AntdSelect
              showSearch
              className='h-9 text-neutral-600'
              placeholder='Select equipment type'
              optionFilterProp='children'
              filterOption={(
                input: string,
                option: BaseOptionType | undefined
              ) =>
                (option?.label.toLocaleLowerCase() ?? '').includes(
                  input.toLocaleLowerCase()
                )
              }
              filterSort={(optionA: BaseOptionType, optionB: BaseOptionType) =>
                (optionA?.label ?? '')
                  .toLowerCase()
                  .localeCompare((optionB?.label ?? '').toLowerCase())
              }
              onChange={field.onChange}
              value={field.value}
              options={transportTypes?.map((transportType) => ({
                value: transportType.value,
                label: transportType.label,
              }))}
            />
          )}
        />
      </div>
    </div>
  );
}
