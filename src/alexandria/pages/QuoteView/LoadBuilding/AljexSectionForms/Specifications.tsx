import { useCallback, useEffect, useRef } from 'react';
import { UseFormReturn, useFieldArray, useWatch } from 'react-hook-form';

import { Button } from 'components/Button';
import { RHFTextInput } from 'components/input/RHFTextInput';
import { Divider } from 'components/layout';
import { Typography } from 'components/typography/Typography';
import { ALJEX_CHARACTER_LIMITS } from 'pages/QuoteView/LoadBuilding/AljexSectionForms/characterLimits';
import { Commodity, NormalizedLoad, createNewCommodity } from 'types/Load';

import { LoadBuildingTextInput } from './UnifiedAljexLoadForm';
import { CommodityItemInputCard } from './components/CommodityItemInputCard';

const SpecificationsForm = ({
  formMethods,
}: {
  formMethods: UseFormReturn<NormalizedLoad>;
}) => {
  const { setValue, watch, control, formState } = formMethods;
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'commodities',
  });

  const hasManuallyEditedDescriptions = useRef(false);

  useEffect(() => {
    if (fields.length === 0) {
      append(createNewCommodity());
    }
  }, [fields.length, append]);

  // Track if user has manually edited the commodities description
  useEffect(() => {
    if (formState.dirtyFields.specifications?.commodities) {
      hasManuallyEditedDescriptions.current = true;
    }
  }, [formState.dirtyFields.specifications?.commodities]);

  const removeCommodity = (index: number) => {
    remove(index);
  };

  const addCommodity = () => append(createNewCommodity());

  // Update auto-calculated form fields when commodities change; useWatch is more reliable than watch for deep watching of form arrays
  const watchedCommodities = useWatch({
    control,
    name: 'commodities',
  });

  const updateFormFields = useCallback(() => {
    const commodities = watchedCommodities || [];
    const totalQuantity = commodities.reduce(
      (sum: number, item: Commodity) => sum + (item.quantity || 0),
      0
    );
    const totalWeight = commodities.reduce(
      (sum: number, item: Commodity) => sum + (item.weightTotal || 0),
      0
    );

    // Only update descriptions if user hasn't manually edited them
    if (!hasManuallyEditedDescriptions.current) {
      const maxLength =
        ALJEX_CHARACTER_LIMITS['specifications.commodities'] || 18;
      let descriptions = commodities
        .map((item: Commodity) => item.description)
        .filter((desc: string) => desc?.trim())
        .join(', ');

      // Truncate if exceeds character limit
      if (descriptions.length > maxLength) {
        descriptions = descriptions.substring(0, maxLength - 3) + '...';
      }

      setValue('specifications.commodities', descriptions);
    }

    setValue('specifications.totalPieces.val', totalQuantity);
    setValue('specifications.totalWeight.val', totalWeight);
  }, [watchedCommodities, setValue]);

  useEffect(() => {
    updateFormFields();
  }, [updateFormFields]);

  return (
    <div className='grid grid-cols-4 gap-2 mx-0 w-full'>
      <div className='col-span-4'>
        <RHFTextInput
          name='specifications.commodities'
          label='Description'
          required={true}
          placeholder='Enter description'
          maxLength={ALJEX_CHARACTER_LIMITS['specifications.commodities']}
        />
      </div>

      <Divider className='col-span-4' />
      <Typography variant='h5' className='col-span-4'>
        Items
      </Typography>
      {fields.length > 0 && (
        <div className='flex flex-col gap-4 col-span-4'>
          {fields.map((field, index) => (
            <CommodityItemInputCard
              formMethods={formMethods}
              key={field.id}
              item={watch(`commodities.${index}`) || field}
              index={index}
              onRemove={() => removeCommodity(index)}
              onSave={updateFormFields}
            />
          ))}
        </div>
      )}

      <div className='col-span-4 flex items-center justify-end mr-px'>
        <Button
          type='button'
          variant='ghost'
          size='sm'
          onClick={addCommodity}
          className='h-8 px-3 text-xs text-brand-500 hover:-mx-px'
          buttonNamePosthog={null}
        >
          + Add Commodity
        </Button>
      </div>

      <div className='col-span-4 mt-2'>
        <div className='text-sm font-medium text-neutral-700 mb-4'>
          Load Summary (Auto-calculated)
        </div>
        <div className='grid grid-cols-4 gap-4 mx-0 w-full'>
          <div className='col-span-2'>
            <LoadBuildingTextInput
              name='specifications.totalPieces.val'
              label='Total Quantity'
              inputType='number'
              options={{ valueAsNumber: true }}
              required={false}
              readOnly={true}
            />
          </div>
          <div className='col-span-2'>
            <LoadBuildingTextInput
              name='specifications.totalWeight.val'
              label='Total Weight'
              inputType='number'
              options={{ valueAsNumber: true }}
              required={false}
              readOnly={true}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
export default SpecificationsForm;
