import { UseFormReturn } from 'react-hook-form';

import DateTimeInput from 'components/input/DateTimeInput';
import { InputValue } from 'components/input/RHFTextInput';
import { NormalizedLoad } from 'types/Load';

import { LoadBuildingTextInput } from './UnifiedAljexLoadForm';

export type StopFormProps = {
  stop: 'pickup' | 'consignee';
  formMethods: UseFormReturn<NormalizedLoad>;
};

export default function StopForm({
  stop,
  formMethods,
}: StopFormProps): React.JSX.Element {
  const { watch, control } = formMethods;

  const watchedStop = watch(stop);
  const displayTitle = 'Location';

  return (
    <div className='grid grid-cols-4 gap-4 mx-0 w-full'>
      {/* Company Information */}
      <div className='col-span-4 text-sm font-medium text-neutral-700 mb-2'>
        {displayTitle}
      </div>

      <div className='col-span-4'>
        <LoadBuildingTextInput
          name={`${stop}.name`}
          label='Company Name'
          required={true}
          placeholder={`Enter ${displayTitle.toLowerCase()} company name`}
        />
      </div>

      <div className='col-span-4'>
        <LoadBuildingTextInput
          name={`${stop}.addressLine1`}
          label='Street Address'
          required={true}
          placeholder='123 Main Street'
        />
      </div>

      <div className='col-span-4'>
        <LoadBuildingTextInput
          name={`${stop}.addressLine2`}
          label='Address Line 2'
          required={false}
          placeholder='Suite 100, Dock 5, etc. (optional)'
        />
      </div>

      <div className='col-span-3'>
        <LoadBuildingTextInput name={`${stop}.city`} label='City' />
      </div>

      <div className='col-span-1'>
        <LoadBuildingTextInput name={`${stop}.state`} label='State' />
      </div>

      <div className='col-span-3'>
        <LoadBuildingTextInput
          name={`${stop}.zipCode`}
          label='ZIP'
          placeholder='12345'
        />
      </div>

      <div className='col-span-1'>
        <LoadBuildingTextInput name={`${stop}.country`} label='Country' />
      </div>

      <div className='col-span-4 text-sm font-medium text-neutral-700 mb-2 mt-3'>
        Contact Information
      </div>

      <div className='col-span-2'>
        <LoadBuildingTextInput
          name={`${stop}.contact`}
          label='Contact Person'
          required={false}
          placeholder='Contact name'
        />
      </div>

      <div className='col-span-2'>
        <LoadBuildingTextInput
          name={`${stop}.phone`}
          label='Phone'
          required={false}
          placeholder='(*************'
          inputValue={InputValue.PHONE_NUMBER}
        />
      </div>

      <div className='col-span-4'>
        <LoadBuildingTextInput
          name={`${stop}.email`}
          label='Email'
          required={false}
          placeholder='<EMAIL>'
        />
      </div>

      <div className='col-span-4 text-sm font-medium text-neutral-700 mb-2 mt-3'>
        Reference & Appointment Information
      </div>

      {stop === 'pickup' && (
        <>
          <div className='col-span-4'>
            <LoadBuildingTextInput
              name='pickup.businessHours'
              label='Business Hours'
              required={false}
              placeholder='8:00 AM - 5:00 PM'
            />
          </div>
          <div className='col-span-4'>
            <LoadBuildingTextInput
              name='pickup.refNumber'
              label='Reference Number'
              required={false}
              placeholder='Optional reference number'
            />
          </div>

          <div className='col-span-4'>
            <DateTimeInput
              control={control}
              name='pickup.readyTime'
              label='Ready Time'
              required={true}
              preventNormalizedLabelTZ={true}
            />
          </div>
          <div className='col-span-4'>
            <DateTimeInput
              control={control}
              name='pickup.apptStartTime'
              label='Appointment Time'
              required={false}
              preventNormalizedLabelTZ={true}
            />
          </div>
          <div className='col-span-4'>
            <LoadBuildingTextInput
              name='pickup.apptNote'
              label='Appointment Notes'
              required={false}
              placeholder='Call ahead, loading dock, etc.'
            />
          </div>
        </>
      )}

      {stop === 'consignee' && (
        <>
          <div className='col-span-4'>
            <LoadBuildingTextInput
              name='consignee.businessHours'
              label='Business Hours'
              required={false}
              placeholder='8:00 AM - 5:00 PM'
            />
          </div>
          <div className='col-span-4'>
            <LoadBuildingTextInput
              name='consignee.refNumber'
              label='Reference Number'
              required={false}
              placeholder='Optional reference number'
            />
          </div>
          <div className='col-span-4'>
            <DateTimeInput
              control={control}
              name='consignee.mustDeliver'
              label='Must Deliver By'
              required={false}
              hideTimePicker={true}
              preventNormalizedLabelTZ={true}
            />
          </div>

          <div className='col-span-4'>
            <DateTimeInput
              control={control}
              name='consignee.apptStartTime'
              label='Appointment Time'
              required={false}
              preventNormalizedLabelTZ={true}
            />
          </div>
          <div className='col-span-4'>
            <LoadBuildingTextInput
              name='consignee.apptNote'
              label='Appointment Notes'
              required={false}
              placeholder='Call ahead, loading dock, etc.'
            />
          </div>
        </>
      )}

      {/* Current Information */}
      {watchedStop && watchedStop.name && (
        <div className='col-span-4 mt-4'>
          <div className='bg-neutral-50 p-3 rounded-md border'>
            <div className='text-sm font-medium text-neutral-700 mb-2'>
              {displayTitle} Summary
            </div>
            <div className='text-sm text-neutral-600'>
              <div>
                <strong>{watchedStop.name}</strong>
              </div>
              {watchedStop.contact && <div>Contact: {watchedStop.contact}</div>}
              {watchedStop.addressLine1 && (
                <div>
                  {watchedStop.addressLine1}
                  {watchedStop.addressLine2 && `, ${watchedStop.addressLine2}`}
                </div>
              )}
              {(watchedStop.city ||
                watchedStop.state ||
                watchedStop.zipCode) && (
                <div>
                  {watchedStop.city && watchedStop.city}
                  {watchedStop.state && `, ${watchedStop.state}`}
                  {watchedStop.zipCode && ` ${watchedStop.zipCode}`}
                </div>
              )}
              {watchedStop.phone && <div>Phone: {watchedStop.phone}</div>}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
