import { useCallback, useEffect, useState } from 'react';
import { Controller, UseFormReturn, useFieldArray } from 'react-hook-form';

import { ChevronDownIcon, ChevronUpIcon } from 'lucide-react';

import { Button } from 'components/Button';
import { Label } from 'components/Label';
import { Textarea } from 'components/Textarea';
import { Flex } from 'components/layout/Flex';
import { ALJEX_CHARACTER_LIMITS } from 'pages/QuoteView/LoadBuilding/AljexSectionForms/characterLimits';
import { LoadBuildingTextInput } from 'pages/QuoteView/LoadBuilding/McleodLoadBuildingForm';
import { AdditionalReference, NormalizedLoad } from 'types/Load';

const refTypes = [
  { value: 'BL', label: 'BOL' },
  { value: 'BOL', label: 'BOL' },
  { value: 'LOAD', label: 'Load #' },
];

const maxRefs = 8;
const alwaysVisibleRefs = 2;
const genericRefs = maxRefs - alwaysVisibleRefs;

const AdditionalReferencesForm = ({
  formMethods,
}: {
  formMethods: UseFormReturn<NormalizedLoad>;
  isCreateMode?: boolean;
}) => {
  const { control, watch } = formMethods;

  // Initialize default additional references structure
  const initializeDefaultReferences = useCallback(
    (existingRefs: AdditionalReference[] = []) => {
      const defaultRefs = [
        {
          qualifier: 'BL',
          number: '',
          weight: 0,
          pieces: 0,
          shouldSendToDriver: false,
        },
        {
          qualifier: 'LOAD',
          number: '',
          weight: 0,
          pieces: 0,
          shouldSendToDriver: false,
        },
        // 6 generics, hidden by default
        ...Array.from({ length: genericRefs }, () => ({
          qualifier: '',
          number: '',
          weight: 0,
          pieces: 0,
          shouldSendToDriver: false,
        })),
      ];

      // If we have existing additional references from the load, use them
      if (existingRefs && existingRefs.length > 0) {
        existingRefs.forEach((ref: AdditionalReference) => {
          if (ref.qualifier === 'BL' || ref.qualifier === 'BOL') {
            defaultRefs[0] = {
              qualifier: ref.qualifier,
              number: ref.number,
              weight: ref.weight,
              pieces: ref.pieces,
              shouldSendToDriver: ref.shouldSendToDriver,
            };
          } else if (ref.qualifier === 'LOAD') {
            defaultRefs[1] = {
              qualifier: ref.qualifier,
              number: ref.number,
              weight: ref.weight,
              pieces: ref.pieces,
              shouldSendToDriver: ref.shouldSendToDriver,
            };
          } else {
            // For generic references (REF1, REF2, etc.), find the first available slot
            const availableIndex = defaultRefs.findIndex(
              (dr, index) => index >= 2 && dr.number === ''
            );
            if (availableIndex !== -1) {
              defaultRefs[availableIndex] = {
                qualifier: ref.qualifier,
                number: ref.number,
                weight: ref.weight,
                pieces: ref.pieces,
                shouldSendToDriver: ref.shouldSendToDriver,
              };
            }
          }
        });
      }

      return defaultRefs;
    },
    []
  );

  const { fields, replace } = useFieldArray({
    control,
    name: 'additionalReferences',
  });

  const [expanded, setExpanded] = useState(false);
  const watchedAdditionalReferences = watch('additionalReferences');

  // reinitialize additional references when load data changes (for both edit mode and AI suggestions)
  useEffect(() => {
    if (
      !watchedAdditionalReferences ||
      watchedAdditionalReferences.length < maxRefs
    ) {
      replace(initializeDefaultReferences(watchedAdditionalReferences));
    }

    if (watchedAdditionalReferences.length > 2) {
      setExpanded(true);
    } else {
      setExpanded(false);
    }
  }, [watchedAdditionalReferences, replace, initializeDefaultReferences]);

  // Only show first 2 unless expanded
  const visibleFields = expanded ? fields : fields.slice(0, alwaysVisibleRefs);

  return (
    <div className='grid grid-cols-1 gap-6 mx-0 w-full'>
      {/* Additional References Section */}
      <div>
        <div className='mb-4'>
          <div className='flex items-center gap-2'>
            <span className='text-sm font-medium text-neutral-700'>
              Additional References
            </span>
          </div>
          <p className='text-sm text-neutral-500 mt-1'>
            Add reference numbers like Bill of Lading, Load numbers, etc.
          </p>
        </div>
        <div className='grid grid-cols-1 gap-4 mx-0 w-full'>
          <div className='space-y-2'>
            {Array.from({ length: Math.ceil(visibleFields.length / 2) }).map(
              (_, rowIdx) => (
                <div key={rowIdx} className='flex gap-4'>
                  {visibleFields
                    .slice(rowIdx * 2, rowIdx * 2 + 2)
                    .map((field, colIdx) => {
                      const fieldIndex = rowIdx * 2 + colIdx;
                      const ref = field;
                      return (
                        <div key={field.id} className='flex-1'>
                          <LoadBuildingTextInput
                            name={`additionalReferences.${fieldIndex}.number`}
                            label={
                              ref.qualifier
                                ? refTypes.find(
                                    (t) => t.value === ref.qualifier
                                  )?.label || 'Reference'
                                : `Reference #${fieldIndex + 1}`
                            }
                            placeholder={
                              ref.qualifier
                                ? refTypes.find(
                                    (t) => t.value === ref.qualifier
                                  )?.label || 'Reference'
                                : `Reference #${fieldIndex + 1}`
                            }
                          />
                        </div>
                      );
                    })}
                </div>
              )
            )}
          </div>

          <div className='flex items-center justify-center mt-2'>
            <Button
              type='button'
              variant='ghost'
              size='sm'
              onClick={() => setExpanded((v: boolean) => !v)}
              className='h-8 px-3 text-xs text-info-500 flex items-center gap-1'
              buttonNamePosthog={null}
            >
              {expanded ? (
                <ChevronUpIcon className='h-4 w-4' />
              ) : (
                <ChevronDownIcon className='h-4 w-4' />
              )}
              <span>
                {expanded
                  ? 'Hide extra references'
                  : 'Expand to add more references'}
              </span>
            </Button>
          </div>
          <span className='text-xs text-neutral-500 text-center block'>
            {expanded ? maxRefs : alwaysVisibleRefs}/{maxRefs} references
          </span>
        </div>
      </div>
      {/* Special Instructions Section */}
      <Flex direction='col' gap='sm' className='mt-4'>
        <Label name='specifications.planningComment'>
          Special Instructions
        </Label>
        <Controller
          control={control}
          name='specifications.planningComment'
          render={({ field: { onChange, value } }) => (
            <Textarea
              className='p-2 h-20 whitespace-pre-wrap focus-visible:ring-transparent'
              onChange={onChange}
              value={value}
              placeholder='Enter special instructions'
              maxLength={
                ALJEX_CHARACTER_LIMITS['specifications.planningComment']
              }
            />
          )}
        />
      </Flex>
    </div>
  );
};
export default AdditionalReferencesForm;
