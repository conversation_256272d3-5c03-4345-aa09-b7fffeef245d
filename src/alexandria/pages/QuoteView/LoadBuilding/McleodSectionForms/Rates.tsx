import { useContext, useEffect, useState } from 'react';
import { JSX } from 'react';
import { Controller, FieldPath, UseFormReturn } from 'react-hook-form';

import { ErrorMessage } from '@hookform/error-message';
import { Select as AntdSelect } from 'antd';
import { BaseOptionType } from 'antd/es/select';

import { Label } from 'components/Label';
import { Grid } from 'components/layout';
import { Typography } from 'components/typography';
import { SidebarStateContext } from 'contexts/sidebarStateContext';
import { useAuth } from 'hooks/useAuth';
import { useLoadContext } from 'hooks/useLoadContext';
import { useServiceFeatures } from 'hooks/useServiceContext';
import useTMSContext from 'hooks/useTMSContext';
import {
  fetchRevenueCodes,
  tridentRevenueCodes,
} from 'pages/QuoteView/LoadBuilding/McleodSectionForms/constants';
import { NormalizedLoad } from 'types/Load';
import { getFieldAttribute } from 'types/LoadAttributes';
import { Maybe } from 'types/UtilityTypes';
import { TMS } from 'types/enums/Integrations';
import { SuggestionPipelines } from 'types/suggestions/CoreSuggestions';
import captureException from 'utils/captureException';
import { titleCase } from 'utils/formatStrings';

import {
  LoadBuildingTextInput,
  RateType,
  devDisableRequiredFields,
} from '../McleodLoadBuildingForm';

export function RatesForm({
  formMethods,
  showCarrierFields,
}: {
  formMethods: UseFormReturn<NormalizedLoad>;
  showCarrierFields: boolean; // Show in Load Info, read-only mode. Not in Load Building bc it's broken rn
}): JSX.Element {
  const {
    currentState: { clickedSuggestion },
  } = useContext(SidebarStateContext);
  const { serviceID, tmsIntegrations } = useServiceFeatures();
  const { user } = useAuth();

  const tms = tmsIntegrations?.find((tms) => tms.name === TMS.McleodEnterprise);

  // const isSalesperson1Enabled = tms?.tenant.includes('fcfm'); // Fetch Freight only for now
  const isSalesperson1Enabled = false;
  const hasSalesperson1 = formMethods.watch('rateData.salesperson1');

  const {
    control,
    formState: { errors },
  } = formMethods;
  //  TODO: Get from tms.GetLoadBuildingAttributes ENG-3493

  const getRevenueCodes = () => {
    if (tms?.tenant.includes('trident')) {
      return tridentRevenueCodes;
    }
    if (tms?.tenant.includes('fcfm')) {
      return fetchRevenueCodes;
    }

    captureException(
      `No revenue codes configured for Mcleod tenant ${tms?.tenant}`,
      {
        tenant: tms?.tenant,
        serviceID,
        tmsID: tms?.id,
      }
    );
    return [];
  };

  const revenueCodes = getRevenueCodes();

  const collectionMethods = [
    { value: 'P', label: 'Prepaid' },
    { value: 'C', label: 'Collect' },
    { value: 'T', label: 'Third-Party' },
    { value: 'D', label: 'COD' },
  ];

  return (
    <Grid cols='4' gap='sm' className='mx-0 w-full'>
      {isSalesperson1Enabled && (
        <>
          <div className='col-span-2'>
            <Label
              hideAIHint={true}
              name={'rateData.salesperson1'}
              required={false}
            >
              Salesperson 1
            </Label>
            <Controller
              name='rateData.salesperson1'
              control={control}
              rules={{ required: false }}
              render={({ field }) => (
                <div className='text-neutral-500'>
                  <AntdSelect
                    showSearch
                    disabled={false}
                    className='h-9 text-neutral-500'
                    placeholder={'Choose'}
                    optionFilterProp='children'
                    filterOption={(
                      input: string,
                      option: BaseOptionType | undefined
                    ) =>
                      (option?.label.toLocaleLowerCase() ?? '').includes(
                        input.toLocaleLowerCase()
                      )
                    }
                    filterSort={(
                      optionA: BaseOptionType,
                      optionB: BaseOptionType
                    ) =>
                      (optionA?.label ?? '')
                        .toLowerCase()
                        .localeCompare((optionB?.label ?? '').toLowerCase())
                    }
                    onChange={field.onChange}
                    value={field.value}
                    options={[
                      {
                        value: user?.external_tms_id ?? '',
                        label: user?.external_tms_id ?? '',
                      },
                    ]}
                  />
                </div>
              )}
            />
            <ErrorMessage
              errors={errors}
              name={'rateData.salesperson1'}
              render={({ message }: { message: string }) => (
                <Typography variant='body-xs' className='text-error-500'>
                  {message}
                </Typography>
              )}
            />
          </div>
          <div className='col-span-2'>
            <div className='relative'>
              <LoadBuildingTextInput
                name='rateData.salesperson1Percent'
                label='Percentage'
                readOnly={!hasSalesperson1}
                options={{
                  valueAsNumber: true,
                  min: 0,
                  max: 100,
                }}
                placeholder='(0-100)'
                inputClassName='pr-6'
              />
              <span className='absolute right-2 top-[30px] text-sm text-neutral-800 pointer-events-none'>
                %
              </span>
            </div>
          </div>
        </>
      )}
      {/* Customer Rates */}
      <>
        <div className='col-span-4 text-md text-neutral-800 font-semibold mb-1'>
          Customer
        </div>
        <div className='col-span-2'>
          <Label name={'rateData.collectionMethod'} required={true}>
            Collection Method
          </Label>
          <Controller
            name='rateData.collectionMethod'
            control={control}
            rules={{ required: devDisableRequiredFields ? false : 'Required' }}
            render={({ field }) => (
              <div className=' text-neutral-500'>
                <AntdSelect
                  showSearch
                  className='h-9 text-neutral-500'
                  placeholder={'Choose'}
                  optionFilterProp='children'
                  filterOption={(
                    input: string,
                    option: BaseOptionType | undefined
                  ) =>
                    (option?.label.toLocaleLowerCase() ?? '').includes(
                      input.toLocaleLowerCase()
                    )
                  }
                  filterSort={(
                    optionA: BaseOptionType,
                    optionB: BaseOptionType
                  ) =>
                    (optionA?.label ?? '')
                      .toLowerCase()
                      .localeCompare((optionB?.label ?? '').toLowerCase())
                  }
                  onChange={field.onChange}
                  value={field.value}
                  options={collectionMethods?.map((method) => ({
                    value: method.label,
                    label: method.label,
                  }))}
                />
              </div>
            )}
          />
          <ErrorMessage
            errors={errors}
            name={'rateData.collectionMethod'}
            render={({ message }: { message: string }) => (
              <Typography variant='body-xs' className='text-error-500'>
                {message}
              </Typography>
            )}
          />
        </div>
        <div className='col-span-2'>
          <Label
            name={'rateData.revenueCode'}
            required={true}
            // We always default this value so don't show AI hint unless there's a suggestion
            hideAIHint={
              !clickedSuggestion ||
              clickedSuggestion.pipeline !== SuggestionPipelines.LoadBuilding
            }
          >
            Revenue Code
          </Label>
          <Controller
            name='rateData.revenueCode'
            control={control}
            rules={{ required: devDisableRequiredFields ? false : 'Required' }}
            render={({ field }) => (
              <div className=' text-neutral-500'>
                <AntdSelect
                  showSearch
                  className='h-9 text-neutral-500'
                  placeholder={'Choose'}
                  optionFilterProp='children'
                  filterOption={(
                    input: string,
                    option: BaseOptionType | undefined
                  ) =>
                    (option?.label.toLocaleLowerCase() ?? '').includes(
                      input.toLocaleLowerCase()
                    )
                  }
                  filterSort={(
                    optionA: BaseOptionType,
                    optionB: BaseOptionType
                  ) =>
                    (optionA?.label ?? '')
                      .toLowerCase()
                      .localeCompare((optionB?.label ?? '').toLowerCase())
                  }
                  onChange={field.onChange}
                  value={field.value}
                  options={revenueCodes?.map((code) => ({
                    value: code.value,
                    label: code.label,
                  }))}
                />
              </div>
            )}
          />
          <ErrorMessage
            errors={errors}
            name={'rateData.revenueCode'}
            render={({ message }: { message: string }) => (
              <Typography variant='body-xs' className='text-error-500'>
                {message}
              </Typography>
            )}
          />
        </div>
        {<RateGrid party='customer' formMethods={formMethods} />}
      </>
      {/* Carrier Rates */}
      {/* FIXME: Mcleod PUT carrier rates and dispatch not working */}
      {showCarrierFields && (
        <>
          <hr className='col-span-4 my-1' />
          <div className='col-span-4 text-md text-neutral-800 font-semibold mb-1'>
            Carrier
          </div>

          <RateGrid party='carrier' formMethods={formMethods} />
        </>
      )}
    </Grid>
  );
}

{
  /* TODO: Add support for other charges and TOTAL charge (fuel surcharge) */
}

function RateGrid({
  party,
  formMethods,
}: {
  party: 'customer' | 'carrier';
  formMethods: UseFormReturn<NormalizedLoad>;
}): JSX.Element {
  const {
    control,
    formState: { errors },
    setValue,
    clearErrors,
    watch,
    setError,
  } = formMethods;

  const { fieldAttributes } = useLoadContext();
  const { tmsName } = useTMSContext();

  // Mcleod calculates distance after creating load
  const [isRatingTypeDistance, setIsRateTypeDistance] = useState(false);

  // Watch relevant fields from the form
  const watchedWeight = watch('specifications.totalWeight.val');
  const watchedDistance = watch('specifications.totalDistance.val');

  const watchedRateType = watch(`rateData.${party}RateType`);
  const watchedRate = watch(`rateData.${party}LineHaulRate`);

  function calculateInputs(
    party: 'customer' | 'carrier',
    rateType: string,
    weight: number,
    distance: number,
    rate: Maybe<number>
  ) {
    let ratingUnits: Maybe<number> = 1;
    setIsRateTypeDistance(false);
    clearErrors('specifications.totalWeight.val');
    clearErrors(
      `rateData.${party}LineHaulCharge.val` as FieldPath<NormalizedLoad>
    );

    switch (rateType) {
      case 'Flat':
        ratingUnits = 1; // Flat rate always has 1 unit
        break;

      case 'Distance':
        setIsRateTypeDistance(true);
        ratingUnits = null;
        // *After* load is created, Mcleod calculates distance so we can calculate rating units when user makes updates
        if (distance) {
          ratingUnits = distance;
        } else {
          // When user is load building, we don't know the distance yet, so we can't calculate rating units
          ratingUnits = null;
        }
        break;

      case 'CWT': // Hundredweight = 100 pounds
        if (weight) {
          ratingUnits = weight / 100;
        } else {
          setError('specifications.totalWeight.val', {
            message: 'Required for weight-based rate',
          });
          setError(
            `rateData.${party}LineHaulCharge.val` as FieldPath<NormalizedLoad>,
            {
              message: 'Missing weight',
            }
          );
        }
        break;

      case 'Tons': // Tons = 2000 pounds
        if (weight) {
          ratingUnits = weight / 2000;
        } else {
          setError('specifications.totalWeight.val', {
            message: 'Required for weight-based rate',
          });
          setError(
            `rateData.${party}LineHaulCharge.val` as FieldPath<NormalizedLoad>,
            {
              message: 'Missing weight',
            }
          );
        }
        break;

      default:
        ratingUnits = null;
        break;
    }

    // Set calculated values in the form
    setValue(
      `rateData.${party}RateNumUnits` as FieldPath<NormalizedLoad>,
      ratingUnits
    );

    if (ratingUnits && rate) {
      setValue(
        `rateData.${party}LineHaulCharge.val` as FieldPath<NormalizedLoad>,
        ratingUnits * rate
      );
    } else {
      setValue(
        `rateData.${party}LineHaulCharge.val` as FieldPath<NormalizedLoad>,
        null
      );
    }
  }

  useEffect(() => {
    calculateInputs(
      party,
      watchedRateType,
      watchedWeight,
      watchedDistance,
      watchedRate
    );
  }, [watch, setValue, watchedRate, watchedRateType, watchedWeight]);

  return (
    <>
      {/* Rate Type */}
      <div className='col-span-2'>
        <Label name={`rateData.${party}RateType` as FieldPath<NormalizedLoad>}>
          Rate Type
        </Label>
        <Controller
          name={`rateData.${party}RateType` as FieldPath<NormalizedLoad>}
          control={control}
          render={({ field }) => (
            <div className='text-neutral-500'>
              <AntdSelect
                showSearch
                className='h-9 text-neutral-500'
                placeholder={'Choose'}
                optionFilterProp='children'
                filterOption={(
                  input: string,
                  option: BaseOptionType | undefined
                ) =>
                  (option?.label.toLocaleLowerCase() ?? '').includes(
                    input.toLocaleLowerCase()
                  )
                }
                filterSort={(
                  optionA: BaseOptionType,
                  optionB: BaseOptionType
                ) =>
                  (optionA?.label ?? '')
                    .toLowerCase()
                    .localeCompare((optionB?.label ?? '').toLowerCase())
                }
                onChange={field.onChange}
                value={field.value}
                disabled={
                  getFieldAttribute(
                    fieldAttributes,
                    `rateData.${party}RateType`
                  )?.isReadOnly
                }
                options={Object.entries(RateType).map(([key]) => ({
                  value: key,
                  label: key,
                }))}
              />
            </div>
          )}
        />
        <ErrorMessage
          errors={errors}
          name={`rateData.${party}RateType` as FieldPath<NormalizedLoad>}
          render={({ message }: { message: string }) => (
            <Typography variant='body-xs' className='text-error-500'>
              {message}
            </Typography>
          )}
        />
      </div>
      {/* Rating Units (Read-only) */}
      <div className='col-span-2'>
        <LoadBuildingTextInput
          name={`rateData.${party}RateNumUnits` as FieldPath<NormalizedLoad>}
          label='Rating Units'
          // NOTE: Use disabled instead of readOnly so string placeholder is shown
          options={{ valueAsNumber: true, disabled: true }}
          placeholder={
            isRatingTypeDistance ? `${titleCase(tmsName)} TBD` : undefined
          }
        />
      </div>
      <div className='col-span-2'>
        {/* Line Haul Rate */}
        <LoadBuildingTextInput
          name={`rateData.${party}LineHaulRate` as FieldPath<NormalizedLoad>}
          label='Line Haul Rate'
          options={{ valueAsNumber: true }}
        />
      </div>
      {/* Line Haul Charge = Rate * Units */}
      <div className='col-span-2'>
        <LoadBuildingTextInput
          name={
            `rateData.${party}LineHaulCharge.val` as FieldPath<NormalizedLoad>
          }
          label='Line Haul Charge'
          placeholder={
            isRatingTypeDistance ? `${titleCase(tmsName)} TBD` : undefined
          }
          // NOTE: Use disabled instead of readOnly so string placeholder is shown
          options={{ valueAsNumber: true, disabled: true }}
        />
      </div>
    </>
  );
}
