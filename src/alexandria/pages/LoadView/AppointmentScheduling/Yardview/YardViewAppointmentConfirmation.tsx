import { Typography } from 'components/typography/Typography';

interface YardViewAppointmentConfirmationProps {
  apptConfirmationNumber: string;
  tmsUpdateSucceeded: boolean;
  apptConfirmationUrl: string;
}

export function YardViewAppointmentConfirmation({
  apptConfirmationNumber,
  tmsUpdateSucceeded,
  apptConfirmationUrl,
}: YardViewAppointmentConfirmationProps) {
  return (
    <div className='whitespace-pre-wrap my-3 rounded py-3 text-neutral-700 px-4 bg-success-200'>
      <Typography variant='body-sm' className='mb-2'>
        Appointment confirmed 🎉
      </Typography>
      <Typography variant='body-sm' weight='semibold' className='mb-4'>
        YardView Confirmation #: {apptConfirmationNumber}
      </Typography>

      <Typography variant='body-sm' className='mb-1'>
        {tmsUpdateSucceeded
          ? `Your TMS was also updated with the appointment details`
          : `Make sure to update your TMS with the scheduled appointment.`}
      </Typography>

      {apptConfirmationUrl && (
        <Typography variant='body-sm' className='mt-3'>
          Please{' '}
          <a
            href={apptConfirmationUrl}
            target='_blank'
            rel='noopener noreferrer'
            className='text-accent underline'
          >
            click here
          </a>{' '}
          to visit the appointment page.
        </Typography>
      )}
    </div>
  );
}
