import {
  CustomApptFieldsTemplate,
  StopTypes,
  WarehouseSettings,
} from 'types/Appointment';
import { NormalizedLoad } from 'types/Load';
import { Warehouse } from 'types/Warehouse';

import {
  SevenElevenMarylandID,
  SevenElevenVirginiaID,
  aiSuggestCustomApptFields,
  removeSchedulerInfo,
  suggestPROCustomFields,
  suggestRefNumberField,
} from './helpers';

describe('aiSuggestCustomApptFields', () => {
  const mockLoad = {
    carrier: {
      trailerNumber: 'TR1234',
      firstDriverPhone: '************',
      name: 'CarrierName',
    },
    poNums: 'PO123,PO124,PO1235',
    customer: {
      refNumber: 'REF123',
      name: 'CustomerName',
    },
    consignee: {
      refNumber: 'ConsigneeRef',
    },
    pickup: {
      refNumber: 'PickupRef',
    },
    specifications: {
      totalOutPalletCount: 2,
    },
    externalTMSID: 'TMS123',
    freightTrackingID: 'FTID123',
  } as NormalizedLoad;

  const mockCustomFields = [
    {
      name: 'strTrailer',
      label: 'Trailer',
      description: '',
      value: '',
      requiredForCarrier: true,
      dropDownValues: [],
    },
    {
      name: 'dropdownVehicleType',
      label: 'Vehicle Type',
      description: '',
      value: '',
      requiredForCarrier: true,
      dropDownValues: [],
    },
    {
      name: 'intMisc1',
      label: 'Misc',
      description: '',
      value: '',
      requiredForCarrier: true,
      dropDownValues: [],
    },
    {
      name: 'dropdownOrder Type',
      label: 'Order Type',
      description: '',
      value: '',
      requiredForCarrier: true,
      dropDownValues: [],
    },
    {
      name: 'phoneCell Phone',
      label: 'Cell Phone',
      description: '',
      value: '',
      requiredForCarrier: true,
      dropDownValues: [],
    },
    {
      name: 'strHauling Carrier Name',
      label: 'Hauling Carrier Name',
      description: '',
      value: '',
      requiredForCarrier: true,
      dropDownValues: [],
    },
    {
      name: 'strOrder 1',
      label: 'PO Number 1',
      description: '',
      value: '',
      requiredForCarrier: true,
      dropDownValues: [],
    },
    {
      name: 'strOrder 2',
      label: 'PO Number 2',
      description: '',
      value: '',
      requiredForCarrier: true,
      dropDownValues: [],
    },
    {
      name: 'strOrder3',
      label: 'PO Number 2',
      description: '',
      value: '',
      requiredForCarrier: true,
      dropDownValues: [],
    },
  ] as unknown as CustomApptFieldsTemplate[];

  const mock711VAWarehouse: Warehouse = {
    warehouseID: SevenElevenVirginiaID,
  } as Warehouse;

  const mock711MDWarehouse: Warehouse = {
    warehouseID: SevenElevenMarylandID,
  } as Warehouse;

  const mockOtherWarehouse: Warehouse = {
    warehouseID: 'SomeOtherWarehouseID',
    warehouseName: 'someWarehouse',
  } as Warehouse;

  const mockOMWarehouse: Warehouse = {
    warehouseID: 'mockOMWarehouse',
    warehouseName: 'OM Memphis',
  } as Warehouse;

  beforeEach(() => {
    mockCustomFields.map((field) => (field.value = ''));
  });
  it('should correctly map fields for SevenElevenVirginiaID', () => {
    aiSuggestCustomApptFields(
      mockLoad,
      StopTypes.Pickup,
      mock711VAWarehouse,
      mockCustomFields
    );

    expect(
      mockCustomFields.find((field) => field.name === 'strTrailer')?.value
    ).toBe(mockLoad.carrier.trailerNumber);
    expect(
      mockCustomFields.find((field) => field.name === 'dropdownVehicleType')
        ?.value
    ).toBe('Straight Truck');
    expect(
      mockCustomFields.find((field) => field.name === 'intMisc1')?.value
    ).toBe(mockLoad.specifications.totalOutPalletCount?.toString());
    expect(
      mockCustomFields.find((field) => field.name === 'dropdownOrder Type')
        ?.value
    ).toBe('PO');
    expect(
      mockCustomFields.find((field) => field.name === 'strOrder 1')?.value
    ).toBe('PO123');
    expect(
      mockCustomFields.find((field) => field.name === 'strOrder 2')?.value
    ).toBe('PO124');

    expect(
      mockCustomFields.find((field) => field.name === 'strOrder3')?.value
    ).toBe('PO1235');
  });

  it('should correctly map fields for SevenElevenMarylandID', () => {
    aiSuggestCustomApptFields(
      mockLoad,
      StopTypes.Dropoff,
      mock711MDWarehouse,
      mockCustomFields
    );

    expect(
      mockCustomFields.find((field) => field.name === 'phoneCell Phone')?.value
    ).toBe(mockLoad.carrier.firstDriverPhone);
    expect(
      mockCustomFields.find((field) => field.name === 'strHauling Carrier Name')
        ?.value
    ).toBe(mockLoad.carrier.name);
  });

  it('should correctly map fields for other warehouse IDs', () => {
    aiSuggestCustomApptFields(
      mockLoad,
      StopTypes.Dropoff,
      mockOtherWarehouse,
      mockCustomFields
    );

    expect(
      mockCustomFields.find((field) => field.name === 'strTrailer')?.value
    ).toBe('');
    expect(
      mockCustomFields.find((field) => field.name === 'dropdownVehicleType')
        ?.value
    ).toBe('');
    expect(
      mockCustomFields.find((field) => field.name === 'intMisc1')?.value
    ).toBe('');
    expect(
      mockCustomFields.find((field) => field.name === 'dropdownOrder Type')
        ?.value
    ).toBe('');

    expect(
      mockCustomFields.find((field) => field.name === 'strOrder 1')?.value
    ).toBe(mockLoad.poNums);
    expect(
      mockCustomFields.find((field) => field.name === 'strOrder 2')?.value
    ).toBe(mockLoad.poNums);
    expect(
      mockCustomFields.find((field) => field.name === 'strOrder3')?.value
    ).toBe(mockLoad.poNums);
  });

  it('should match and set values for custom fields without dropdowns', () => {
    const customFieldsWithMatch = [
      ...mockCustomFields,
      {
        name: 'strCustomerName',
        label: 'Customer Name',
        description: 'Enter customer name',
        value: '',
        requiredForCarrier: true,
        dropDownValues: [],
      },
    ] as CustomApptFieldsTemplate[];

    aiSuggestCustomApptFields(
      mockLoad,
      StopTypes.Dropoff,
      mockOtherWarehouse,
      customFieldsWithMatch
    );

    expect(
      customFieldsWithMatch.find((field) => field.name === 'strCustomerName')
        ?.value
    ).toBe(mockLoad.customer.name);
  });

  it('should use Fuse.js to match and set dropdown values', () => {
    const customFieldsWithDropdowns = [
      ...mockCustomFields,
      {
        name: 'dropdownCustomerName',
        label: 'Customer Name',
        description: 'Select customer name',
        value: '',
        requiredForCarrier: true,
        dropDownValues: ['CustomerName', 'AnotherCustomer'],
      },
    ] as CustomApptFieldsTemplate[];

    aiSuggestCustomApptFields(
      mockLoad,
      StopTypes.Dropoff,
      mockOtherWarehouse,
      customFieldsWithDropdowns
    );

    expect(
      customFieldsWithDropdowns.find(
        (field) => field.name === 'dropdownCustomerName'
      )?.value
    ).toBe('CustomerName');
  });

  it('should set field value to load.freightTrackingID if field label includes "PRO OR BOL"', () => {
    const testLoad = {
      ...mockLoad,
      externalTMSID: '',
    } as NormalizedLoad;
    const customFields: CustomApptFieldsTemplate[] = [
      ...mockCustomFields,
      {
        name: 'field1',
        label: 'PRO OR BOL Number',
        description: '',
        requiredForCarrier: true,
        dropDownValues: [],
        value: '',
      },
    ] as CustomApptFieldsTemplate[];

    aiSuggestCustomApptFields(
      testLoad,
      StopTypes.Pickup,
      mockOtherWarehouse,
      customFields
    );

    expect(
      customFields.find((field) => field.label === 'PRO OR BOL Number')?.value
    ).toBe(mockLoad.freightTrackingID);
  });

  it('should set field value to load.externalTMSID if field label includes "PRO/BOL" or "BOL OR PRO"', () => {
    const customFields: CustomApptFieldsTemplate[] = [
      ...mockCustomFields,
      {
        name: 'field1',
        label: 'BOL OR PRO Number',
        description: '',
        requiredForCarrier: true,
        dropDownValues: [],
        value: '',
      },
    ] as CustomApptFieldsTemplate[];

    aiSuggestCustomApptFields(
      mockLoad,
      StopTypes.Dropoff,
      mockOMWarehouse,
      customFields
    );

    expect(
      customFields.find((field) => field.label === 'BOL OR PRO Number')?.value
    ).toBe(mockLoad.customer.refNumber);
  });
});

describe('suggestPROCustomFields', () => {
  it('should not modify fields if pro is undefined', () => {
    const customFields: CustomApptFieldsTemplate[] = [
      {
        name: 'Field 1',
        type: 'text',
        label: 'PRO Number',
        value: '',
        description: 'Enter PRO number',
        dropDownValues: [],
        hiddenFromCarrier: false,
        requiredForCarrier: true,
        requiredForCheckIn: false,
        requiredForWarehouse: false,
      },
    ];

    suggestPROCustomFields(customFields, undefined, undefined);

    expect(customFields[0].value).toBe('');
  });

  it('should set pro value', () => {
    const customFields: CustomApptFieldsTemplate[] = [
      {
        name: 'Field 1',
        type: 'text',
        label: 'PRO Number',
        value: '',
        description: 'Enter PRO number',
        dropDownValues: [],
        hiddenFromCarrier: false,
        requiredForCarrier: true,
        requiredForCheckIn: false,
        requiredForWarehouse: false,
      },
      {
        name: 'Field 2',
        type: 'text',
        label: 'Second Field',
        value: '',
        description: 'Another field here',
        dropDownValues: [],
        hiddenFromCarrier: false,
        requiredForCarrier: false,
        requiredForCheckIn: false,
        requiredForWarehouse: false,
      },
    ];

    suggestPROCustomFields(customFields, '12345', '');

    expect(customFields[0].value).toBe('12345');
    expect(customFields[1].value).toBe('');
  });

  it("should not set PRO because it's not required", () => {
    const customFields: CustomApptFieldsTemplate[] = [
      {
        name: 'Field 1',
        type: 'text',
        label: 'PRO Number',
        value: '',
        description: 'Enter PRO number',
        dropDownValues: [],
        hiddenFromCarrier: false,
        requiredForCarrier: false,
        requiredForCheckIn: false,
        requiredForWarehouse: false,
      },
      {
        name: 'Field 2',
        type: 'text',
        label: 'Second Field',
        value: '',
        description: 'Another field here',
        dropDownValues: [],
        hiddenFromCarrier: false,
        requiredForCarrier: false,
        requiredForCheckIn: false,
        requiredForWarehouse: false,
      },
    ];

    suggestPROCustomFields(customFields, '12345', '');

    expect(customFields[0].value).toBe('');
    expect(customFields[1].value).toBe('');
  });
  it('should not set pro value to fields with dropdown values', () => {
    const customFields: CustomApptFieldsTemplate[] = [
      {
        name: 'Field 1',
        type: 'text',
        label: 'PRO Number',
        value: '',
        description: 'Enter PRO number',
        dropDownValues: ['Option 1'],
        hiddenFromCarrier: false,
        requiredForCarrier: false,
        requiredForCheckIn: false,
        requiredForWarehouse: false,
      },
    ];

    suggestPROCustomFields(customFields, '12345', '');

    expect(customFields[0].value).toBe('');
  });

  it('should set pro value regardless of case', () => {
    const customFields: CustomApptFieldsTemplate[] = [
      {
        name: 'Field 1',
        type: 'text',
        label: 'pro number',
        value: '',
        description: '',
        dropDownValues: [],
        hiddenFromCarrier: false,
        requiredForCarrier: true,
        requiredForCheckIn: false,
        requiredForWarehouse: false,
      },
    ];

    suggestPROCustomFields(customFields, '12345', '');

    expect(customFields[0].value).toBe('12345');
  });
});

describe('suggestRefNumberField', () => {
  it('should return empty string if referenceNumberIsRequired is false', () => {
    const settings: WarehouseSettings = {
      referenceNumberIsRequired: false,
      referenceNumberHelperText: '',
      referenceNumberDisplayName: '',
      referenceNumberIsVisible: false,
      referenceNumberIsUnique: false,
    };

    const result = suggestRefNumberField(settings, 'PO12345', 'PRO12345');
    expect(result).toBe('');
  });

  it('should return pro number if suggestPRO is true', () => {
    const settings: WarehouseSettings = {
      referenceNumberIsRequired: true,
      referenceNumberHelperText: 'Please enter your PRO',
      referenceNumberDisplayName: 'PRO Number',
      referenceNumberIsVisible: true,
      referenceNumberIsUnique: false,
    };

    const result = suggestRefNumberField(settings, 'PO12345', 'PRO12345');
    expect(result).toBe('PRO12345');
  });

  it('should return po number if suggestPONum is true', () => {
    const settings: WarehouseSettings = {
      referenceNumberIsRequired: true,
      referenceNumberHelperText: 'Please enter your po number',
      referenceNumberDisplayName: 'PO Number',
      referenceNumberIsVisible: true,
      referenceNumberIsUnique: false,
    };

    const result = suggestRefNumberField(settings, 'PO12345', undefined);
    expect(result).toBe('PO12345');
  });

  it('should return PRO if no matching terms are found', () => {
    const settings: WarehouseSettings = {
      referenceNumberIsRequired: true,
      referenceNumberHelperText: 'Enter your reference number',
      referenceNumberDisplayName: 'Reference Number',
      referenceNumberIsVisible: true,
      referenceNumberIsUnique: false,
    };

    const result = suggestRefNumberField(settings, 'PO12345', 'PRO12345');
    expect(result).toBe('PRO12345');
  });

  it('should handle case insensitivity for pro terms', () => {
    const settings: WarehouseSettings = {
      referenceNumberIsRequired: true,
      referenceNumberHelperText: 'Enter your PRO',
      referenceNumberDisplayName: 'PRO Number',
      referenceNumberIsVisible: true,
      referenceNumberIsUnique: false,
    };

    const result = suggestRefNumberField(settings, 'PO12345', 'PRO12345');
    expect(result).toBe('PRO12345');
  });

  it('should handle case insensitivity for po terms', () => {
    const settings: WarehouseSettings = {
      referenceNumberIsRequired: true,
      referenceNumberHelperText: 'Enter your PO Number',
      referenceNumberDisplayName: 'PO NUMBER',
      referenceNumberIsVisible: true,
      referenceNumberIsUnique: false,
    };

    const result = suggestRefNumberField(settings, 'PO12345', undefined);
    expect(result).toBe('PO12345');
  });
});

describe('removeSchedulerInfo', () => {
  it('should remove "Opendock <number>" from the input string', () => {
    const input = 'Do not arrive early. Opendock 12345.';
    const result = removeSchedulerInfo(input);
    expect(result).toBe('Do not arrive early.');
  });

  it('should remove "Opendock <number>" even if there is no trailing period', () => {
    const input = 'Appointment set with Opendock 67890';
    const result = removeSchedulerInfo(input);
    expect(result).toBe('Appointment set with');
  });

  it('should remove "Opendock <number>" when it appears at the beginning of the string', () => {
    const input = 'Opendock 23456 confirmed the appointment';
    const result = removeSchedulerInfo(input);
    expect(result).toBe('confirmed the appointment');
  });

  it('should remove "Opendock <number>" when it appears in the middle of the string', () => {
    const input = 'Appointment confirmed by Opendock 34567 for tomorrow';
    const result = removeSchedulerInfo(input);
    expect(result).toBe('Appointment confirmed by  for tomorrow');
  });

  it('should return the input string if there is no match', () => {
    const input = 'Appointment confirmed without Opendock reference';
    const result = removeSchedulerInfo(input);
    expect(result).toBe(input);
  });
});
