import { useEffect, useRef, useState } from 'react';
import {
  Controller,
  FormProvider,
  SubmitErrorHandler,
  useForm,
} from 'react-hook-form';

import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';

import { Button } from 'components/Button';
import { Checkbox } from 'components/Checkbox';
import { DatePicker } from 'components/DatePicker';
import {
  DebounceSelect,
  GenericLocationOption,
} from 'components/DebounceSelect';
import { Label } from 'components/Label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from 'components/Select';
import { Textarea } from 'components/Textarea';
import { Flex, Grid } from 'components/layout';
import ButtonLoader from 'components/loading/ButtonLoader';
import { Typography } from 'components/typography';
import { ExtendedFormProvider } from 'contexts/extendedFormContext';
import { useToast } from 'hooks/useToaster';
import { confirmSlotAppt } from 'lib/api/confirmSlotAppt';
import { getWarehouseSearch } from 'lib/api/getWarehouseSearch';
import { getOpenApptSlots } from 'lib/api/openApptSlots';
import { submitAppt } from 'lib/api/submitAppt';
import { validateAppt } from 'lib/api/validateAppt';
import {
  GroupedSlot,
  OrderedSlots,
  SchedulingPortals,
  StopTypes,
} from 'types/Appointment';
import { NormalizedLoad } from 'types/Load';
import { Maybe } from 'types/UtilityTypes';
import { Warehouse } from 'types/Warehouse';
import ButtonText from 'types/enums/ButtonText';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';
import { cn } from 'utils/shadcn';

import TimeSlotButton from '../TimeSlotButton';
import WarehouseDisplay from '../WarehouseDisplay';
import { RetalixPOList } from './RetalixPOList';

dayjs.extend(utc);
dayjs.extend(timezone);

// Calling object PurchaseOrders instead of PONumbers to avoid stuttering
export type PurchaseOrder = {
  number: string;
  isValid: boolean;
  error: string;
};

// Add time preference options
const timePreferences = [
  { value: 'Anytime', label: 'Anytime' },
  { value: 'Before Noon', label: 'Before Noon' },
  { value: 'Noon - 6pm', label: 'Noon - 6pm' },
  { value: 'After 6pm', label: 'After 6pm' },
];

interface RetalixInputsWithoutLoad {
  shouldRequestLumper: boolean;
  apptNote: string;
  poNumbers: string;
  requestedDate?: Date;
  timePreference?: string;
}

type RetalixFormProps = {
  type: StopTypes;
  load: NormalizedLoad;
  integrationId?: number;
  recentWarehouses: Warehouse[];
  selectedWarehouse: Maybe<Warehouse>;
  setSelectedWarehouse: (warehouse: Maybe<Warehouse>) => void;
};

export function RetalixForm({
  type,
  load,
  integrationId,
  recentWarehouses,
  selectedWarehouse,
  setSelectedWarehouse,
}: RetalixFormProps) {
  const { toast } = useToast();
  const formMethods = useForm<RetalixInputsWithoutLoad>();
  const { control, handleSubmit, getValues } = formMethods;
  const scrollResultsIntoViewRef = useRef<HTMLDivElement>(null);

  const [warehouses, setWarehouses] = useState<Warehouse[]>(
    recentWarehouses && recentWarehouses.length ? recentWarehouses : []
  );
  const [uniqueWarehouseSources, setUniqueWarehouseSources] = useState<
    string[]
  >([]);
  const [isInitialSearch, setIsInitialSearch] = useState(true);
  const [hasOverriddenSuggestedWarehouse, setHasOverriddenSuggestedWarehouse] =
    useState(false);

  const [isValidatingPOs, setIsValidatingPOs] = useState(false);
  const [isLoadingSlots, setIsLoadingSlots] = useState(false);
  const [isLoadingSubmit, setIsLoadingSubmit] = useState(false);
  const [isLoadingConfirm, setIsLoadingConfirm] = useState(false);
  const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrder[]>([]);
  const [orderedSlots, setOrderedSlots] = useState<Maybe<OrderedSlots>>(null);
  const [selectedSlot, setSelectedSlot] = useState<Maybe<GroupedSlot>>(null);
  const [apptConfirmationNumber, setApptConfirmationNumber] = useState('');
  const [liveAppointmentsAvailable, setLiveAppointmentsAvailable] =
    useState(true);

  useEffect(() => {
    if (warehouses) {
      setUniqueWarehouseSources([SchedulingPortals.Retalix]);
    }
  }, [warehouses]);

  useEffect(() => {
    if (
      selectedWarehouse &&
      selectedWarehouse.warehouseSource == SchedulingPortals.Retalix &&
      !hasOverriddenSuggestedWarehouse
    ) {
      // This effect is no longer needed since we're using external state
      // The warehouse is already set from the parent component
    }
  }, [selectedWarehouse, hasOverriddenSuggestedWarehouse]);

  useEffect(() => {
    setHasOverriddenSuggestedWarehouse(false);
  }, [type]);

  const handleResetWarehouseSearch = () => {
    setIsInitialSearch(true);
    setWarehouses(recentWarehouses);
  };

  const handleWarehouseSearch = async (search: string) => {
    if (search.length > 3) {
      setIsInitialSearch(false);
      const searchRes = await getWarehouseSearch(search);
      if (searchRes.isOk()) {
        const { warehouses: searchedWarehouses } = searchRes.value ?? {
          warehouses: [],
        };
        // Prevent filtering on null/undefined result from API
        const safeSearchedWarehouses = Array.isArray(searchedWarehouses)
          ? searchedWarehouses
          : [];

        const retalixWarehouses = safeSearchedWarehouses.filter(
          (w) => w && w.warehouseSource === SchedulingPortals.Retalix
        );
        setWarehouses(retalixWarehouses);
        return retalixWarehouses && retalixWarehouses.length
          ? mapWarehousesToOptions(retalixWarehouses)
          : [];
      }
    }
    handleResetWarehouseSearch();
    return mapWarehousesToOptions(recentWarehouses).filter((wh) =>
      wh.label.toLocaleLowerCase().includes(search.toLocaleLowerCase())
    );
  };

  const mapWarehousesToOptions = (warehouses: Warehouse[]) =>
    warehouses?.map((option: Warehouse) => ({
      ...option,
      value: option.warehouseID,
      name: option.warehouseName,
      mainAddress: option.warehouseAddressLine1,
      secondaryAddress: option.warehouseAddressLine2,
      source: option.warehouseSource,
      label: option.warehouseName,
    }));

  useEffect(() => {
    setOrderedSlots(null);
  }, [selectedWarehouse?.warehouseID]);

  useEffect(() => {
    if (load.poNums && load.poNums !== '') {
      setPurchaseOrders(
        load.poNums
          .split(load.poNums?.includes(',') ? ',' : '/')
          .map((poNum) => ({ number: poNum, isValid: false, error: '' }))
      );
    }
  }, [load]);

  const validatePONumbers = async (poNumbers: string[]) => {
    setIsValidatingPOs(true);
    try {
      const validateRes = await validateAppt(
        selectedWarehouse?.warehouseID ?? '',
        selectedWarehouse?.warehouseSource ?? SchedulingPortals.Retalix,
        poNumbers
      );
      if (validateRes.isOk()) {
        const { validatedPONumbers } = validateRes.value;
        if (validatedPONumbers.length === 0) {
          toast({
            description: 'No PO numbers to validate.',
            variant: 'default',
          });
          return false;
        }
        setPurchaseOrders(
          validatedPONumbers.map((poNum) => ({
            number: poNum.poNumber,
            isValid: poNum.isValid,
            error: poNum.error,
          }))
        );
        const allPOsValid = validatedPONumbers.every(
          (po) => po.isValid && !po.error
        );
        if (!allPOsValid) {
          toast({
            description: 'Some PO numbers are invalid.',
            variant: 'destructive',
          });
        }
        return allPOsValid;
      } else {
        toast({
          description: 'Failed to validate PO numbers.',
          variant: 'destructive',
        });
        return false;
      }
    } catch {
      toast({
        description: 'An error occurred during validation.',
        variant: 'destructive',
      });
      return false;
    } finally {
      setIsValidatingPOs(false);
    }
  };

  const loadAvailableSlots = async (validatedPONumbers: string[]) => {
    setIsLoadingSlots(true);
    try {
      const res = await getOpenApptSlots({
        loadTypeID: 'placeholder-value',
        warehouseID: selectedWarehouse?.warehouseID ?? '',
        warehouseTimezoneStartDate: dayjs(),
        warehouseTimezoneEndDate: dayjs(),
        freightTrackingID: '',
        source: SchedulingPortals.Retalix,
        warehouse: {
          warehouseID: selectedWarehouse?.warehouseID ?? '',
          warehouseName: selectedWarehouse?.warehouseName ?? '',
        },
        poNumbers: validatedPONumbers,
      });
      if (res.isOk()) {
        setOrderedSlots(res.value);
        setLiveAppointmentsAvailable(true);
        if (!res.value?.slots || Object.keys(res.value.slots).length === 0) {
          toast({
            description: 'No available appointment times found.',
            variant: 'default',
          });
          return false;
        }
        return true;
      } else {
        if (
          res.error.message ===
          'Live appointments are not available at this time'
        ) {
          setLiveAppointmentsAvailable(false);
          toast({
            description:
              'Option 2 (Live Appointments) is not available for this warehouse. Please use Option 1 to submit an appointment request.',
            variant: 'default',
          });
        } else {
          toast({
            description:
              res.error.message || 'Failed to load available appointments.',
            variant: 'destructive',
          });
        }
        return false;
      }
    } catch {
      toast({
        description: 'Failed to load available appointments.',
        variant: 'destructive',
      });
      return false;
    } finally {
      setIsLoadingSlots(false);
    }
  };

  const handleValidateAppt = async () => {
    const poNumbers = purchaseOrders.map((po) => po.number);
    const isValid = await validatePONumbers(poNumbers);
    if (!isValid) return;
    await loadAvailableSlots(poNumbers);
  };

  const handleConfirmAppointment = async () => {
    if (!selectedSlot || !orderedSlots || !selectedWarehouse) {
      toast({
        description: 'Please select an available time slot',
        variant: 'default',
      });
      return;
    }
    const validPoNumbers = purchaseOrders
      .filter((po) => po.isValid && !po.error)
      .map((po) => po.number);
    if (!validPoNumbers.length) {
      toast({
        description: 'No valid PO numbers found',
        variant: 'destructive',
      });
      return;
    }
    setIsLoadingConfirm(true);
    const res = await confirmSlotAppt({
      // TODO: update Relay with confirmation number
      source: SchedulingPortals.Retalix,
      isTMSLoad: false,
      stopType: type,
      start: selectedSlot.startTime,
      loadTypeId: orderedSlots.loadType.id || 'placeholder',
      warehouseID: orderedSlots.warehouse.warehouseID,
      warehouseTimezone: selectedWarehouse?.warehouseTimezone || 'UTC',
      dockId: selectedSlot.dock.id || 'placeholder',
      loadID: load.ID!,
      freightTrackingId: load.freightTrackingID || 'placeholder', // PRO Number
      integrationId: integrationId,
      trailerType: orderedSlots.trailerType,
      subscribedEmail: '',
      notes: 'Booking appointment',
      poNums: validPoNumbers.join(','),
      requestType: type,
    });
    if (res.isOk()) {
      setApptConfirmationNumber(res.value.ConfirmationNo);
    } else {
      if (res.error.message === 'Conflicting Appointments') {
        toast({
          title: 'Conflicting Appointments',
          description:
            "Make sure you don't have an existing appointment for this load.",
          variant: 'destructive',
        });
      } else {
        toast({
          description: res.error.message,
          variant: 'destructive',
        });
      }
    }
    setIsLoadingConfirm(false);
  };

  const handleSubmitAppt = async () => {
    setIsLoadingSubmit(true);
    const formValues = getValues();
    const request = {
      warehouseId: selectedWarehouse?.warehouseID ?? '',
      source: selectedWarehouse?.warehouseSource ?? SchedulingPortals.Retalix,
      poNumbers: purchaseOrders.map((po) => po.number),
      lumperRequested: formValues.shouldRequestLumper,
      note: formValues.apptNote,
      requestedDate: formValues.requestedDate
        ? dayjs(formValues.requestedDate).format()
        : undefined,
      timePreference: formValues.timePreference || undefined,
      requestType: type,
    };
    const submitRes = await submitAppt(request);
    if (submitRes.isOk()) {
      toast({
        description: 'Your appointment has been submitted.',
        variant: 'success',
      });
    } else {
      toast({
        description: 'An error occurred while submitting your appointment.',
        variant: 'destructive',
      });
    }
    setIsLoadingSubmit(false);
  };

  const onInvalid: SubmitErrorHandler<any> = async () => {
    toast({
      description: 'Some fields are invalid.',
      variant: 'destructive',
    });
  };

  const canSubmit =
    purchaseOrders.length > 0 &&
    purchaseOrders.every((po) => po.isValid && po.error === '');

  useEffect(() => {
    if (canSubmit) {
      scrollResultsIntoViewRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [canSubmit]);

  return (
    <ExtendedFormProvider aiDefaultValues={true}>
      <FormProvider {...formMethods}>
        <div className='col-span-6'>
          <form className='w-full flex flex-col mb-2'>
            <label className='text-neutral-600'>Warehouse</label>
            <DebounceSelect
              showSearch
              placeholder='Choose'
              optionFilterProp='children'
              value={
                selectedWarehouse
                  ? mapWarehousesToOptions([selectedWarehouse])
                  : null
              }
              fetchOptions={handleWarehouseSearch}
              onFocus={handleResetWarehouseSearch}
              onSelect={({ value }) => {
                if (
                  !hasOverriddenSuggestedWarehouse &&
                  value !== selectedWarehouse?.warehouseName
                ) {
                  setHasOverriddenSuggestedWarehouse(true);
                }
                const foundWarehouse = warehouses.find(
                  (w) => w.warehouseID === value
                );
                setSelectedWarehouse(foundWarehouse || null);
              }}
              options={[
                {
                  label: (
                    <span>{`${isInitialSearch ? 'Recently used' : 'Searched'} warehouses`}</span>
                  ),
                  title: `${isInitialSearch ? 'Recently used' : 'Searched'} warehouses`,
                  options: mapWarehousesToOptions(warehouses),
                },
              ]}
              optionRender={(option) => (
                <GenericLocationOption
                  option={option.data}
                  optionFieldsToRender={[
                    'mainAddress',
                    'secondaryAddress',
                    ...(uniqueWarehouseSources ? ['source'] : []),
                  ]}
                />
              )}
              notFoundContent={
                isInitialSearch ? (
                  <Typography>
                    Start typing to search for a warehouse
                  </Typography>
                ) : (
                  <Typography>No results found</Typography>
                )
              }
            />
          </form>

          {selectedWarehouse && (
            <>
              <WarehouseDisplay warehouse={selectedWarehouse} />

              <div className='mt-2'>
                <form
                  onSubmit={handleSubmit(handleSubmitAppt, onInvalid)}
                  className='flex flex-col gap-4 mt-4 mx-0 w-full'
                >
                  <div>
                    <Label name='poNumbers'>Customer PO #</Label>
                    <RetalixPOList
                      purchaseOrders={purchaseOrders}
                      setPurchaseOrders={setPurchaseOrders}
                    />
                  </div>

                  <Button
                    buttonNamePosthog={
                      ButtonNamePosthog.ValidateRetalixPONumbers
                    }
                    className='w-full'
                    type='button'
                    disabled={!purchaseOrders.length || isValidatingPOs}
                    onClick={handleValidateAppt}
                  >
                    {isValidatingPOs ? (
                      <ButtonLoader />
                    ) : (
                      ButtonText.ValidatePONumbers
                    )}
                  </Button>

                  {canSubmit && (
                    <Flex
                      direction='col'
                      gap='2xl'
                      className='mt-4'
                      ref={scrollResultsIntoViewRef}
                    >
                      {/* Submit appointment request section - always visible */}
                      <div className='rounded-lg border border-neutral-200 p-4 bg-neutral-50 shadow-sm'>
                        <div className='mb-6'>
                          <span className='text-sm font-medium text-neutral-500 block mb-2'>
                            Option 1
                          </span>
                          <Typography
                            variant='h3'
                            className='text-neutral-900 mb-2'
                          >
                            Submit Appointment Request
                          </Typography>
                          <Typography
                            variant='body-sm'
                            className='text-neutral-500 mt-1'
                          >
                            Submit a request for review. You will be notified
                            once approved.
                          </Typography>
                        </div>

                        <Controller
                          name='shouldRequestLumper'
                          control={control}
                          render={({ field }) => (
                            <Flex align='center' gap='sm'>
                              <Checkbox
                                onCheckedChange={field.onChange}
                                checked={field.value}
                              />
                              <label
                                htmlFor='shouldRequestLumper'
                                className='leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
                              >
                                Request Lumper?
                              </label>
                            </Flex>
                          )}
                        />

                        <div className='space-y-6 py-4'>
                          <Flex gap='sm'>
                            <Label name='requestedDate'>
                              Requested Delivery Date
                            </Label>
                            <Controller
                              name='requestedDate'
                              control={control}
                              render={({ field }) => (
                                <DatePicker field={field} />
                              )}
                            />
                          </Flex>

                          <Flex gap='sm'>
                            <Label name='timePreference'>Preferred Time</Label>
                            <Controller
                              name='timePreference'
                              control={control}
                              render={({ field }) => (
                                <Select
                                  onValueChange={field.onChange}
                                  value={field.value}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder='Select preferred time' />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {timePreferences.map(({ value, label }) => (
                                      <SelectItem key={value} value={value}>
                                        {label}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              )}
                            />
                          </Flex>
                        </div>

                        <Flex direction='col' gap='sm' className='mt-4'>
                          <Label name='apptNote'>Appointment Note</Label>
                          <Controller
                            control={control}
                            name='apptNote'
                            render={({ field: { onChange, value } }) => (
                              <Textarea
                                className='p-2 h-16 whitespace-pre-wrap focus-visible:ring-transparent'
                                onChange={onChange}
                                value={value}
                              />
                            )}
                          />
                        </Flex>

                        <Button
                          buttonNamePosthog={
                            ButtonNamePosthog.SubmitRetalixAppt
                          }
                          type='submit'
                          className='mt-4 w-full'
                          disabled={!canSubmit || isLoadingSubmit}
                        >
                          {isLoadingSubmit ? <ButtonLoader /> : 'Submit'}
                        </Button>
                      </div>

                      {/* Live Appointment Section */}
                      <div
                        className={cn(
                          'rounded-lg border border-neutral-200 p-4 bg-neutral-50 shadow-sm',
                          !liveAppointmentsAvailable &&
                            'opacity-50 pointer-events-none'
                        )}
                      >
                        <div className='mb-6'>
                          <span className='text-sm font-medium text-neutral-500 block mb-2'>
                            Option 2
                          </span>
                          <Typography
                            variant='h3'
                            className='text-neutral-900 mb-2'
                          >
                            Book Live Appointment
                          </Typography>
                          <Typography
                            variant='body-sm'
                            className='text-neutral-500'
                          >
                            {liveAppointmentsAvailable
                              ? 'Book an appointment immediately from available time slots.'
                              : 'Live appointments are not available for this warehouse. Please use Option 1 to submit an appointment request.'}
                          </Typography>
                        </div>

                        {isValidatingPOs ? (
                          <div className='text-center'>
                            <Flex align='center' gap='sm' className='mt-4'>
                              <ButtonLoader />
                              <Typography>Validating PO numbers...</Typography>
                            </Flex>
                          </div>
                        ) : isLoadingSlots ? (
                          <div className='text-center'>
                            <Flex align='center' gap='sm' className='mt-4'>
                              <ButtonLoader />
                              <Typography>Loading open slots...</Typography>
                            </Flex>
                          </div>
                        ) : orderedSlots ? (
                          <div>
                            {Object.entries(orderedSlots.slots).map(
                              ([date, slots]) => (
                                <div key={date}>
                                  <Typography
                                    variant='h6'
                                    weight='bold'
                                    className='text-neutral-400 uppercase mt-4'
                                  >
                                    {date}
                                  </Typography>
                                  <Grid
                                    cols='3'
                                    gap='xs'
                                    className='mt-2 mx-0 w-full'
                                  >
                                    {slots.map((slot, idx) => (
                                      <TimeSlotButton
                                        key={idx}
                                        slot={slot}
                                        isSelected={selectedSlot === slot}
                                        onSelect={(clickedSlot) =>
                                          setSelectedSlot(
                                            selectedSlot === clickedSlot
                                              ? null
                                              : clickedSlot
                                          )
                                        }
                                      />
                                    ))}
                                  </Grid>
                                </div>
                              )
                            )}

                            {selectedSlot && (
                              <div className='mt-4 text-neutral-400 text-left text-sm'>
                                <Typography weight='bold' className='my-1'>
                                  Selected Slot:
                                </Typography>
                                <Typography className='mb-2'>
                                  {dayjs
                                    .utc(selectedSlot.startTime)
                                    .format('MMM D, YYYY, HH:mm')}
                                </Typography>
                                {apptConfirmationNumber ? (
                                  <div className='whitespace-pre-wrap my-3 rounded py-3 text-neutral-900 px-4 bg-success-50'>
                                    <Typography className='mb-2'>
                                      Appointment confirmed 🎉
                                    </Typography>
                                    <Typography
                                      variant='body-sm'
                                      className='mb-4'
                                    >
                                      <Typography
                                        variant='body-sm'
                                        weight='bold'
                                      >
                                        Retalix Confirmation #:{' '}
                                      </Typography>
                                      {apptConfirmationNumber}
                                    </Typography>
                                  </div>
                                ) : (
                                  <Button
                                    onClick={handleConfirmAppointment}
                                    className='mt-4 w-full'
                                    disabled={isLoadingConfirm}
                                    buttonNamePosthog={
                                      ButtonNamePosthog.ConfirmSlotApptScheduling
                                    }
                                  >
                                    {isLoadingConfirm ? (
                                      <ButtonLoader />
                                    ) : (
                                      'Confirm Appointment'
                                    )}
                                  </Button>
                                )}
                              </div>
                            )}
                          </div>
                        ) : (
                          <Button
                            buttonNamePosthog={null}
                            onClick={handleValidateAppt}
                            disabled={
                              !purchaseOrders.length ||
                              !liveAppointmentsAvailable
                            }
                            className='mt-4 w-full'
                          >
                            Load available times
                          </Button>
                        )}
                      </div>
                    </Flex>
                  )}
                </form>
              </div>
            </>
          )}
        </div>
      </FormProvider>
    </ExtendedFormProvider>
  );
}
