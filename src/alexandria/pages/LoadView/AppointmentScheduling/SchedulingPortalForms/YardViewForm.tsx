import React, { useContext, useEffect, useRef, useState } from 'react';
import { Controller, FieldPath, FormProvider, useForm } from 'react-hook-form';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import { ErrorMessage } from '@hookform/error-message';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from 'components/Accordion';
import { Button } from 'components/Button';
import {
  DebounceSelect,
  GenericLocationOption,
} from 'components/DebounceSelect';
import { Label } from 'components/Label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from 'components/Select';
import { Textarea } from 'components/Textarea';
import { RHFTextInput } from 'components/input/RHFTextInput';
import { Flex, Grid } from 'components/layout';
import ButtonLoader from 'components/loading/ButtonLoader';
import { Typography } from 'components/typography';
import { ExtendedFormProvider } from 'contexts/extendedFormContext';
import { SidebarStateContext } from 'contexts/sidebarStateContext';
import { useAuth } from 'hooks/useAuth';
import { useToast } from 'hooks/useToaster';
import { confirmSlotAppt } from 'lib/api/confirmSlotAppt';
import {
  WarehouseCarrierScacs,
  getWarehouseCarrierScacs,
} from 'lib/api/getWarehouseCarrierScacs';
import {
  WarehouseLoadTypes,
  getWarehouseLoadTypes,
} from 'lib/api/getWarehouseLoadTypes';
import { getWarehouseSearch } from 'lib/api/getWarehouseSearch';
import { getWarehousesSearch } from 'lib/api/getWarehousesSearch';
import { getOpenApptSlots } from 'lib/api/openApptSlots';
import {
  GroupedSlot,
  OrderedSlots,
  SchedulingPortals,
  StopTypes,
} from 'types/Appointment';
import { NormalizedLoad } from 'types/Load';
import { Maybe, MaybeUndef, Undef } from 'types/UtilityTypes';
import { Warehouse } from 'types/Warehouse';
import ButtonText from 'types/enums/ButtonText';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';
import ScheduleType from 'types/enums/ScheduleType';

import TimeSlotButton from '../TimeSlotButton';
import WarehouseDisplay from '../WarehouseDisplay';
import { YardViewAppointmentConfirmation } from '../Yardview/YardViewAppointmentConfirmation';

dayjs.extend(utc);
dayjs.extend(timezone);

interface YardViewInputsWithoutLoad {
  appt: {
    addressLine1: MaybeUndef<string>;
    expectedPickupTime: MaybeUndef<string>;
    expectedDeliveryTime: MaybeUndef<string>;
  };
  externalTMSID: string; // TMS's UUID for the shipment
  refNumber: string; // Target PRO Number
  dockId: string;
  startDateTime: Date;
  endDateTime: Date;
  warehouse: any;
  subscribedEmail: string; // TODO: support multiple emails
  apptKey: string;
  trailerId: string;
  carrierScac: string;
  schedulePid: string;
  loadType: string;
  weight: string;
  comments: string;
  loadConfiguration: 'palletized' | 'floorLoaded';
}

type YardViewTextInputProps = React.ComponentPropsWithoutRef<
  typeof RHFTextInput
> & {
  name: FieldPath<YardViewInputsWithoutLoad>;
};
const YardViewTextInput = (props: YardViewTextInputProps) => (
  <RHFTextInput {...props} />
);

type YardViewFormProps = {
  type: StopTypes;
  load: NormalizedLoad;
  integrationId?: number;
  recentWarehouses: Warehouse[];
  selectedWarehouse: Maybe<Warehouse>;
  setSelectedWarehouse: (warehouse: Maybe<Warehouse>) => void;
};

export function YardViewForm({
  type,
  load,
  integrationId,
  recentWarehouses,
  selectedWarehouse,
  setSelectedWarehouse,
}: YardViewFormProps) {
  const { toast } = useToast();
  const {
    currentState: { inboxEmailAddress },
  } = useContext(SidebarStateContext);
  const userEmail = useAuth().user?.email;

  const [loading, setLoading] = useState(false);

  const [warehouses, setWarehouses] = useState<Warehouse[]>(
    recentWarehouses && recentWarehouses.length ? recentWarehouses : []
  );
  const [docks, setDocks] = useState<Warehouse[]>([]);
  const [selectedDock, setSelectedDock] = useState<Maybe<Warehouse>>(null);
  const [uniqueWarehouseSources, setUniqueWarehouseSources] = useState<
    string[]
  >([]);
  const [allWarehouseCarrierScacs, setAllWarehouseCarrierScacs] = useState<
    WarehouseCarrierScacs[]
  >([]);
  const [allWarehouseLoadTypes, setAllWarehouseLoadTypes] = useState<
    WarehouseLoadTypes[]
  >([]);
  const [isInitialWarehouseSearch, setIsInitialWarehouseSearch] =
    useState(true);
  const [isInitialDockSearch, setIsInitialDockSearch] = useState(true);
  const [hasOverriddenSuggestedWarehouse, setHasOverriddenSuggestedWarehouse] =
    useState(false);
  const [hasOverriddenSuggestedDock, setHasOverriddenSuggestedDock] =
    useState(false);

  const [orderedSlots, setOrderedSlots] =
    useState<MaybeUndef<OrderedSlots>>(null);
  const [loadingNextWeek, setLoadingNextWeek] = useState(false);
  // To control scroll position when slots are rendered
  const slotsContainerRef = useRef<Maybe<HTMLDivElement>>(null);
  const endOfSlotsRef = useRef<Maybe<HTMLDivElement>>(null);
  const [scrollTarget, setScrollTarget] =
    useState<Maybe<'start' | 'end'>>(null);
  const [selectedSlot, setSelectedSlot] = useState<Maybe<GroupedSlot>>(null);
  const [apptConfirmationNumber, setApptConfirmationNumber] = useState('');
  const [apptConfirmationUrl, setApptConfirmationUrl] = useState('');
  const [tmsUpdateSucceeded, setTMSUpdateSucceeded] = useState(true);
  const [defaultLoadType, setDefaultLoadType] =
    useState<Undef<string>>(undefined);

  useEffect(() => {
    const fetchAllDocks = async () => {
      if (!selectedWarehouse?.warehouseID) {
        setDocks([]);
        return;
      }

      const searchRes = await getWarehousesSearch(
        selectedWarehouse.warehouseID,
        SchedulingPortals.YardView
      );
      if (searchRes.isOk()) {
        const { warehouses: fetchedWarehouses } = searchRes.value ?? {
          warehouses: [],
        };
        // Prevent filtering on null/undefined result from API
        const safeFetchedWarehouses = Array.isArray(fetchedWarehouses)
          ? fetchedWarehouses
          : [];

        const yardviewWarehouses = safeFetchedWarehouses.filter(
          (w) => w && w.warehouseSource === SchedulingPortals.YardView
        );
        setDocks(yardviewWarehouses);
      } else {
        toast({
          description: 'Failed to fetch docks',
          variant: 'destructive',
        });
      }
    };

    const fetchAllWarehouseCarrierScacs = async () => {
      if (!selectedWarehouse?.warehouseID) {
        setAllWarehouseCarrierScacs([]);
        return;
      }

      const res = await getWarehouseCarrierScacs(
        selectedWarehouse.warehouseID,
        SchedulingPortals.YardView
      );
      if (res.isOk()) {
        setAllWarehouseCarrierScacs(res.value.scacs);
      } else {
        toast({
          description: 'Failed to fetch warehouse carrier scacs',
          variant: 'destructive',
        });
      }
    };

    const fetchAllWarehouseLoadTypes = async () => {
      if (!selectedWarehouse?.warehouseID) {
        setAllWarehouseLoadTypes([]);
        setDefaultLoadType(undefined);
        return;
      }

      const res = await getWarehouseLoadTypes(
        selectedWarehouse.warehouseID,
        SchedulingPortals.YardView
      );
      if (res.isOk()) {
        const loadTypes = res.value.loadTypes;
        setAllWarehouseLoadTypes(loadTypes);
        const liveOption = loadTypes.find(
          (lt) => lt.name === 'Live' || lt.name === 'Live Load'
        );
        if (liveOption) {
          setDefaultLoadType(liveOption.id);
        } else {
          setDefaultLoadType(undefined);
        }
      } else {
        toast({
          description: 'Failed to fetch warehouse load types',
          variant: 'destructive',
        });
        setDefaultLoadType(undefined);
      }
    };

    if (selectedWarehouse?.warehouseID) {
      fetchAllDocks();
      fetchAllWarehouseCarrierScacs();
      fetchAllWarehouseLoadTypes();
    } else {
      setDocks([]);
      setAllWarehouseCarrierScacs([]);
      setAllWarehouseLoadTypes([]);
      setDefaultLoadType(undefined);
    }
  }, [selectedWarehouse?.warehouseID]);

  useEffect(() => {
    if (warehouses) {
      setUniqueWarehouseSources([SchedulingPortals.YardView]);
    }
  }, [warehouses]);

  const handleResetWarehouseSearch = () => {
    setIsInitialWarehouseSearch(true);
    setWarehouses(recentWarehouses);
  };

  const handleWarehouseSearch = async (search: string) => {
    if (search.length > 3) {
      setIsInitialWarehouseSearch(false);
      const res = await getWarehouseSearch(search);
      if (res.isOk()) {
        const { warehouses: searchedWarehouses } = res.value ?? {
          warehouses: [],
        };
        // Prevent filtering on null/undefined result from API
        const safeSearchedWarehouses = Array.isArray(searchedWarehouses)
          ? searchedWarehouses
          : [];

        const yardviewWarehouses = safeSearchedWarehouses.filter(
          (w) => w && w.warehouseSource === SchedulingPortals.YardView
        );
        setWarehouses(yardviewWarehouses);
        return yardviewWarehouses && yardviewWarehouses.length
          ? mapWarehousesToOptions(yardviewWarehouses)
          : [];
      }
    }
    handleResetWarehouseSearch();
    return mapWarehousesToOptions(recentWarehouses).filter((wh) =>
      wh.label.toLocaleLowerCase().includes(search.toLocaleLowerCase())
    );
  };

  const handleResetDockSearch = () => {
    setIsInitialDockSearch(true);
  };

  const handleDockSearch = async (search: string) => {
    setIsInitialDockSearch(search === '');
    const filtered = docks.filter((wh) => {
      return wh.warehouseName.toLowerCase().includes(search.toLowerCase());
    });
    return mapWarehousesToOptions(filtered);
  };

  const mapWarehousesToOptions = (warehouses: Warehouse[]) =>
    warehouses?.map((option: Warehouse) => ({
      ...option,
      value: option.warehouseID,
      name: option.warehouseName,
      mainAddress: option.warehouseAddressLine1,
      secondaryAddress: option.warehouseAddressLine2,
      source: option.warehouseSource,
      label: option.warehouseName,
    }));

  // Some services want the TMS load ID included in the notes field. This function handles that logic.
  const aiSuggestNotes = (
    load: NormalizedLoad,
    loadConfiguration: 'palletized' | 'floorLoaded' = 'palletized'
  ): string => {
    let baseNotes: string;
    // For Fetch Freight and others, always include the load ID in the notes field
    if (!inboxEmailAddress?.includes('nfiindustries.com')) {
      baseNotes = `Customer: ${load.customer.name}\nLoad ID: ${load.freightTrackingID}`;
    } else {
      baseNotes = `Pickup shipper - ${load.pickup.name}\nLoad ID: ${load.freightTrackingID}`;
    }

    const formattedLoadConfig =
      loadConfiguration === 'palletized' ? 'Palletized' : 'Floor Loaded';

    return `${baseNotes}\nLoad Configuration: ${formattedLoadConfig}`;
  };

  const startDateTime = new Date();
  startDateTime.setHours(0, 0, 0, 0);

  const endDateTime = new Date();
  endDateTime.setHours(0, 0, 0, 0);
  endDateTime.setDate(endDateTime.getDate() + 7);

  const formMethods = useForm<YardViewInputsWithoutLoad>({
    mode: 'onChange',
    defaultValues: {
      subscribedEmail: inboxEmailAddress,
      externalTMSID: load.externalTMSID,
      refNumber: load.customer.refNumber,
      comments: aiSuggestNotes(load, 'palletized'),
      weight: '0',
      carrierScac: '',
      loadType: undefined,
      apptKey: '',
      trailerId: '',
      schedulePid: ScheduleType.Inbound,
      startDateTime: startDateTime,
      endDateTime: endDateTime,
      dockId: '',
      loadConfiguration: 'palletized',
    },
  });

  const {
    control,
    formState: { errors },
    getValues,
    resetField,
    watch,
    setValue,
  } = formMethods;

  const loadConfiguration = watch('loadConfiguration');
  const currentLoadType = watch('loadType');

  useEffect(() => {
    resetField('subscribedEmail', {
      defaultValue: selectedWarehouse?.defaultSubscribedEmail ?? userEmail,
    });

    setValue('comments', aiSuggestNotes(load, loadConfiguration), {
      shouldValidate: false,
      shouldDirty: true,
    });
  }, [selectedWarehouse, loadConfiguration, load]);

  useEffect(() => {
    if (defaultLoadType !== undefined && currentLoadType === undefined) {
      setValue('loadType', defaultLoadType, {
        shouldValidate: false,
        shouldDirty: true,
      });
    }
    setOrderedSlots(null);
  }, [defaultLoadType, selectedWarehouse]);

  // Scroll slots into view when they're rendered
  useEffect(() => {
    if (!orderedSlots || Object.keys(orderedSlots.slots).length === 0) {
      return;
    }

    if (scrollTarget === 'end') {
      const el = endOfSlotsRef.current;
      if (el) {
        el.scrollIntoView({ behavior: 'smooth', block: 'end' });
        // Nudge slightly downward for better context
        window.scrollBy({
          top: 40,
          left: 0,
          behavior: 'instant' as ScrollBehavior,
        });
      }
    } else if (scrollTarget === 'start') {
      slotsContainerRef.current?.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }

    // Reset target after scrolling
    if (scrollTarget) {
      setScrollTarget(null);
    }
  }, [orderedSlots, scrollTarget]);

  const handleFetchSlots = async () => {
    const values = getValues();

    if (!selectedWarehouse) {
      toast({
        description: 'Please select a warehouse.',
        variant: 'default',
      });
      return;
    }

    if (!values.schedulePid) {
      toast({
        description: 'Please select a schedule.',
        variant: 'default',
      });
      return;
    }

    if (!values.refNumber) {
      toast({
        description: 'Please add a Target PRO #.',
        variant: 'default',
      });
      return;
    }

    setSelectedSlot(null);
    setLoading(true);

    const res = await getOpenApptSlots({
      source: SchedulingPortals.YardView,
      loadTypeID: 'placeholder',
      warehouseID: selectedWarehouse?.warehouseID ?? '',
      filterType: values.schedulePid,
      freightTrackingID: values.refNumber,
      startDateTime: dayjs(values.startDateTime),
      endDateTime: dayjs(values.endDateTime),
    });

    if (res.isOk()) {
      setOrderedSlots(res.value);
      setScrollTarget('start');

      const slots = res.value?.slots;
      if (!slots || Object.keys(slots).length === 0) {
        toast({
          description: 'No slots were found for the selected date range.',
          variant: 'default',
        });
      }
    } else {
      toast({
        description: res.error.message,
        variant: 'destructive',
      });
    }

    setLoading(false);
  };

  const handleFetchNextWeekSlots = async () => {
    if (!orderedSlots || !selectedWarehouse) {
      return;
    }

    const values = getValues();

    if (!values.schedulePid || !values.refNumber) {
      toast({
        description: 'Please select a schedule and add a Target PRO # first.',
        variant: 'default',
      });
      return;
    }

    const allDaySlots: GroupedSlot[] = Object.values(orderedSlots.slots).flat();
    if (allDaySlots.length === 0) {
      return;
    }

    const latestSlot = allDaySlots.reduce(
      (latest: GroupedSlot, curr: GroupedSlot) =>
        dayjs(curr.startTime).isAfter(dayjs(latest.startTime)) ? curr : latest
    );

    const timezoneId =
      latestSlot.timezone || selectedWarehouse.warehouseTimezone;
    const baseLatest = timezoneId
      ? dayjs(latestSlot.startTime).tz(timezoneId)
      : dayjs(latestSlot.startTime);

    // Add 3 days to reach Tuesday of next week from Saturday (or generally +3 days)
    const nextWeekDate = baseLatest.add(3, 'day').startOf('day');

    setLoadingNextWeek(true);
    const res = await getOpenApptSlots({
      source: SchedulingPortals.YardView,
      loadTypeID: 'placeholder',
      warehouseID: selectedWarehouse.warehouseID,
      filterType: values.schedulePid,
      freightTrackingID: values.refNumber,
      startDateTime: nextWeekDate,
      endDateTime: nextWeekDate,
    });

    if (res.isOk()) {
      const newOrdered = res.value;
      const merged: Record<string, GroupedSlot[]> = { ...orderedSlots.slots };

      Object.entries(newOrdered.slots).forEach(([dateKey, newSlots]) => {
        if (!merged[dateKey]) {
          merged[dateKey] = [];
        }
        const existingIso = new Set(
          merged[dateKey].map((s) => s.startTime.toISOString())
        );
        let pushed = false;
        newSlots.forEach((s) => {
          const iso = s.startTime.toISOString();
          if (!existingIso.has(iso)) {
            merged[dateKey].push(s);
            pushed = true;
          }
        });
        if (pushed) {
          merged[dateKey].sort((a, b) =>
            dayjs(a.startTime).diff(dayjs(b.startTime))
          );
        }
      });

      setOrderedSlots({
        ...orderedSlots,
        slots: merged,
      });
      setScrollTarget('end');

      const addedDays = Object.keys(newOrdered.slots).length;
      if (addedDays === 0) {
        toast({
          description: 'No additional slots found for next week.',
          variant: 'default',
        });
      }
    } else {
      toast({
        description: res.error.message,
        variant: 'destructive',
      });
    }

    setLoadingNextWeek(false);
  };

  const handleConfirmAppointment = async () => {
    const values = getValues();

    if (!selectedSlot || !orderedSlots || !selectedWarehouse) {
      toast({
        description: 'Please select an available time slot',
        variant: 'default',
      });
      return;
    }

    if (!values.loadType) {
      toast({
        description: 'Please select a load type',
        variant: 'default',
      });
      return;
    }

    setApptConfirmationNumber('');
    setApptConfirmationUrl('');
    setLoading(true);

    const res = await confirmSlotAppt({
      source: SchedulingPortals.YardView,
      isTMSLoad: true,
      stopType: type,
      start: selectedSlot.startTime,
      apptKey: values.apptKey,
      carrierScac: values.carrierScac,
      loadTypeId: values.loadType,
      warehouseID: selectedWarehouse?.warehouseID,
      warehouseTimezone: selectedWarehouse?.warehouseTimezone,
      dockId: selectedDock?.warehouseID ?? '',
      loadID: load.ID!,
      freightTrackingId: load.freightTrackingID,
      integrationId: integrationId,
      poNums: values.refNumber,
      requestType: type,
      refNumber: '',
      schedulePid: values.schedulePid,
      trailerId: values.trailerId,
      weight: values.weight,
      subscribedEmail: values.subscribedEmail,
      notes: values.comments,
    });

    if (res.isOk()) {
      toast({
        description: 'Your appointment has been booked.',
        variant: 'success',
      });
      setApptConfirmationNumber(res.value.ConfirmationNo);
      setApptConfirmationUrl(res.value.url ?? '');
      setTMSUpdateSucceeded(res.value.tmsUpdateSucceeded);
    } else {
      if (res.error.message === 'Conflicting Appointments') {
        toast({
          title: 'Conflicting Appointments',
          description:
            "Make sure you don't have an existing appointment for this load.",
          variant: 'destructive',
        });

        return;
      }

      toast({
        description: 'Unable to book appointment',
        variant: 'destructive',
      });
    }

    setLoading(false);
  };

  useEffect(() => {
    setHasOverriddenSuggestedWarehouse(false);
  }, [type]);

  return (
    <ExtendedFormProvider aiDefaultValues={true}>
      <FormProvider {...formMethods}>
        <div className='col-span-6'>
          <form className='w-full flex flex-col mb-2'>
            <Label name='warehouse' required>
              Warehouse
            </Label>
            <DebounceSelect
              showSearch
              placeholder='Choose'
              optionFilterProp='children'
              value={
                selectedWarehouse
                  ? mapWarehousesToOptions([selectedWarehouse])
                  : null
              }
              fetchOptions={handleWarehouseSearch}
              onFocus={handleResetWarehouseSearch}
              onSelect={({ value }) => {
                if (
                  !hasOverriddenSuggestedWarehouse &&
                  value !== selectedWarehouse?.warehouseName
                ) {
                  setHasOverriddenSuggestedWarehouse(true);
                }
                const foundWarehouse = warehouses.find(
                  (w) => w.warehouseID === value
                );
                setSelectedWarehouse(foundWarehouse || null);
              }}
              options={[
                {
                  label: (
                    <span>{`${isInitialWarehouseSearch ? 'Recently used' : 'Searched'} warehouses`}</span>
                  ),
                  title: `${isInitialWarehouseSearch ? 'Recently used' : 'Searched'} warehouses`,
                  options: mapWarehousesToOptions(warehouses),
                },
              ]}
              optionRender={(option) => (
                <GenericLocationOption
                  option={option.data}
                  optionFieldsToRender={[
                    'mainAddress',
                    'secondaryAddress',
                    ...(uniqueWarehouseSources ? ['source'] : []),
                  ]}
                />
              )}
              notFoundContent={
                isInitialWarehouseSearch ? (
                  <p>Start typing to search for a warehouse</p>
                ) : (
                  <p>No results found</p>
                )
              }
            />
          </form>

          {selectedWarehouse && (
            <>
              <WarehouseDisplay warehouse={selectedWarehouse} />

              <div className='mt-2'>
                <div className='grid gap-4 grid-cols-1 mt-4 mx-0 w-full'>
                  <div className='w-full'>
                    {selectedWarehouse && (
                      <div className='flex flex-col gap-4'>
                        <div className='flex flex-col'>
                          <Label name='schedule' required>
                            Schedule
                          </Label>
                          <Controller
                            name='schedulePid'
                            control={control}
                            rules={{ required: 'Required' }}
                            render={({ field }) => (
                              <Select
                                onValueChange={field.onChange}
                                value={field.value}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder='Select schedule' />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem
                                    key={'1'}
                                    value={ScheduleType.Inbound}
                                  >
                                    Inbound
                                  </SelectItem>
                                  <SelectItem
                                    key={'2'}
                                    value={ScheduleType.TargetAirFreightOnly}
                                  >
                                    Target Air Freight Only
                                  </SelectItem>
                                  <SelectItem
                                    key={'16'}
                                    value={ScheduleType.TaxAirFreight}
                                  >
                                    Tax Air Freight
                                  </SelectItem>
                                  <SelectItem
                                    key={'18'}
                                    value={ScheduleType.Elwood}
                                  >
                                    Elwood
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                            )}
                          />
                          <ErrorMessage
                            errors={errors}
                            name='schedulePid'
                            render={({ message }: { message: string }) => (
                              <p className='text-error-500 text-xs'>
                                {message}
                              </p>
                            )}
                          />
                        </div>

                        <div className='w-full'>
                          <YardViewTextInput
                            label='Target PRO #'
                            name='refNumber'
                            required
                          />
                        </div>
                      </div>
                    )}

                    <Button
                      buttonNamePosthog={ButtonNamePosthog.FindOpenApptSlots}
                      type='submit'
                      className='w-full mt-4'
                      onClick={handleFetchSlots}
                      disabled={loading}
                      logProperties={{
                        freightTrackingID: load.freightTrackingID,
                        serviceID: load.serviceID,
                      }}
                    >
                      {loading ? <ButtonLoader /> : ButtonText.GetOpenApptSlots}
                    </Button>
                  </div>
                </div>
              </div>

              <section ref={slotsContainerRef}>
                {loading && null}

                {orderedSlots !== undefined &&
                  orderedSlots !== null &&
                  Object.keys(orderedSlots.slots).length > 0 && (
                    <>
                      <div className='flex flex-col gap-2 mt-2'>
                        <hr className='my-2' />
                        <Typography variant='body' weight='semibold'>
                          Load Details
                        </Typography>

                        <Flex direction='col' gap='xs'>
                          <Label name='loadType' required>
                            Load Type
                          </Label>
                          <Controller
                            name='loadType'
                            control={control}
                            rules={{ required: 'Required' }}
                            render={({ field }) => (
                              <Select
                                onValueChange={field.onChange}
                                value={field.value}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder='Select load type' />
                                </SelectTrigger>
                                <SelectContent>
                                  {allWarehouseLoadTypes.map((option) => (
                                    <SelectItem
                                      key={option.id}
                                      value={option.id}
                                    >
                                      {option.name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            )}
                          />
                          <ErrorMessage
                            errors={errors}
                            name='loadType'
                            render={({ message }: { message: string }) => (
                              <p className='text-error-500 text-xs'>
                                {message}
                              </p>
                            )}
                          />
                        </Flex>

                        <Flex direction='col' gap='xs'>
                          <Label name='loadConfiguration'>
                            Load Configuration
                          </Label>
                          <Controller
                            name='loadConfiguration'
                            control={control}
                            render={({ field }) => (
                              <Select
                                onValueChange={field.onChange}
                                value={field.value}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder='Select configuration' />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value='palletized'>
                                    Palletized
                                  </SelectItem>
                                  <SelectItem value='floorLoaded'>
                                    Floor Loaded
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                            )}
                          />
                        </Flex>

                        <Flex direction='col' gap='xs'>
                          <Label name='comments'>Comments</Label>
                          <Controller
                            control={control}
                            name='comments'
                            render={({ field: { onChange, value } }) => (
                              <Textarea
                                className='p-2 h-16 whitespace-pre-wrap focus-visible:ring-transparent'
                                onChange={onChange}
                                value={value}
                              />
                            )}
                          />
                        </Flex>

                        <Accordion type='single' collapsible defaultValue=''>
                          <AccordionItem value='additional-info'>
                            <AccordionTrigger className='text-sm py-3'>
                              Additional Info
                            </AccordionTrigger>
                            <AccordionContent>
                              <Flex direction='col' gap='sm' className='w-full'>
                                <div className='w-full'>
                                  <form>
                                    <Label name='dock'>Dock</Label>
                                    <DebounceSelect
                                      showSearch
                                      placeholder='Choose'
                                      optionFilterProp='children'
                                      value={
                                        selectedDock
                                          ? mapWarehousesToOptions([
                                              selectedDock,
                                            ])
                                          : null
                                      }
                                      fetchOptions={handleDockSearch}
                                      onFocus={handleResetDockSearch}
                                      onSelect={({ value }) => {
                                        if (
                                          !hasOverriddenSuggestedDock &&
                                          value !== selectedDock?.warehouseName
                                        ) {
                                          setHasOverriddenSuggestedDock(true);
                                        }
                                        const foundWarehouse = docks.find(
                                          (w) => w.warehouseID === value
                                        );
                                        setSelectedDock(foundWarehouse || null);
                                      }}
                                      options={[
                                        {
                                          label: (
                                            <span>{`${isInitialDockSearch ? 'All' : 'Filtered'} docks`}</span>
                                          ),
                                          title: `${isInitialDockSearch ? 'All' : 'Filtered'} docks`,
                                          options:
                                            mapWarehousesToOptions(docks),
                                        },
                                      ]}
                                      optionRender={(option) => (
                                        <GenericLocationOption
                                          option={option.data}
                                          optionFieldsToRender={[
                                            'mainAddress',
                                            'secondaryAddress',
                                            ...(uniqueWarehouseSources
                                              ? ['source']
                                              : []),
                                          ]}
                                        />
                                      )}
                                      notFoundContent={
                                        isInitialDockSearch
                                          ? 'Start typing to filter docks'
                                          : 'No matching docks found'
                                      }
                                    />
                                  </form>
                                </div>

                                <Flex
                                  direction='col'
                                  gap='xs'
                                  className='w-full'
                                >
                                  <Label name='carrierScac'>Carrier SCAC</Label>
                                  <Controller
                                    name='carrierScac'
                                    control={control}
                                    render={({ field }) => (
                                      <Select
                                        onValueChange={field.onChange}
                                        value={field.value}
                                      >
                                        <SelectTrigger>
                                          <SelectValue placeholder='Select carrier scac' />
                                        </SelectTrigger>
                                        <SelectContent>
                                          {allWarehouseCarrierScacs.map(
                                            (option) => (
                                              <SelectItem
                                                key={option.id}
                                                value={option.id}
                                              >
                                                {option.name}
                                              </SelectItem>
                                            )
                                          )}
                                        </SelectContent>
                                      </Select>
                                    )}
                                  />
                                  <ErrorMessage
                                    errors={errors}
                                    name='carrierScac'
                                    render={({
                                      message,
                                    }: {
                                      message: string;
                                    }) => (
                                      <p className='text-error-500 text-xs'>
                                        {message}
                                      </p>
                                    )}
                                  />
                                </Flex>

                                <div className='w-full'>
                                  <YardViewTextInput
                                    label='Trailer #'
                                    name='trailerId'
                                  />
                                </div>

                                <div className='w-full'>
                                  <YardViewTextInput
                                    label='Appt Key #'
                                    name='apptKey'
                                  />
                                </div>
                              </Flex>
                            </AccordionContent>
                          </AccordionItem>
                        </Accordion>
                      </div>

                      <Flex className='mt-4 items-center justify-between'>
                        <Typography variant='body' weight='semibold'>
                          Select an appointment time
                        </Typography>
                      </Flex>

                      {/* Display open slots by date for selection */}
                      {Object.keys(orderedSlots.slots).map((date) => {
                        const daySlots = orderedSlots.slots[date];

                        return (
                          <div key={date}>
                            <Typography
                              variant='body-sm'
                              weight='bold'
                              className='text-neutral-600 uppercase mt-3'
                            >
                              {date}
                            </Typography>
                            <Grid cols='3' gap='xs' className='mt-2'>
                              {daySlots.map(
                                (slot: GroupedSlot, idx: number) => (
                                  <TimeSlotButton
                                    key={idx}
                                    slot={slot}
                                    isSelected={selectedSlot === slot}
                                    onSelect={(clickedSlot) => {
                                      setSelectedSlot(
                                        selectedSlot === clickedSlot
                                          ? null
                                          : clickedSlot
                                      );
                                    }}
                                  />
                                )
                              )}
                            </Grid>
                          </div>
                        );
                      })}

                      {selectedSlot ? null : (
                        <Button
                          buttonNamePosthog={
                            ButtonNamePosthog.FindOpenApptSlots
                          }
                          className='mt-4'
                          size='sm'
                          variant='secondary'
                          onClick={handleFetchNextWeekSlots}
                          disabled={loadingNextWeek}
                        >
                          {loadingNextWeek ? (
                            <ButtonLoader />
                          ) : (
                            <Typography variant='body-sm'>
                              Get open slots (next week)
                            </Typography>
                          )}
                        </Button>
                      )}

                      {/* Anchor to help scroll slightly past the last slots */}
                      <div ref={endOfSlotsRef} />

                      {selectedSlot ? (
                        <div className='mt-4 text-neutral-600 text-left text-sm'>
                          <Typography variant='body-sm' weight='bold'>
                            Selected Slot:
                          </Typography>
                          <Typography variant='body-sm' className='mb-2'>
                            {dayjs(selectedSlot.startTime)
                              .tz(selectedSlot.timezone)
                              .format('MMM D, YYYY, HH:mm')}
                          </Typography>
                          {apptConfirmationNumber ? (
                            <YardViewAppointmentConfirmation
                              apptConfirmationNumber={apptConfirmationNumber}
                              tmsUpdateSucceeded={tmsUpdateSucceeded}
                              apptConfirmationUrl={apptConfirmationUrl}
                            />
                          ) : null}
                          <Button
                            buttonNamePosthog={
                              ButtonNamePosthog.ConfirmSlotApptScheduling
                            }
                            className='mt-2 w-full'
                            onClick={handleConfirmAppointment}
                            disabled={loading}
                          >
                            {loading ? (
                              <ButtonLoader />
                            ) : (
                              ButtonText.ConfirmSlotApptScheduling
                            )}
                          </Button>
                        </div>
                      ) : null}
                    </>
                  )}
              </section>
            </>
          )}
        </div>
      </FormProvider>
    </ExtendedFormProvider>
  );
}
