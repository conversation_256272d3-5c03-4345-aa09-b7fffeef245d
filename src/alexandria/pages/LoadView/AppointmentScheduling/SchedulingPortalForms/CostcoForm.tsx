import { useEffect, useRef, useState } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  FormProvider,
  SubmitError<PERSON>andler,
  useForm,
} from 'react-hook-form';

import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';

import { Button } from 'components/Button';
import { Label } from 'components/Label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from 'components/Select';
import DateTimeInput from 'components/input/DateTimeInput';
import { RHFTextInput } from 'components/input/RHFTextInput';
import { Flex } from 'components/layout';
import ButtonLoader from 'components/loading/ButtonLoader';
import { Typography } from 'components/typography';
import { ExtendedFormProvider } from 'contexts/extendedFormContext';
import { useToast } from 'hooks/useToaster';
import { confirmSlotAppt } from 'lib/api/confirmSlotAppt';
import { validateAppt } from 'lib/api/validateAppt';
import { SchedulingPortals, StopTypes } from 'types/Appointment';
import { NormalizedLoad } from 'types/Load';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';

import {
  DEPOT_OPTIONS,
  DOOR_TYPE_OPTIONS,
  UNLOAD_TYPE_OPTIONS,
  UOM_OPTIONS,
} from '../constants/costcoScheduling';

dayjs.extend(utc);

interface CostcoInputsWithoutLoad {
  // Step 1 - Validation fields
  poNumbers: string;
  depotValue: string;
  uom: string;
  qtyCount: number;
  zipCode: string;

  // Step 2 - Appointment fields
  proNumber: string;
  requestDate: Date;
  doorType: string;
  unloadType: string;
  commodity: string;
  containerNumber: string;
  linkLoadId: string;
  note: string;
}

type CostcoTextInputProps = React.ComponentPropsWithoutRef<
  typeof RHFTextInput
> & {
  name: FieldPath<CostcoInputsWithoutLoad>;
};

const CostcoTextInput = (props: CostcoTextInputProps) => (
  <RHFTextInput {...props} />
);

interface CostcoFormProps {
  type: StopTypes;
  load: NormalizedLoad;
  integrationId?: number;
}

export function CostcoForm({ type, load, integrationId }: CostcoFormProps) {
  const { toast } = useToast();
  const scrollResultsIntoViewRef = useRef<HTMLDivElement>(null);

  const [isValidating, setIsValidating] = useState(false);
  const [isValid, setIsValid] = useState(false);
  const [isLoadingConfirm, setIsLoadingConfirm] = useState(false);
  const [apptConfirmationNumber, setApptConfirmationNumber] = useState('');
  const [doorTypeOptions, setDoorTypeOptions] = useState<
    Array<{ value: string; label: string }>
  >([]);

  const formMethods = useForm<CostcoInputsWithoutLoad>({
    defaultValues: {
      poNumbers: load.freightTrackingID || '',
      depotValue: '',
      uom: '',
      qtyCount: 0,
      zipCode: load.pickup?.zipCode || '',
      proNumber: '',
      requestDate: new Date(),
      doorType: '',
      unloadType: '',
      commodity: '',
      containerNumber: '',
      linkLoadId: '',
      note: '',
    },
    mode: 'onChange',
  });

  const { control, handleSubmit, getValues, watch, setValue } = formMethods;

  // Watch for changes in form values
  const poNumbers = watch('poNumbers');
  const depotValue = watch('depotValue');
  const uom = watch('uom');
  const qtyCount = watch('qtyCount');
  const zipCode = watch('zipCode');

  useEffect(() => {
    if (isValid) {
      scrollResultsIntoViewRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [isValid]);

  const validateAppointment = async () => {
    const formValues = getValues();

    // Convert single PO to array for API
    const poNumbers = formValues.poNumbers ? [formValues.poNumbers] : [];

    if (poNumbers.length === 0) {
      toast({
        description: 'Please enter a PO ID.',
        variant: 'destructive',
      });
      return;
    }

    if (!formValues.depotValue) {
      toast({
        description: 'Please select a depot value.',
        variant: 'destructive',
      });
      return;
    }

    if (!formValues.uom) {
      toast({
        description: 'Please select a UOM.',
        variant: 'destructive',
      });
      return;
    }

    const qtyCountNum = Number(formValues.qtyCount);
    if (!formValues.qtyCount || isNaN(qtyCountNum) || qtyCountNum <= 0) {
      toast({
        description: 'Please enter a valid quantity count.',
        variant: 'destructive',
      });
      return;
    }

    if (!formValues.zipCode) {
      toast({
        description: 'Please enter a zip code.',
        variant: 'destructive',
      });
      return;
    }

    setIsValidating(true);

    try {
      const validateRes = await validateAppt(
        'placeholder',
        SchedulingPortals.Costco,
        poNumbers,
        {
          depotValue: formValues.depotValue,
          uom: formValues.uom,
          qtyCount: Number(formValues.qtyCount),
          zipCode: formValues.zipCode,
        }
      );

      if (validateRes.isOk()) {
        const { validatedPONumbers } = validateRes.value;
        const isValid = validatedPONumbers.some(
          (po) => po.poNumber && po.isValid
        );

        // Extract door type options from validated PO numbers
        const validPO = validatedPONumbers.find(
          (po) => po.isValid && po.doorType
        );
        if (validPO && validPO.doorType) {
          const doorTypeOptions = Object.entries(validPO.doorType).map(
            ([key, value]) => ({ value: key, label: value })
          );
          setDoorTypeOptions(doorTypeOptions);
        }

        if (isValid) {
          setIsValid(true);
          toast({
            description:
              'Validation successful! You can now proceed to request an appointment.',
            variant: 'success',
          });
        } else {
          setIsValid(false);
          toast({
            description:
              'Validation failed. Please check your information and try again.',
            variant: 'destructive',
          });
        }
      } else {
        setIsValid(false);
        toast({
          description:
            validateRes.error.message || 'Failed to validate appointment.',
          variant: 'destructive',
        });
      }
    } catch {
      setIsValid(false);
      toast({
        description: 'An error occurred during validation.',
        variant: 'destructive',
      });
    } finally {
      setIsValidating(false);
    }
  };

  const handleMakeAppointment = async () => {
    const formValues = getValues();

    // Validate required fields for appointment
    if (!formValues.requestDate) {
      toast({
        description: 'Please select a request date.',
        variant: 'destructive',
      });
      return;
    }

    if (!formValues.doorType) {
      toast({
        description: 'Please select a door type.',
        variant: 'destructive',
      });
      return;
    }

    if (!formValues.unloadType) {
      toast({
        description: 'Please select an unload.',
        variant: 'destructive',
      });
      return;
    }

    if (!formValues.commodity) {
      toast({
        description: 'Please enter a commodity.',
        variant: 'destructive',
      });
      return;
    }

    setIsLoadingConfirm(true);

    try {
      const res = await confirmSlotAppt({
        source: SchedulingPortals.Costco,
        isTMSLoad: false,
        appointments: [
          {
            start: formValues.requestDate,
            freightTrackingId: formValues.poNumbers || '',
            proNumber: formValues.proNumber || '-',
            doorType: formValues.doorType || '-',
            unloadType: formValues.unloadType || '-',
            commodity: formValues.commodity || '-',
            containerNumber: formValues.containerNumber || '-',
            linkLoadId: formValues.linkLoadId || '-',
            notes: formValues.note || '-',
            uom: uom || '-',
            qtyCount: Number(qtyCount) || 0,
            depotValue: depotValue || '-',
            zipCode: zipCode || '-',
          },
        ],
        loadID: load.ID!,
        loadTypeId: '-',
        freightTrackingId: formValues.poNumbers || '-',
        integrationId: integrationId,
        stopType: type,
        start: formValues.requestDate,
        warehouseID: '-',
        warehouseTimezone: '-',
        dockId: '-',
      });

      if (res.isOk()) {
        setApptConfirmationNumber(res.value.ConfirmationNo);
        toast({
          description: 'Appointment requested successfully!',
          variant: 'success',
        });
      } else {
        if (res.error.message === 'Conflicting Appointments') {
          toast({
            title: 'Conflicting Appointments',
            description:
              "Make sure you don't have an existing appointment for this load.",
            variant: 'destructive',
          });
        } else {
          toast({
            description: res.error.message || 'Failed to create appointment.',
            variant: 'destructive',
          });
        }
      }
    } catch {
      toast({
        description: 'An error occurred while creating the appointment.',
        variant: 'destructive',
      });
    } finally {
      setIsLoadingConfirm(false);
    }
  };

  const onInvalid: SubmitErrorHandler<CostcoInputsWithoutLoad> = async () => {
    toast({
      description: 'Some fields are invalid.',
      variant: 'destructive',
    });
  };

  return (
    <ExtendedFormProvider aiDefaultValues={true}>
      <FormProvider {...formMethods}>
        <form
          onSubmit={handleSubmit(handleMakeAppointment, onInvalid)}
          className='flex flex-col gap-4 mt-4 mx-0 w-full'
        >
          <Flex direction='col' gap='lg'>
            {/* Validation Form */}
            <div className='rounded-lg border border-neutral-400 p-4 bg-neutral-50 shadow-sm'>
              <div className='mb-4'>
                <Typography variant='h5' className='mb-2' weight='medium'>
                  Validate Appointment
                </Typography>
                <Typography variant='body-xs' textColor='muted'>
                  Enter your PO information to validate the appointment request.
                </Typography>
              </div>

              <Flex direction='col' gap='md' className='w-full'>
                <CostcoTextInput name='poNumbers' label='PO ID' required />

                <div className='flex flex-col gap-2 w-full'>
                  <Label htmlFor='depotValue' name='depotValue' required>
                    Depot
                  </Label>
                  <Select
                    value={depotValue}
                    onValueChange={(value: string) => {
                      setValue('depotValue', value);
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select depot value' />
                    </SelectTrigger>
                    <SelectContent>
                      {DEPOT_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className='flex flex-col gap-2 w-full'>
                  <Label htmlFor='uom' name='uom' required>
                    UOM
                  </Label>
                  <Select
                    value={uom}
                    onValueChange={(value: string) => {
                      setValue('uom', value);
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select UOM' />
                    </SelectTrigger>
                    <SelectContent>
                      {UOM_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <CostcoTextInput
                  name='qtyCount'
                  label='QTY/PLT Count'
                  inputType='number'
                  placeholder='Enter quantity count'
                  required
                />

                <CostcoTextInput
                  name='zipCode'
                  label='Pickup Zip Code'
                  required
                />

                <Button
                  buttonNamePosthog={ButtonNamePosthog.ValidateCostcoPRONumber}
                  className='w-full'
                  type='button'
                  disabled={
                    isValidating ||
                    !poNumbers ||
                    poNumbers.trim() === '' ||
                    !depotValue ||
                    !uom ||
                    !qtyCount ||
                    Number(qtyCount) <= 0 ||
                    !zipCode
                  }
                  onClick={validateAppointment}
                >
                  {isValidating ? <ButtonLoader /> : 'Validate Appointment'}
                </Button>
              </Flex>
            </div>

            {/* Request Appointment Form - only show after validation and for dropoff */}
            <div
              className='rounded-lg border border-neutral-400 p-4 bg-neutral-50 shadow-sm'
              ref={scrollResultsIntoViewRef}
            >
              <div className='mb-4'>
                <Typography variant='h5' className='mb-2' weight='medium'>
                  Request Appointment
                </Typography>
                <Typography variant='body-xs' textColor='muted'>
                  Complete the appointment details to schedule your delivery.
                </Typography>
              </div>

              <Flex direction='col' gap='md' className='w-full'>
                <CostcoTextInput name='proNumber' label='Pro #' />

                <div className='w-full'>
                  <DateTimeInput
                    control={control}
                    name='requestDate'
                    label='Request Date'
                    preventNormalizedLabelTZ={true}
                    hideAIHint={true}
                    hideTimePicker={true}
                    required
                  />
                </div>

                <div className='flex flex-col gap-2 w-full'>
                  <Label htmlFor='doorType' name='doorType' required>
                    Door Type
                  </Label>
                  <Select
                    value={watch('doorType')}
                    onValueChange={(value: string) => {
                      setValue('doorType', value);
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select door type' />
                    </SelectTrigger>
                    <SelectContent>
                      {doorTypeOptions.length > 0
                        ? doorTypeOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))
                        : DOOR_TYPE_OPTIONS.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className='flex flex-col gap-2 w-full'>
                  <Label htmlFor='unloadType' name='unloadType' required>
                    Unload
                  </Label>
                  <Select
                    value={watch('unloadType')}
                    onValueChange={(value: string) => {
                      setValue('unloadType', value);
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select unload type' />
                    </SelectTrigger>
                    <SelectContent>
                      {UNLOAD_TYPE_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <CostcoTextInput name='commodity' label='Commodity' required />

                <CostcoTextInput name='containerNumber' label='Link Load #' />

                <CostcoTextInput name='linkLoadId' label='Link Load ID' />

                <CostcoTextInput name='note' label='Notes' />

                {apptConfirmationNumber ? (
                  <div className='whitespace-pre-wrap my-3 rounded py-3 text-neutral-900 px-4 bg-success-50'>
                    <Typography className='mb-2'>
                      Appointment requested successfully! 🎉
                    </Typography>
                    {/* <Typography variant='body-sm' className='mb-4'>
                        <Typography weight='bold'>
                          Costco Confirmation #:{' '}
                        </Typography>
                        {apptConfirmationNumber}
                      </Typography> */}
                  </div>
                ) : (
                  <Button
                    buttonNamePosthog={
                      ButtonNamePosthog.ConfirmSlotApptScheduling
                    }
                    className='mt-2 w-full'
                    type='submit'
                    disabled={isLoadingConfirm}
                  >
                    {isLoadingConfirm ? (
                      <ButtonLoader />
                    ) : (
                      'Request Appointment'
                    )}
                  </Button>
                )}
              </Flex>
            </div>
          </Flex>
        </form>
      </FormProvider>
    </ExtendedFormProvider>
  );
}
