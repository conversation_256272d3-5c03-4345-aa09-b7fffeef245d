import { useState } from 'react';
import { <PERSON>, FieldPath, FormProvider, useForm } from 'react-hook-form';

import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';

import { Button } from 'components/Button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from 'components/Select';
import DateTimeInput from 'components/input/DateTimeInput';
import FormInputWrapper from 'components/input/FormInputWrapper';
import { RHFTextInput } from 'components/input/RHFTextInput';
import { ReferenceNumberInput } from 'components/input/ReferenceNumberInput';
import { Flex, Grid } from 'components/layout';
import ButtonLoader from 'components/loading/ButtonLoader';
import { Typography } from 'components/typography';
import {
  DEFAULT_SEARCH_OPTION,
  SEARCH_OPTION_LABELS,
  SearchOption,
} from 'constants/SearchOptions';
import { ExtendedFormProvider } from 'contexts/extendedFormContext';
import { useToast } from 'hooks/useToaster';
import { confirmSlotAppt } from 'lib/api/confirmSlotAppt';
import { getOpenApptSlots } from 'lib/api/openApptSlots';
import {
  GroupedSlot,
  OrderedSlots,
  SchedulingPortals,
  StopTypes,
} from 'types/Appointment';
import { NormalizedLoad } from 'types/Load';
import { Maybe } from 'types/UtilityTypes';
import { Warehouse } from 'types/Warehouse';
import ButtonText from 'types/enums/ButtonText';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';
import { cn } from 'utils/shadcn';

dayjs.extend(utc);

interface OneNetworkInputsWithoutLoad {
  freightTrackingID: string;
  startDateTime: Date;
  endDateTime: Date;
  phone: string;
  email: string;
  requestedDateTime: Date;
  warehouseID: string;
  state: string;
  city: string;
  zipCode: string;
  searchBy: SearchOption;
}

type OneNetworkTextInputProps = React.ComponentPropsWithoutRef<
  typeof RHFTextInput
> & {
  name: FieldPath<OneNetworkInputsWithoutLoad>;
};
const OneNetworkTextInput = (props: OneNetworkTextInputProps) => (
  <RHFTextInput {...props} />
);

interface OneNetworkFormProps {
  type: StopTypes;
  load: NormalizedLoad;
  selectedWarehouse: Maybe<Warehouse>;
  integrationId?: number;
}

export function OneNetworkForm({
  type,
  load,
  selectedWarehouse,
  integrationId,
}: OneNetworkFormProps) {
  const { toast } = useToast();

  const [isLoadingSlots, setIsLoadingSlots] = useState(false);
  const [isLoadingConfirm, setIsLoadingConfirm] = useState(false);
  const [orderedSlots, setOrderedSlots] = useState<Maybe<OrderedSlots>>(null);
  const [selectedSlot, setSelectedSlot] = useState<Maybe<GroupedSlot>>(null);
  const [apptConfirmationNumber, setApptConfirmationNumber] = useState('');
  const [liveAppointmentsAvailable, setLiveAppointmentsAvailable] =
    useState(true);
  const [warehouseId, setWarehouseId] = useState('');

  const formMethods = useForm<OneNetworkInputsWithoutLoad>({
    defaultValues: {
      freightTrackingID: load.freightTrackingID || '',
      warehouseID: warehouseId ?? selectedWarehouse?.warehouseID ?? '',
      startDateTime: new Date(),
      endDateTime: dayjs().add(3, 'days').toDate(),
      phone: '',
      email: '',
      requestedDateTime: new Date(),
      state:
        type === StopTypes.Pickup
          ? load.pickup?.state || ''
          : load.consignee?.state || '',
      city:
        type === StopTypes.Pickup
          ? load.pickup?.city || ''
          : load.consignee?.city || '',
      zipCode:
        type === StopTypes.Pickup
          ? load.pickup?.zipCode || ''
          : load.consignee?.zipCode || '',
      searchBy: DEFAULT_SEARCH_OPTION,
    },
  });

  const { control, getValues } = formMethods;

  const loadAvailableSlots = async () => {
    const values = getValues();
    const defaultStart = dayjs().startOf('day');
    const defaultEnd = dayjs().add(7, 'days').endOf('day');
    const start = values.startDateTime
      ? dayjs(values.startDateTime)
      : defaultStart;
    const end = values.endDateTime ? dayjs(values.endDateTime) : defaultEnd;
    const today = dayjs().startOf('day');

    if (start && !end) {
      toast({
        description: 'End date is required when start date is provided',
        variant: 'default',
      });
      return;
    }

    if (start && end && start.isAfter(end)) {
      toast({
        description: values.endDateTime
          ? 'Start date cannot be after end date'
          : 'Please select an end date',
        variant: 'default',
      });
      return;
    }

    if (start && end) {
      const daysDifference = end.diff(start, 'day');
      if (daysDifference >= 2) {
        toast({
          description:
            'The difference between start and end dates cannot exceed 2 days',
          variant: 'default',
        });
        return;
      }
    }

    if (start?.isBefore(today, 'day') || end?.isBefore(today, 'day')) {
      toast({
        description: 'Appointment dates cannot be in the past',
        variant: 'default',
      });
      return;
    }

    if (!values.phone) {
      toast({
        description: 'Please enter a phone number.',
        variant: 'default',
      });
      return;
    }

    if (!values.email) {
      toast({
        description: 'Please enter an email address.',
        variant: 'default',
      });
      return;
    }

    if (!values.freightTrackingID) {
      toast({
        description: 'Please enter a PO number.',
        variant: 'default',
      });
      return;
    }

    if (!values.state) {
      toast({
        description: 'Please enter a state.',
        variant: 'default',
      });
      return;
    }

    if (!values.city) {
      toast({
        description: 'Please enter a city.',
        variant: 'default',
      });
      return;
    }

    if (!values.zipCode) {
      toast({
        description: 'Please enter a zip code.',
        variant: 'default',
      });
      return;
    }

    if (!values.searchBy) {
      toast({
        description: 'Please select a reference type.',
        variant: 'default',
      });
      return;
    }

    setIsLoadingSlots(true);

    try {
      const res = await getOpenApptSlots({
        source: SchedulingPortals.OneNetwork,
        freightTrackingID: values.freightTrackingID,
        loadTypeID: values.freightTrackingID,
        warehouseID: warehouseId ?? selectedWarehouse?.warehouseID,
        requestType: type,
        startDateTime: start,
        endDateTime: end,
        state: values.state,
        city: values.city,
        zipCode: values.zipCode,
        searchBy: values.searchBy,
      });

      if (res.isOk()) {
        setOrderedSlots(res.value);
        setLiveAppointmentsAvailable(true);
        setWarehouseId(res.value?.warehouse.warehouseID);
        setWarehouseId(
          res.value?.warehouse?.warehouseID ??
            selectedWarehouse?.warehouseID ??
            ''
        );

        if (!res.value?.slots || Object.keys(res.value.slots).length === 0) {
          toast({
            description: 'No available appointment times found.',
            variant: 'default',
          });
          return false;
        }
        return true;
      } else {
        toast({
          description:
            res.error.message || 'Failed to load available appointments.',
          variant: 'destructive',
        });
        return false;
      }
    } catch {
      toast({
        description: 'Failed to load available appointments.',
        variant: 'destructive',
      });
      return false;
    } finally {
      setIsLoadingSlots(false);
    }
  };

  const handleConfirmAppointment = async () => {
    if (!selectedSlot || !orderedSlots) {
      toast({
        description: 'Please select an available time slot',
        variant: 'default',
      });
      return;
    }

    setIsLoadingConfirm(true);

    try {
      const res = await confirmSlotAppt({
        source: SchedulingPortals.OneNetwork,
        isTMSLoad: false,
        stopType: type,
        start: selectedSlot.startTime,
        loadTypeId: 'placeholder',
        warehouseID: warehouseId ?? selectedWarehouse?.warehouseID ?? '',
        warehouseTimezone: 'UTC',
        dockId: 'placeholder',
        loadID: load.ID!,
        freightTrackingId: getValues().freightTrackingID,
        integrationId: integrationId,
        requestType: type,
        phone: getValues().phone,
        email: getValues().email,
        appointments: [
          {
            start: selectedSlot.startTime,
            freightTrackingId: getValues().freightTrackingID,
            state: getValues().state,
            city: getValues().city,
            zipCode: getValues().zipCode,
            searchBy: getValues().searchBy,
          },
        ],
      });

      if (res.isOk()) {
        setApptConfirmationNumber(res.value.ConfirmationNo);
        toast({
          description: 'Appointment confirmed successfully!',
          variant: 'success',
        });
      } else {
        if (res.error.message === 'Conflicting Appointments') {
          toast({
            title: 'Conflicting Appointments',
            description:
              "Make sure you don't have an existing appointment for this load.",
            variant: 'destructive',
          });
        } else {
          toast({
            description: res.error.message || 'Failed to confirm appointment.',
            variant: 'destructive',
          });
        }
      }
    } catch {
      toast({
        description: 'An error occurred while confirming the appointment.',
        variant: 'destructive',
      });
    } finally {
      setIsLoadingConfirm(false);
    }
  };

  return (
    <ExtendedFormProvider aiDefaultValues={true}>
      <FormProvider {...formMethods}>
        <form className='mt-4 mx-0 w-full'>
          <Flex direction='col'>
            <div
              className={cn(
                'rounded-lg border border-neutral-400 p-4 bg-neutral-50 shadow-sm',
                !liveAppointmentsAvailable && 'opacity-50 pointer-events-none'
              )}
            >
              <div className='mb-6'>
                <Typography variant='h5' className='mb-2' weight='medium'>
                  Book Live Appointment
                </Typography>
                <Typography variant='body-sm' className='text-neutral-500'>
                  {liveAppointmentsAvailable
                    ? 'Book an appointment immediately from available time slots.'
                    : 'Live appointments are not available for this PO.'}
                </Typography>
              </div>

              <Flex direction='col' gap='md' className='w-full'>
                <ReferenceNumberInput
                  name={'freightTrackingID'}
                  control={control}
                  label={'PO Number'}
                  placeholder={'Enter PO Number'}
                  required
                  load={load}
                  generateOptions={(normalizedLoad: NormalizedLoad) =>
                    generateReferenceNumberOptions(normalizedLoad)
                  }
                />

                <FormInputWrapper name='searchBy' label='Search By' required>
                  <Controller
                    control={control}
                    name='searchBy'
                    render={({ field }) => (
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <SelectTrigger className='w-full'>
                          <SelectValue placeholder='Select a reference type' />
                        </SelectTrigger>
                        <SelectContent>
                          {Object.entries(SEARCH_OPTION_LABELS).map(
                            ([value, label]) => (
                              <SelectItem key={value} value={value}>
                                {label}
                              </SelectItem>
                            )
                          )}
                        </SelectContent>
                      </Select>
                    )}
                  />
                </FormInputWrapper>

                <OneNetworkTextInput
                  name='phone'
                  label='Phone Number'
                  placeholder='Enter phone number'
                  required
                />

                <OneNetworkTextInput
                  name='email'
                  label='Email'
                  placeholder='Enter email'
                  required
                />

                <OneNetworkTextInput
                  name='state'
                  label='State'
                  placeholder='Enter state'
                  required
                />

                <OneNetworkTextInput
                  name='city'
                  label='City'
                  placeholder='Enter city'
                  required
                />

                <OneNetworkTextInput
                  name='zipCode'
                  label='Zip Code'
                  placeholder='Enter zip code'
                  required
                />

                <DateTimeInput
                  control={control}
                  name='startDateTime'
                  label='Search From'
                  preventNormalizedLabelTZ={true}
                  hideAIHint={true}
                />

                <DateTimeInput
                  control={control}
                  name='endDateTime'
                  label='Search To'
                  preventNormalizedLabelTZ={true}
                  hideAIHint={true}
                />

                <Button
                  type='button'
                  className='w-full'
                  disabled={isLoadingSlots || !liveAppointmentsAvailable}
                  buttonNamePosthog={ButtonNamePosthog.FindOpenApptSlots}
                  onClick={loadAvailableSlots}
                >
                  {isLoadingSlots ? (
                    <ButtonLoader />
                  ) : (
                    ButtonText.GetOpenApptSlots
                  )}
                </Button>
              </Flex>

              {orderedSlots && (
                <div className='mt-4'>
                  {Object.entries(orderedSlots.slots).map(([date, slots]) => (
                    <div key={date}>
                      <Typography
                        variant='h6'
                        weight='bold'
                        className='text-neutral-400 uppercase text-sm mt-4'
                      >
                        {date}
                      </Typography>
                      <Grid cols='3' gap='xs' className='mt-2 mx-0 w-full'>
                        {slots.map((slot, idx) => (
                          <button
                            type='button'
                            key={idx}
                            onClick={() =>
                              setSelectedSlot(
                                selectedSlot === slot ? null : slot
                              )
                            }
                            className={cn(
                              'text-neutral-900 bg-neutral-50 border border-neutral-400 p-1 py-2 rounded cursor-pointer text-sm',
                              selectedSlot === slot &&
                                'bg-brand border-brand-700 text-neutral-50'
                            )}
                          >
                            {dayjs.utc(slot.startTime).format('HH:mm')}
                          </button>
                        ))}
                      </Grid>
                    </div>
                  ))}

                  {selectedSlot && (
                    <div className='mt-4 text-neutral-400 text-left'>
                      <Typography
                        weight='bold'
                        className='my-1'
                        variant='body-sm'
                      >
                        Selected Slot:
                      </Typography>
                      <Typography className='mb-2' variant='body-sm'>
                        {dayjs
                          .utc(selectedSlot.startTime)
                          .format('MMM D, YYYY, HH:mm')}
                      </Typography>

                      {apptConfirmationNumber ? (
                        <div className='whitespace-pre-wrap my-3 rounded py-3 text-neutral-900 px-4 bg-success-50'>
                          <Typography className='mb-2'>
                            Appointment confirmed 🎉
                          </Typography>
                          <Typography variant='body-sm' className='mb-4'>
                            <Typography weight='bold'>
                              OneNetwork Confirmation #:{' '}
                            </Typography>
                            {apptConfirmationNumber}
                          </Typography>
                        </div>
                      ) : (
                        <Button
                          buttonNamePosthog={
                            ButtonNamePosthog.ConfirmSlotApptScheduling
                          }
                          className='mt-2 w-full'
                          onClick={handleConfirmAppointment}
                          disabled={isLoadingConfirm}
                        >
                          {isLoadingConfirm ? (
                            <ButtonLoader />
                          ) : (
                            'Confirm appointment'
                          )}
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>
          </Flex>
        </form>
      </FormProvider>
    </ExtendedFormProvider>
  );
}

type ReferenceOption = {
  value: string;
  label: string;
  source: string;
};

function generateReferenceNumberOptions(
  normalizedLoad: NormalizedLoad
): ReferenceOption[] {
  const options: ReferenceOption[] = [];
  const seenValues = new Set<string>();

  if (normalizedLoad?.freightTrackingID) {
    const freightTrackingId = normalizedLoad.freightTrackingID;
    if (freightTrackingId && !seenValues.has(freightTrackingId)) {
      seenValues.add(freightTrackingId);
      options.push({
        value: freightTrackingId,
        label: `Freight Tracking ID: ${freightTrackingId}`,
        source: 'freightTrackingID',
      });
    }
  }

  if (normalizedLoad?.pickup?.refNumber) {
    const pickupRef = normalizedLoad.pickup.refNumber.replace(/^PO\s*/i, '');
    if (pickupRef && !seenValues.has(pickupRef)) {
      seenValues.add(pickupRef);
      options.push({
        value: pickupRef,
        label: `Pickup Ref: ${pickupRef}`,
        source: 'pickup',
      });
    }
  }

  if (normalizedLoad?.consignee?.refNumber) {
    const consigneeRef = normalizedLoad.consignee.refNumber;
    if (consigneeRef && !seenValues.has(consigneeRef)) {
      seenValues.add(consigneeRef);
      options.push({
        value: consigneeRef,
        label: `Consignee Ref: ${consigneeRef}`,
        source: 'consignee',
      });
    }
  }

  if (normalizedLoad?.poNums) {
    const poNumbers = normalizedLoad.poNums
      .split(',')
      .map((po: string) => po.trim())
      .filter((po: string) => po);
    poNumbers.forEach((po: string) => {
      if (po && !seenValues.has(po)) {
        seenValues.add(po);
        options.push({
          value: po,
          label: `PO Number: ${po}`,
          source: 'poNums',
        });
      }
    });
  }

  return options;
}
