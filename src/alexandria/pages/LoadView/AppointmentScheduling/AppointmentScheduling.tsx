import { useContext, useEffect, useRef, useState } from 'react';

import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore library installed on parent module, overriding tsc check
import DOMPurify from 'dompurify';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore react-quill is in the parent dir
import JoditEditor from 'jodit-react';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore jodit is in the parent dir
import { IJodit } from 'jodit/esm/types';
import { ArrowLeft, Mail } from 'lucide-react';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import { Button } from 'components/Button';
import { Card, CardContent } from 'components/Card';
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from 'components/Tooltip';
import { Flex } from 'components/layout';
import { Typography } from 'components/typography';
import { IntegrationCore, ServiceContext } from 'contexts/serviceContext';
import useFetchEmailTemplates from 'hooks/useFetchEmailTemplates';
import { useToast } from 'hooks/useToaster';
import CostcoLogo from 'icons/CostcoLogo';
import E2openLogo from 'icons/E2OpenLogo';
import ManhattanLogo from 'icons/ManhattanLogo';
import OneNetworkLogo from 'icons/OneNetworkLogo';
import OpendockLogo from 'icons/OpendockLogo';
import RetalixLogo from 'icons/RetalixLogo';
import YardviewLogo from 'icons/YardviewLogo';
import { saveApptEmailRequest } from 'lib/api/saveApptEmailRequest';
import { SchedulingPortals, StopTypes } from 'types/Appointment';
import { TemplateType } from 'types/EmailTemplates';
import { NormalizedLoad } from 'types/Load';
import { Undef } from 'types/UtilityTypes';
import { Maybe } from 'types/UtilityTypes';
import { Warehouse } from 'types/Warehouse';
import {
  CopyToClipboardOptions,
  copyToClipboard,
  createRichTextContent,
  isHTMLContent,
} from 'utils/copyToClipboard';
import { getJoditEditorConfig, nl2br } from 'utils/getJoditEditorHelpers';

import { CostcoForm } from './SchedulingPortalForms/CostcoForm';
import { E2openForm } from './SchedulingPortalForms/E2openForm';
import { ManhattanForm } from './SchedulingPortalForms/ManhattanForm';
import { OneNetworkForm } from './SchedulingPortalForms/OneNetworkForm';
import { OpendockForm } from './SchedulingPortalForms/OpendockForm';
import { RetalixForm } from './SchedulingPortalForms/RetalixForm';
import { YardViewForm } from './SchedulingPortalForms/YardViewForm';

type Portal = {
  id: string;
  name: string;
  icon: any;
  color: string;
  requiresCustomerInfo?: boolean;
  category?: 'standard' | 'beta';
  integrationId?: number;
  username?: string;
};

const getPortalConfig = (
  integrationName: string,
  id: number,
  username?: string
): Undef<Portal> => {
  const configMap: Partial<Record<SchedulingPortals, Portal>> = {
    [SchedulingPortals.Costco]: {
      id: SchedulingPortals.Costco,
      name: 'Costco',
      icon: CostcoLogo,
      color: 'blue',
      category: 'beta',
      integrationId: id,
      username: username,
    },
    [SchedulingPortals.E2open]: {
      id: SchedulingPortals.E2open,
      name: 'E2open',
      icon: E2openLogo,
      color: 'blue',
      category: 'beta',
      integrationId: id,
      username: username,
    },
    [SchedulingPortals.Manhattan]: {
      id: SchedulingPortals.Manhattan,
      name: 'Manhattan',
      icon: ManhattanLogo,
      color: 'blue',
      category: 'beta',
      integrationId: id,
      username: username,
    },
    [SchedulingPortals.OneNetwork]: {
      id: SchedulingPortals.OneNetwork,
      name: 'OneNetwork',
      icon: OneNetworkLogo,
      color: 'red',
      category: 'beta',
      integrationId: id,
      username: username,
    },
    [SchedulingPortals.Opendock]: {
      id: SchedulingPortals.Opendock,
      name: 'Opendock',
      icon: OpendockLogo,
      color: 'green',
      category: 'standard',
      integrationId: id,
      username: username,
    },
    [SchedulingPortals.Retalix]: {
      id: SchedulingPortals.Retalix,
      name: 'Retalix NCR',
      icon: RetalixLogo,
      color: 'green',
      category: 'beta',
      integrationId: id,
      username: username,
    },
    [SchedulingPortals.YardView]: {
      id: SchedulingPortals.YardView,
      name: 'Yardview',
      icon: YardviewLogo,
      color: 'blue',
      category: 'beta',
      integrationId: id,
      username: username,
    },
  };
  return Object.values(SchedulingPortals).includes(
    integrationName as SchedulingPortals
  )
    ? configMap[integrationName as SchedulingPortals]
    : undefined;
};

// Helper function to get color classes - this is the only place we use tailwind's root colors
const getColorClasses = (color: string) => {
  const colorMap: Record<string, { bg: string; text: string }> = {
    blue: { bg: 'bg-info-100', text: 'text-info-600' },
    green: { bg: 'bg-success-100', text: 'text-success-600' },
    red: { bg: 'bg-error-100', text: 'text-error-600' },
    yellow: { bg: 'bg-warning-100', text: 'text-warning-600' },
    orange: { bg: 'bg-brand-100', text: 'text-brand-600' },
  };
  return colorMap[color] || { bg: 'bg-neutral-100', text: 'text-neutral-600' };
};

dayjs.extend(utc);
dayjs.extend(timezone);

type AppointmentSchedulingProps = {
  type: StopTypes;
  load: NormalizedLoad;
  recentWarehouses: Warehouse[];
  selectedWarehouse: Maybe<Warehouse>;
  setSelectedWarehouse: (warehouse: Maybe<Warehouse>) => void;
};

// Helper function to get the integration ID for the selected warehouse
const getPortalIntegrationId = (
  selectedWarehouse: Maybe<Warehouse>,
  schedulerIntegrations: IntegrationCore[]
) => {
  return selectedWarehouse?.warehouseSource
    ? (schedulerIntegrations.find(
        (integration) => integration.name === selectedWarehouse.warehouseSource
      )?.id ?? 0)
    : 0;
};

export default function AppointmentScheduling({
  type,
  load,
  recentWarehouses,
  selectedWarehouse,
  setSelectedWarehouse,
}: AppointmentSchedulingProps) {
  const {
    serviceFeaturesEnabled: { isAppointmentEmailingEnabled },
    schedulerIntegrations,
  } = useContext(ServiceContext);

  const joditRef = useRef<IJodit | null>(null);

  const { toast } = useToast();

  const [selectedPortal, setSelectedPortal] = useState<Maybe<string>>(
    selectedWarehouse?.warehouseSource ?? null
  );
  const [selectedPortalIntegrationId, setSelectedPortalIntegrationId] =
    useState<number>(
      getPortalIntegrationId(selectedWarehouse, schedulerIntegrations)
    );
  const [isManualPortalSelection, setIsManualPortalSelection] = useState(false);

  const [showEmail, setShowEmail] = useState(false);
  const [templateText, setTemplateText] = useState('');

  const { emailTemplates } = useFetchEmailTemplates(
    load.ID,
    TemplateType.APPOINTMENT,
    type
  );

  const portals: Portal[] = schedulerIntegrations
    .map((integration) =>
      getPortalConfig(integration.name, integration.id, integration.username)
    )
    .filter((portal): portal is Portal => portal !== undefined);

  const getPortalsByCategory = () => {
    const standard = portals.filter((p) => p.category === 'standard');
    const beta = portals.filter((p) => p.category === 'beta');

    return { standard, beta };
  };

  const handlePortalSelect = (
    portalId: string,
    portalIntegrationId: number = 0
  ) => {
    setSelectedPortal(portalId);
    setSelectedPortalIntegrationId(portalIntegrationId);
    setIsManualPortalSelection(true);
  };

  const handleBack = () => {
    if (showEmail) {
      setShowEmail(false);
    } else {
      setSelectedPortal(null);
      setSelectedPortalIntegrationId(0);
      setIsManualPortalSelection(true); // Mark as manual so useEffect doesn't override
    }
  };

  useEffect(() => {
    const warehouseSource = selectedWarehouse?.warehouseSource ?? null;

    // Only auto-sync with warehouse source if not manually overridden
    if (!isManualPortalSelection && selectedPortal !== warehouseSource) {
      setSelectedPortal(warehouseSource);
    }

    // Update selectedPortalIntegrationId based on the current portal
    if (selectedPortal) {
      const integration = schedulerIntegrations.find(
        (integration) => integration.name === selectedPortal
      );
      const newIntegrationId = integration?.id ?? 0;

      if (newIntegrationId !== selectedPortalIntegrationId) {
        setSelectedPortalIntegrationId(newIntegrationId);
      }
    } else if (selectedPortalIntegrationId !== 0) {
      setSelectedPortalIntegrationId(0);
    }
  }, [
    selectedWarehouse,
    selectedPortal,
    schedulerIntegrations,
    selectedPortalIntegrationId,
    isManualPortalSelection,
  ]);

  // Reset manual selection flag when selectedWarehouse changes
  useEffect(() => {
    setIsManualPortalSelection(false);
  }, [selectedWarehouse]);

  useEffect(() => {
    if (emailTemplates?.appointmentRequestTemplateHTML?.body) {
      setTemplateText(
        nl2br(emailTemplates.appointmentRequestTemplateHTML.body)
      );
    }
  }, [emailTemplates]);

  // Filter selected warehouse so we don't select warehouses from different portals inside forms
  const filterSelectedWarehouse = (portal: SchedulingPortals) => {
    return selectedWarehouse?.warehouseSource === portal
      ? selectedWarehouse
      : null;
  };

  if (showEmail) {
    const renderEmailTemplate = () => {
      if (!templateText) {
        return '<Typography>No template available</Typography>';
      }

      const sanitizedHTML = DOMPurify.sanitize(templateText);
      return sanitizedHTML;
    };

    const copyEmailTemplate = async () => {
      if (!emailTemplates?.appointmentRequestTemplateHTML) {
        toast({
          description: 'No template available to copy.',
          variant: 'destructive',
        });
        return;
      }

      await saveApptEmailRequest(
        templateText,
        emailTemplates,
        load.freightTrackingID
      );

      const copyToClipboardOptions: CopyToClipboardOptions = {
        toastMessage: 'Template copied to clipboard',
        toastVariant: 'default',
        wrapInHTML: true,
        joditEditorRef: joditRef,
      };

      await copyToClipboard(
        isHTMLContent(templateText)
          ? createRichTextContent(templateText)
          : templateText,
        copyToClipboardOptions
      );
    };

    return (
      <div className='w-full mx-auto py-4 space-y-4'>
        <Flex align='center' gap='sm' className='mb-2'>
          <Button
            variant='ghost'
            size='sm'
            onClick={handleBack}
            className='p-1 hover:border-none'
            buttonNamePosthog={null}
          >
            <ArrowLeft className='h-4 w-4' />
          </Button>
          <Typography variant='h3'>Email Request</Typography>
        </Flex>

        <Typography variant='body-sm' className='text-neutral-600'>
          Copy this template and send it via email
        </Typography>

        <JoditEditor
          value={renderEmailTemplate()}
          config={getJoditEditorConfig({ refToUpdateAfterInit: joditRef })}
          onBlur={(e: any) => setTemplateText(e)}
        />

        <Button
          onClick={copyEmailTemplate}
          className='w-full'
          buttonNamePosthog={null}
          disabled={!emailTemplates?.appointmentRequestTemplateHTML}
        >
          Copy Template
        </Button>
      </div>
    );
  }

  if (selectedPortal) {
    const portal = portals.find(
      (p) =>
        p.id === selectedPortal &&
        p.integrationId === selectedPortalIntegrationId
    );

    if (!portal) {
      return null;
    }

    const commonProps = {
      type,
      load,
      integrationId: portal.integrationId,
    };

    return (
      <div className='w-full mx-auto py-4 space-y-4'>
        <Flex direction='col' gap='none'>
          <Flex align='center' gap='xs'>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant='ghost'
                    size='sm'
                    onClick={handleBack}
                    className='p-1 hover:border-opacity-0'
                    buttonNamePosthog={null}
                  >
                    <ArrowLeft className='h-5 w-5' />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side='top' className='rounded-md ml-2'>
                  Change scheduling portal
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <Typography variant='h5' weight='medium'>
              {portal?.name}
            </Typography>
          </Flex>

          {portal.username && (
            <Flex direction='row' gap='xs'>
              <Typography variant='body-xs' className='text-neutral-400'>
                Username:
              </Typography>
              <Typography variant='body-xs' className='text-neutral-500'>
                {portal.username}
              </Typography>
            </Flex>
          )}
        </Flex>

        <div className='space-y-4'>
          {portal.id === SchedulingPortals.Costco && (
            <CostcoForm key={portal.id} {...commonProps} />
          )}
          {portal.id === SchedulingPortals.E2open && (
            <E2openForm key={portal.id} {...commonProps} />
          )}
          {portal.id === SchedulingPortals.Manhattan && (
            <ManhattanForm {...commonProps} />
          )}
          {portal.id === SchedulingPortals.OneNetwork && (
            <OneNetworkForm
              key={portal.id}
              {...commonProps}
              selectedWarehouse={filterSelectedWarehouse(
                SchedulingPortals.OneNetwork
              )}
            />
          )}
          {portal.id === SchedulingPortals.Opendock && (
            <OpendockForm
              key={portal.id}
              {...commonProps}
              recentWarehouses={(recentWarehouses || []).filter(
                (w) => w && w.warehouseSource === SchedulingPortals.Opendock
              )}
              selectedWarehouse={filterSelectedWarehouse(
                SchedulingPortals.Opendock
              )}
              setSelectedWarehouse={setSelectedWarehouse}
            />
          )}
          {portal.id === SchedulingPortals.Retalix && (
            <RetalixForm
              key={portal.id}
              {...commonProps}
              recentWarehouses={(recentWarehouses || []).filter(
                (w) => w && w.warehouseSource === SchedulingPortals.Retalix
              )}
              selectedWarehouse={filterSelectedWarehouse(
                SchedulingPortals.Retalix
              )}
              setSelectedWarehouse={setSelectedWarehouse}
            />
          )}
          {portal.id === SchedulingPortals.YardView && (
            <YardViewForm
              key={portal.id}
              {...commonProps}
              recentWarehouses={(recentWarehouses || []).filter(
                (w) => w && w.warehouseSource === SchedulingPortals.YardView
              )}
              selectedWarehouse={filterSelectedWarehouse(
                SchedulingPortals.YardView
              )}
              setSelectedWarehouse={setSelectedWarehouse}
            />
          )}
        </div>
      </div>
    );
  }

  const { standard, beta } = getPortalsByCategory();

  const renderPortalCard = (portal: Portal, isBeta: boolean = false) => {
    const IconComponent = portal.icon;
    const colorClasses = getColorClasses(portal.color);

    return (
      <Card
        key={portal.id + portal.integrationId}
        className='w-full cursor-pointer transition-all hover:shadow-md hover:bg-neutral-50'
        onClick={() => handlePortalSelect(portal.id, portal.integrationId)}
      >
        <CardContent className='p-2'>
          <Flex align='center' gap='md'>
            <div className={`p-2 ${colorClasses.bg} rounded-lg shrink-0`}>
              <IconComponent
                className={`h-4 w-4 object-contain ${colorClasses.text}`}
              />
            </div>
            <div className='min-w-0 flex-1'>
              <Flex direction='col' gap='none' className='w-full'>
                <Flex
                  justify='between'
                  align='center'
                  gap='sm'
                  className='w-full'
                >
                  <Typography variant='h6' weight='medium'>
                    {portal.name}
                  </Typography>
                  {isBeta && (
                    <span className='text-[10px] text-neutral-50 bg-brand-main/90 rounded-full px-2 py-0.5'>
                      Beta
                    </span>
                  )}
                </Flex>

                {portal.username && (
                  <Flex direction='row' gap='xs'>
                    <Typography variant='body-xs' className='text-neutral-400'>
                      Username:
                    </Typography>
                    <Typography variant='body-xs' className='text-neutral-500'>
                      {portal.username.length > 20
                        ? portal.username.slice(0, 20) + '...'
                        : portal.username}
                    </Typography>
                  </Flex>
                )}
              </Flex>
            </div>
          </Flex>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className='w-full mx-auto mb-4'>
      <Flex align='center' gap='sm'>
        <Typography variant='h5' weight='medium' className='mt-2'>
          Book an Appointment
        </Typography>
      </Flex>

      {/* Portals Section */}
      <div className='mt-2'>
        <Flex align='center' gap='sm'>
          <Typography variant='h6' weight='medium' className='text-neutral-400'>
            Scheduling Portals
          </Typography>
        </Flex>

        <Flex direction='col' gap='sm' className='mt-2'>
          {standard.map((portal) => renderPortalCard(portal))}
          {beta.map((portal) => renderPortalCard(portal, true))}
        </Flex>
      </div>

      {/* Email Request Section */}
      {isAppointmentEmailingEnabled && (
        <div className='pt-4 border-t mt-4'>
          <Card
            className='w-full cursor-pointer transition-all hover:shadow-md hover:bg-neutral-50'
            onClick={() => setShowEmail(true)}
          >
            <CardContent className='p-3'>
              <Flex align='center' gap='md'>
                <div className='p-2 bg-brand-100 rounded-lg shrink-0'>
                  <Mail className='h-4 w-4 text-brand-600' />
                </div>
                <div className='min-w-0'>
                  <Typography variant='h6' weight='medium'>
                    Email Request
                  </Typography>
                  <Typography
                    variant='body-xs'
                    className='text-neutral-400 mt-1'
                  >
                    Get a template to send via email
                  </Typography>
                </div>
              </Flex>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
