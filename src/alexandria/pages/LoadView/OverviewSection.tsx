import { useMemo, useState } from 'react';

import _ from 'lodash';
import {
  ArrowDownIcon,
  ArrowUpIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  ExternalLinkIcon,
} from 'lucide-react';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import isProd from '@utils/isProd';

import { Badge } from 'components/Badge';
import { Button } from 'components/Button';
import CarrierVerificationCard from 'components/CarrierVerificationCard';
import {
  Tooltip,
  TooltipContent,
  TooltipPortal,
  TooltipProvider,
  TooltipTrigger,
} from 'components/Tooltip';
import { Flex } from 'components/layout';
import { Typography } from 'components/typography/Typography';
import { useServiceFeatures } from 'hooks/useServiceContext';
import { createTMSInstance } from 'lib/hosts/interface';
import {
  Dropoff,
  Load,
  LoadStopsItem,
  LoadStopsItemTypes,
  Pickup,
} from 'types/Load';
import { Maybe } from 'types/UtilityTypes';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';
import { TMS } from 'types/enums/Integrations';
import { titleCase } from 'utils/formatStrings';
import { getDrumkitContainer } from 'utils/getDrumkitContainer';

interface OverviewSectionProps {
  tmsName: TMS;
  load: Load;
  labels: string[];
  fromEmail: Maybe<string>;
}

const popoverRootContainer = getDrumkitContainer();

export default function OverviewSection({
  tmsName,
  load,
  labels,
  fromEmail,
}: OverviewSectionProps) {
  const {
    externalTMSID,
    freightTrackingID,
    pickup,
    consignee,
    status: loadStatus,
    moreThanTwoStops,
    stops,
    mode,
  } = load;

  const [showAllRouteStops, setShowAllRouteStops] = useState(false);

  let loadRoute = [
    {
      label: getRouteLabelPickupDropoff(pickup),
      type: LoadStopsItemTypes.Pickup,
    },
    {
      label: getRouteLabelPickupDropoff(consignee),
      type: LoadStopsItemTypes.Delivery,
    },
  ];

  if (moreThanTwoStops && stops?.length) {
    loadRoute = stops.map((s) => ({
      label: getRouteLabelStop(s),
      type: s.stopType,
    }));
  }

  const poNumbers = load.poNums ? load.poNums.split(',') : [];
  const equipmentType = load.specifications?.transportType;
  const carrierName = load.carrier?.name;

  const pickupStopCount = moreThanTwoStops
    ? stops?.filter((s) => s.stopType === LoadStopsItemTypes.Pickup).length
    : 1;

  const dropoffStopCount = moreThanTwoStops
    ? stops?.filter((s) => s.stopType === LoadStopsItemTypes.Delivery).length
    : 1;

  const {
    serviceFeaturesEnabled: {
      isCarrierVerificationEnabled,
      isMultiStopLoadViewEnabled,
    },
  } = useServiceFeatures();

  let externalTMSIDMoniker, freightTrackingIDMoniker;

  // TODO: add more tms integration url/support for hyperlink to integration load page
  const integrationLoadPageUrls: Record<TMS | string, string> = {
    [TMS.Turvo]: `https://app.turvo.com/#/SHc59HSy/shipments/${externalTMSID}`,
  };

  try {
    const tmsClient = createTMSInstance(tmsName);
    externalTMSIDMoniker = tmsClient.getFieldMoniker('externalTMSID');
    freightTrackingIDMoniker = tmsClient.getFieldMoniker('freightTrackingID');
  } catch (e) {
    if (!isProd()) console.error(e);
  }

  const externalLinkLabel = useMemo(() => {
    switch (tmsName) {
      case TMS.Turvo:
        return `${titleCase(tmsName)} Shipment`;
      default:
        return `${titleCase(tmsName)} Load`;
    }
  }, [tmsName]);

  return (
    <TooltipProvider>
      <Flex direction='col' gap='sm' className='pb-4 w-full text-sm'>
        {externalTMSID && externalTMSIDMoniker && (
          <Flex
            justify='between'
            align='center'
            gap='sm'
            wrap='wrap'
            className='px-4'
          >
            <Typography variant='h6' className='text-neutral-800'>
              {externalTMSIDMoniker}
            </Typography>
            <Badge
              variant='outline'
              className='border-brand bg-brand-50 text-brand capitalize'
            >
              {externalTMSID}
            </Badge>
          </Flex>
        )}

        {freightTrackingID && freightTrackingIDMoniker && (
          <Flex
            justify='between'
            align='center'
            gap='sm'
            wrap='wrap'
            className='w-full px-4'
          >
            <Typography variant='h6' className='text-neutral-800'>
              {freightTrackingIDMoniker}
            </Typography>
            <Badge variant='outline' className='border-neutral-500 capitalize'>
              {freightTrackingID}
            </Badge>
          </Flex>
        )}

        <Flex
          justify='between'
          align='center'
          gap='sm'
          wrap='wrap'
          className='w-full px-4'
        >
          <Typography variant='h6' className='text-neutral-800'>
            Load Status
          </Typography>
          <Badge
            variant='outline'
            className='border-brand bg-brand-50 text-brand capitalize'
          >
            {loadStatus ? loadStatus : 'Undetermined'}
          </Badge>
        </Flex>

        {isMultiStopLoadViewEnabled && moreThanTwoStops && (
          <Flex
            justify='between'
            align='center'
            gap='sm'
            wrap='wrap'
            className='w-full px-4'
          >
            <Typography variant='h6' className='text-neutral-800'>
              Stops
            </Typography>
            <Flex align='center' gap='sm'>
              <Tooltip delayDuration={10}>
                <TooltipTrigger asChild>
                  <Badge
                    variant='outline'
                    className='border-neutral-400 flex items-center gap-1'
                  >
                    <span>{pickupStopCount}</span>
                    <ArrowUpIcon size={14} className='!stroke-brand' />
                  </Badge>
                </TooltipTrigger>
                <TooltipContent>Number of pickup locations</TooltipContent>
              </Tooltip>
              <Tooltip delayDuration={10}>
                <TooltipTrigger asChild>
                  <Badge
                    variant='outline'
                    className='border-neutral-400 flex items-center gap-1'
                  >
                    <span>{dropoffStopCount}</span>
                    <ArrowDownIcon size={14} className='!stroke-success' />
                  </Badge>
                </TooltipTrigger>
                <TooltipContent>Number of dropoff locations</TooltipContent>
              </Tooltip>
            </Flex>
          </Flex>
        )}

        {poNumbers.length > 0 && (
          <Flex
            justify='between'
            gap='2xl'
            className={`w-full px-4
              ${poNumbers.length > 2 ? 'items-start' : 'items-center'}`} // align start if PO number render takes up > 1 line
          >
            <Typography variant='h6' className='text-neutral-800 shrink-0'>
              PO #
            </Typography>
            <Flex justify='end' gap='sm' wrap='wrap' role='list'>
              {poNumbers.map(
                (item, idx) =>
                  item &&
                  item.trim().length > 0 && (
                    <Badge
                      role='listitem'
                      key={idx}
                      variant='outline'
                      className='border-neutral-500 capitalize'
                    >
                      {item.trim()}
                    </Badge>
                  )
              )}
            </Flex>
          </Flex>
        )}

        {labels.length > 0 && (
          <>
            <Flex
              justify='between'
              align='center'
              gap='sm'
              wrap='wrap'
              className='w-full px-4'
            >
              <Typography variant='h6' className='text-neutral-800'>
                Email Labels
              </Typography>
              <div
                role='list'
                className='flex flex-row flex-wrap gap-2 justify-end'
              >
                {labels.map(
                  (item, idx) =>
                    item.length > 0 && (
                      <Badge
                        role='listitem'
                        key={idx}
                        variant='outline'
                        className='border-neutral-500 capitalize'
                      >
                        {item.trim()}
                      </Badge>
                    )
                )}
              </div>
            </Flex>
          </>
        )}

        {/* Origin/Destination */}
        {loadRoute.length > 0 && (
          <Flex gap='xl' justify='between' className='w-full px-4'>
            <h2 className='text-neutral-800 text-sm'>O/D</h2>
            <Flex direction='col' align='end' gap='xs'>
              {/* First stop */}
              {getRouteStopBadge(
                loadRoute[0].label,
                loadRoute[0].type === LoadStopsItemTypes.Pickup
              )}

              {/* Intermediate stops */}

              {loadRoute.length > 2 && (
                <>
                  {!showAllRouteStops ? (
                    <Button
                      buttonNamePosthog={ButtonNamePosthog.ShowAllRouteStops}
                      variant='ghost'
                      className='h-7 gap-0.5 mr-5.5 items-center text-xs text-neutral-800 underline px-0 border-none hover:border-none'
                      size='sm'
                      onClick={() => setShowAllRouteStops(!showAllRouteStops)}
                    >
                      <ChevronDownIcon size={16} />
                      {`${loadRoute.length - 2} interim stops`}
                    </Button>
                  ) : (
                    <>
                      <Button
                        buttonNamePosthog={ButtonNamePosthog.ShowAllRouteStops}
                        variant='ghost'
                        className='h-7 gap-0.5 mr-5.5 items-center text-xs text-neutral-800 underline px-0 border-none hover:border-none'
                        size='sm'
                        onClick={() => setShowAllRouteStops(!showAllRouteStops)}
                      >
                        <ChevronUpIcon size={16} />
                        {`Hide interim stops`}
                      </Button>

                      {loadRoute.map((route, i) => {
                        if (i === 0 || i === loadRoute.length - 1) {
                          return null;
                        }

                        return getRouteStopBadge(
                          route.label,
                          route.type === LoadStopsItemTypes.Pickup
                        );
                      })}
                    </>
                  )}
                </>
              )}

              {/* Last stop */}
              {getRouteStopBadge(
                loadRoute[loadRoute.length - 1].label,
                loadRoute[loadRoute.length - 1].type ===
                  LoadStopsItemTypes.Pickup
              )}
            </Flex>
          </Flex>
        )}

        {/* Mode */}
        {mode && (
          <div className='flex flex-row flex-wrap gap-2 justify-between w-full px-4 items-center'>
            <h2 className='text-neutral-800 text-sm'>Mode</h2>
            <Badge variant='outline' className='border-neutral-500 capitalize'>
              {mode}
            </Badge>
          </div>
        )}

        {/* Equipment Type */}
        {equipmentType && (
          <div className='flex flex-row flex-wrap gap-2 justify-between w-full px-4 items-center'>
            <h2 className='text-neutral-800 text-sm'>Equipment Type</h2>
            <Badge variant='outline' className='border-neutral-500 capitalize'>
              {equipmentType}
            </Badge>
          </div>
        )}

        {/* Carrier Name */}
        {carrierName && (
          <div className='flex flex-row flex-wrap gap-2 justify-between w-full px-4 items-center'>
            <h2 className='text-neutral-800 text-sm'>Carrier</h2>
            <Badge variant='outline' className='border-neutral-500 capitalize'>
              {carrierName}
            </Badge>
          </div>
        )}

        {/* Hyper link to integration load page */}
        {tmsName in integrationLoadPageUrls && (
          <Flex
            justify='between'
            align='center'
            gap='sm'
            wrap='wrap'
            className='w-full px-4'
          >
            <Typography variant='body-sm'>{externalLinkLabel}</Typography>
            <a
              href={integrationLoadPageUrls[tmsName]}
              target='_blank'
              rel='noopener noreferrer'
              className='text-brand-main text-sm underline gap-1 flex items-center'
            >
              <Typography variant='body-sm'>View in TMS</Typography>
              <ExternalLinkIcon size={16} />
            </a>
          </Flex>
        )}

        {isCarrierVerificationEnabled && fromEmail && (
          <CarrierVerificationCard fromEmail={fromEmail} />
        )}
      </Flex>
    </TooltipProvider>
  );
}

function getRouteLabelStop(stop: LoadStopsItem) {
  return stop?.address?.city && stop?.address?.state
    ? `${_.startCase(_.toLower(stop.address.city))}, ${stop.address.state}`
    : stop?.address?.city || stop?.address?.name;
}

function getRouteLabelPickupDropoff(loadStop: Pickup | Dropoff) {
  return loadStop?.city && loadStop?.state
    ? `${_.startCase(_.toLower(loadStop.city))}, ${loadStop.state}`
    : loadStop?.city || loadStop?.name;
}

function getRouteStopBadge(label: string, isPickup: boolean) {
  return (
    <Flex align='center' gap='xs'>
      <Badge
        variant='outline'
        className='border-neutral-500 capitalize px-1.5 text-[11px]'
      >
        {label}
      </Badge>
      <Tooltip delayDuration={10}>
        <TooltipTrigger asChild>
          {isPickup ? (
            <ArrowUpIcon size={16} className='text-neutral-800 !stroke-brand' />
          ) : (
            <ArrowDownIcon
              size={16}
              className='text-neutral-800 !stroke-success'
            />
          )}
        </TooltipTrigger>
        <TooltipPortal container={popoverRootContainer}>
          <TooltipContent>{isPickup ? 'Pickup' : 'Delivery'}</TooltipContent>
        </TooltipPortal>
      </Tooltip>
    </Flex>
  );
}
