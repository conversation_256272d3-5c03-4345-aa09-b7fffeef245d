import { NormalizedLoad } from 'types/Load';
import { datetimeFieldOptions } from 'utils/formValidators';

import { LoadDateTimeInput } from '../Inputs/LoadDateTimeInput';

type ThreeGStopFieldsProps = {
  currentLoad: NormalizedLoad;
  stopIndex: number;
};

export const ThreeGStopFields = ({
  stopIndex,
  currentLoad,
}: ThreeGStopFieldsProps) => (
  <>
    {/* Expected Arrival in 3G */}
    <LoadDateTimeInput
      name={`stops.${stopIndex}.expectedStartTime`}
      label='Expected Date'
      options={datetimeFieldOptions}
      load={currentLoad}
    />

    {/* Earliest Pickup in 3G */}
    <LoadDateTimeInput
      name={`stops.${stopIndex}.readyTime`}
      label='Customer Requested Date'
      options={datetimeFieldOptions}
      load={currentLoad}
    />

    {/* Appointment in 3G */}
    <LoadDateTimeInput
      name={`stops.${stopIndex}.apptStartTime`}
      label='Appointment'
      options={datetimeFieldOptions}
      load={currentLoad}
    />

    {/* Actual Arrival in 3G */}
    <LoadDateTimeInput
      name={`stops.${stopIndex}.actualStartTime`}
      label='Actual Arrival'
      options={datetimeFieldOptions}
      load={currentLoad}
    />

    {/* Actual Departure in 3G */}
    <LoadDateTimeInput
      name={`stops.${stopIndex}.actualEndTime`}
      label='Actual Departure'
      options={datetimeFieldOptions}
      load={currentLoad}
    />
  </>
);

type ThreeGTwoStopFieldsProps = {
  isPickup: boolean;
  currentLoad: NormalizedLoad;
};

export const ThreeGTwoStopFields = ({
  isPickup,
  currentLoad,
}: ThreeGTwoStopFieldsProps) => (
  <>
    {/* Expected Arrival in 3G */}
    <LoadDateTimeInput
      name={
        isPickup ? `carrier.expectedPickupTime` : `carrier.expectedDeliveryTime`
      }
      label='Expected Date'
      options={datetimeFieldOptions}
      load={currentLoad}
    />

    {/* Earliest Pickup in 3G */}
    <LoadDateTimeInput
      name={isPickup ? `pickup.readyTime` : `consignee.mustDeliver`}
      label='Customer Requested Date'
      options={datetimeFieldOptions}
      load={currentLoad}
    />

    {/* Appointment in 3G */}
    <LoadDateTimeInput
      name={isPickup ? `pickup.apptStartTime` : `consignee.apptStartTime`}
      label='Appointment'
      options={datetimeFieldOptions}
      load={currentLoad}
    />

    {/* Actual Arrival in 3G */}
    <LoadDateTimeInput
      name={isPickup ? `carrier.pickupStart` : `carrier.deliveryStart`}
      label='Actual Arrival'
      options={datetimeFieldOptions}
      load={currentLoad}
    />

    {/* Actual Departure in 3G */}
    <LoadDateTimeInput
      name={isPickup ? `carrier.pickupEnd` : `carrier.deliveryEnd`}
      label='Actual Departure'
      options={datetimeFieldOptions}
      load={currentLoad}
    />
  </>
);
