import { useEffect, useState } from 'react';
import { Controller, FieldPath, UseFormReturn } from 'react-hook-form';

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from 'components/Accordion';
import { Checkbox } from 'components/Checkbox';
import { RHFDebounceSelect } from 'components/input/RHFDebounceSelect';
import { InputValue } from 'components/input/RHFTextInput';
import { Flex } from 'components/layout';
import { useFieldAttributes } from 'hooks/useLoadContext';
import useTMSContext from 'hooks/useTMSContext';
import {
  LoadDateTimeInput,
  LoadSelectInput,
  LoadTextInput,
} from 'pages/LoadView/LoadInformation/Components';
import { NormalizedLoad, TMSLocation } from 'types/Load';
import { getFieldAttribute } from 'types/LoadAttributes';
import { Maybe } from 'types/UtilityTypes';
import { TMS } from 'types/enums/Integrations';
import { datetimeFieldOptions } from 'utils/formValidators';
import {
  GenericCompanySearchableFields,
  locationSearchHandler,
  mapLocationsToAntdOptions,
} from 'utils/loadInfoAndBuilding';

import { ThreeGTwoStopFields } from '../Components/ThreeG/ThreeG';

export function PickupSectionForm({
  suggestedFieldsPrevValues,
  formMethods,
  isLoadingLocations,
  locations,
  handleRefreshLocations,
  setLocations,
}: {
  suggestedFieldsPrevValues: any;
  formMethods: UseFormReturn<NormalizedLoad>;
  isLoadingLocations: boolean;
  locations: Maybe<TMSLocation[]>;
  handleRefreshLocations: () => void;
  setLocations: React.Dispatch<React.SetStateAction<Maybe<TMSLocation[]>>>;
}) {
  const stop = 'pickup';

  const {
    control,
    watch,
    setValue,
    formState: { errors },
  } = formMethods;

  const fieldAttrs = useFieldAttributes();
  const apptRequiredAttr = getFieldAttribute(fieldAttrs, 'pickup.apptRequired');

  const watchedLocationID = watch(`${stop}.externalTMSID`);
  const watchedLocationObj = watch(stop);

  const { tmsName, tmsID } = useTMSContext();
  const [isMcleodTMS, setIsMcleodTMS] = useState<boolean>(
    tmsName === TMS.McleodEnterprise
  );

  useEffect(() => {
    if (isMcleodTMS) {
      setIsMcleodTMS(true);
    }
  }, [tmsName]);

  useEffect(() => {
    if (watchedLocationID) {
      const selectedLoc = locations?.find(
        (loc) => loc.externalTMSID === watchedLocationID
      );
      if (!selectedLoc) {
        return;
      }
      // setLocations(injectSelectedObject(selectedLoc, locations ?? []));

      Object.entries(selectedLoc).forEach(([key, value]) => {
        setValue(`${stop}.${key}` as FieldPath<NormalizedLoad>, value, {
          shouldDirty: true,
        });
      });
    }
  }, [watchedLocationID]);

  const handleLocationSearch = async (
    field: GenericCompanySearchableFields,
    value: string
  ) => {
    return locationSearchHandler({
      tmsID,
      locations,
      setLocations,
      field,
      value,
    });
  };

  const currentLoad = formMethods.watch(); // Get current form values for all fields

  return (
    <>
      {!isMcleodTMS ? (
        <LoadTextInput name='pickup.name' label='Name' />
      ) : (
        <>
          <RHFDebounceSelect
            required={true}
            name={`${stop}.externalTMSID`}
            label='Name'
            control={control}
            errors={errors}
            data={locations}
            isLoading={isLoadingLocations}
            refreshHandler={handleRefreshLocations}
            fetchOptions={handleLocationSearch}
            mapOptions={mapLocationsToAntdOptions}
          />
        </>
      )}

      <Accordion type='multiple' className='w-full'>
        <AccordionItem value='pickup-address'>
          <AccordionTrigger className='text-sm font-medium py-2'>
            Address & Contact Info
          </AccordionTrigger>
          <AccordionContent className='space-y-3 pt-2'>
            <LoadTextInput name='pickup.addressLine1' label='Address Line 1' />
            <LoadTextInput name='pickup.addressLine2' label='Address Line 2' />
            <LoadTextInput name='pickup.city' label='City' />
            <LoadTextInput name='pickup.state' label='State' />
            <LoadTextInput
              name='pickup.zipCode'
              label='Zip Code'
              placeholder='12345'
              // options={zipCodeFieldOptions}
            />
            <LoadTextInput name='pickup.country' label='Country' />
            <LoadTextInput name='pickup.contact' label='Contact' />
            <LoadTextInput
              name='pickup.phone'
              label='Phone'
              placeholder='(*************'
              // options={phoneFieldOptions}
              inputValue={InputValue.PHONE_NUMBER}
            />
            <LoadTextInput
              name='pickup.email'
              label='Email'
              placeholder='<EMAIL>'
            />
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      <Accordion
        type='multiple'
        className='w-full'
        defaultValue={['pickup-appointment']}
      >
        <AccordionItem value='pickup-appointment'>
          <AccordionTrigger className='text-sm font-medium py-2'>
            Appointment Info
          </AccordionTrigger>
          <AccordionContent className='pt-2'>
            <Flex direction='col' gap='md'>
              {tmsName === TMS.ThreeG ? (
                <ThreeGTwoStopFields
                  isPickup={true}
                  currentLoad={currentLoad}
                />
              ) : (
                <>
                  <LoadTextInput
                    name='pickup.businessHours'
                    label='Business Hours'
                  />
                  <LoadTextInput name='pickup.refNumber' label='Ref #' />
                  <LoadDateTimeInput
                    name='pickup.readyTime'
                    label='Ready Time'
                    options={datetimeFieldOptions}
                    load={currentLoad}
                  />

                  {apptRequiredAttr &&
                    !apptRequiredAttr?.isNotSupported &&
                    watchedLocationObj?.apptRequired && (
                      <Controller
                        name={`${stop}.apptRequired`}
                        control={control}
                        render={({ field }) => (
                          <Flex align='center' gap='sm'>
                            <Checkbox
                              onCheckedChange={(checked) => {
                                field.onChange(checked);
                              }}
                              checked={field.value || undefined}
                              disabled={apptRequiredAttr?.isReadOnly}
                            />
                            <label
                              htmlFor={`${stop}.apptRequired`}
                              className='leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
                            >
                              {'Appointment Required?'}
                            </label>
                          </Flex>
                        )}
                      />
                    )}
                  <Controller
                    name={`${stop}.apptConfirmed`}
                    control={control}
                    render={({ field }) => (
                      <Flex align='center' gap='sm'>
                        <Checkbox
                          onCheckedChange={(checked) => {
                            field.onChange(checked);
                          }}
                          checked={field.value || undefined}
                        />
                        <label
                          htmlFor={`${stop}.apptConfirmed`}
                          className='leading-none text-neutral-600 peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
                        >
                          Confirm Appointment
                        </label>
                      </Flex>
                    )}
                  />
                  <LoadDateTimeInput
                    name='pickup.apptStartTime'
                    label='Appointment Start Time'
                    options={datetimeFieldOptions}
                    prevValue={suggestedFieldsPrevValues?.pickupApptTime}
                    load={currentLoad}
                  />
                  <LoadDateTimeInput
                    name='pickup.apptEndTime'
                    label='Appointment End Time'
                    options={datetimeFieldOptions}
                    load={currentLoad}
                  />

                  {tmsName === TMS.Turvo && (
                    <LoadSelectInput
                      name='pickup.apptType'
                      label='Appointment Type'
                      options={[
                        { value: 'FCFS', label: 'FCFS' },
                        { value: 'By appointment', label: 'By appointment' },
                      ]}
                    />
                  )}

                  <LoadTextInput
                    name='pickup.apptNote'
                    label='Appointment Note'
                  />
                </>
              )}
            </Flex>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </>
  );
}
