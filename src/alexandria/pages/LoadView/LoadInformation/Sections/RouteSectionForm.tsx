import { UseFormReturn } from 'react-hook-form';

import _ from 'lodash';
import { WarehouseIcon } from 'lucide-react';

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from 'components/Accordion';
import { Badge } from 'components/Badge';
import { Flex } from 'components/layout';
import { Typography } from 'components/typography';
import useTMSContext from 'hooks/useTMSContext';
import {
  AdditionalReference,
  LoadStopsItem,
  LoadStopsItemTypes,
  NormalizedLoad,
} from 'types/Load';
import { TMS } from 'types/enums/Integrations';
import { datetimeFieldOptions } from 'utils/formValidators';
import { cn } from 'utils/shadcn';

import {
  LoadDateTimeInput,
  LoadSelectInput,
  LoadTextInput,
} from '../../LoadInformation/Components';
import { ThreeGStopFields } from '../Components/ThreeG/ThreeG';

type RouteSectionFormProps = {
  stops: LoadStopsItem[];
  formMethods: UseFormReturn<NormalizedLoad>;
};

// Route section is meant for multi-stop loads, which can include any number of pickups and dropoffs.
// Non-multi-stop loads will simply have Pickup and Dropoff sections instead.
export function RouteSectionForm({
  stops,
  formMethods,
}: RouteSectionFormProps) {
  const { tmsName } = useTMSContext();
  const currentLoad = formMethods.watch();

  const isThreeG = tmsName === TMS.ThreeG;

  const sortedStops = [...stops].sort((a, b) => a.stopNumber - b.stopNumber);

  return (
    <div className='w-full'>
      <div className='space-y-4'>
        {sortedStops.map((stop, index) => {
          const stopLabel =
            stop.stopType === LoadStopsItemTypes.Pickup ? `Pickup` : `Dropoff`;

          return (
            <div
              key={index}
              className='px-3 py-3 bg-neutral-50 rounded border border-neutral-400 shadow-sm'
            >
              <Flex align='start' gap='sm' className='w-full'>
                <Flex direction='col' align='start' gap='sm' className='w-full'>
                  <Flex
                    align='center'
                    justify='between'
                    gap='md'
                    className='w-full'
                  >
                    <Flex align='start' className='w-full'>
                      {stop.address?.city && (
                        <Typography
                          variant='body-sm'
                          weight='semibold'
                          className='truncate max-w-[108px]'
                          title={_.startCase(_.toLower(stop.address.city))}
                        >
                          {_.startCase(_.toLower(stop.address.city))}
                        </Typography>
                      )}

                      {stop.address?.state && stop.address?.zip && (
                        <Typography
                          variant='body-sm'
                          weight='semibold'
                          className='whitespace-nowrap'
                        >
                          {`, ${stop.address.state} ${stop.address.zip}`}
                        </Typography>
                      )}
                    </Flex>

                    <Badge
                      variant='default'
                      className={cn(
                        'text-[10px]',
                        stop.stopType === LoadStopsItemTypes.Delivery
                          ? 'bg-success-50 border-success text-success'
                          : 'bg-brand-50 border-brand text-brand'
                      )}
                    >
                      {stopLabel}
                    </Badge>
                  </Flex>

                  <Flex align='center' gap='sm'>
                    <WarehouseIcon className='w-4 h-4' />
                    <Typography
                      variant='body-xs'
                      weight='bold'
                      className='text-neutral-600 leading-3.5'
                    >
                      {stop.address.name}
                    </Typography>
                  </Flex>
                </Flex>
              </Flex>

              <Accordion type='multiple' className='w-full mt-3'>
                <AccordionItem value={`${index}-address`}>
                  <AccordionTrigger className='text-sm font-medium py-2'>
                    Address & Contact Info
                  </AccordionTrigger>
                  <AccordionContent className='space-y-3 pt-2'>
                    <LoadTextInput
                      name={`stops.${index}.address.name`}
                      label='Name'
                    />
                    <LoadTextInput
                      name={`stops.${index}.address.city`}
                      label='City'
                    />
                    <LoadTextInput
                      name={`stops.${index}.address.state`}
                      label='State'
                    />
                    <LoadTextInput
                      name={`stops.${index}.address.zip`}
                      label='Zip Code'
                    />
                    <LoadTextInput
                      name={`stops.${index}.address.country`}
                      label='Country'
                    />
                    <LoadTextInput
                      name={`stops.${index}.contact`}
                      label='Contact'
                    />
                    <LoadTextInput
                      name={`stops.${index}.phone`}
                      label='Phone'
                    />
                    <LoadTextInput
                      name={`stops.${index}.email`}
                      label='Email'
                    />
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value={`${index}-appointment`}>
                  <AccordionTrigger className='text-sm font-medium py-2'>
                    Appointment Info
                  </AccordionTrigger>
                  <AccordionContent className='pt-2'>
                    <Flex direction='col' gap='md'>
                      {isThreeG ? (
                        <ThreeGStopFields
                          stopIndex={index}
                          currentLoad={currentLoad}
                        />
                      ) : (
                        <>
                          <LoadTextInput
                            name={`stops.${index}.businessHours`}
                            label='Business Hours'
                          />

                          <LoadTextInput
                            name={`stops.${index}.refNumber`}
                            label='Ref #'
                          />

                          {stop.stopType === LoadStopsItemTypes.Pickup && (
                            <LoadDateTimeInput
                              name={`stops.${index}.readyTime`}
                              label='Ready Time'
                              options={datetimeFieldOptions}
                              load={currentLoad}
                            />
                          )}

                          {stop.stopType === LoadStopsItemTypes.Delivery && (
                            <LoadTextInput
                              name={`stops.${index}.mustDeliver`}
                              label='Must Deliver By'
                            />
                          )}

                          <LoadDateTimeInput
                            name={`stops.${index}.apptStartTime`}
                            label='Appointment Start Time'
                            options={datetimeFieldOptions}
                            load={formMethods.watch()}
                          />
                          <LoadDateTimeInput
                            name={`stops.${index}.apptEndTime`}
                            label='Appointment End Time'
                            options={datetimeFieldOptions}
                            load={formMethods.watch()}
                          />

                          {tmsName === TMS.Turvo && (
                            <LoadSelectInput
                              name={`stops.${index}.apptType`}
                              label='Appointment Type'
                              options={[
                                { value: 'FCFS', label: 'FCFS' },
                                {
                                  value: 'By appointment',
                                  label: 'By appointment',
                                },
                              ]}
                            />
                          )}

                          <LoadTextInput
                            name={`stops.${index}.apptNote`}
                            label='Appointment Note'
                          />
                        </>
                      )}
                    </Flex>
                  </AccordionContent>
                </AccordionItem>

                {stop.additionalReferences &&
                  stop.additionalReferences.length > 0 && (
                    <AccordionItem value={`${index}-shipment`}>
                      <AccordionTrigger className='text-sm font-medium py-2'>
                        Shipment Details
                      </AccordionTrigger>
                      <AccordionContent className='pt-2'>
                        <div className='space-y-3'>
                          {stop.additionalReferences.map(
                            (_ref: AdditionalReference, refIndex: number) => (
                              <div
                                key={refIndex}
                                className='border border-neutral-200 rounded p-3'
                              >
                                <Typography
                                  variant='body-sm'
                                  weight='medium'
                                  className='mb-2'
                                >
                                  Reference {refIndex + 1}
                                </Typography>
                                <div className='grid grid-cols-2 gap-3 mx-0 w-full'>
                                  <LoadTextInput
                                    name={`stops.${index}.additionalReferences.${refIndex}.number`}
                                    label='Reference Number'
                                  />
                                  <LoadTextInput
                                    name={`stops.${index}.additionalReferences.${refIndex}.qualifier`}
                                    label='Qualifier'
                                  />
                                  <LoadTextInput
                                    name={`stops.${index}.additionalReferences.${refIndex}.weight`}
                                    label='Weight'
                                    inputType='number'
                                  />
                                  <LoadTextInput
                                    name={`stops.${index}.additionalReferences.${refIndex}.pieces`}
                                    label='Pieces'
                                    inputType='number'
                                  />
                                </div>
                              </div>
                            )
                          )}
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  )}
              </Accordion>
            </div>
          );
        })}
      </div>
    </div>
  );
}
