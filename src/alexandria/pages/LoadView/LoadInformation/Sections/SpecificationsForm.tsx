import { useEffect, useState } from 'react';
import { Controller, UseFormReturn } from 'react-hook-form';

import { ErrorMessage } from '@hookform/error-message';
import { Select as AntdSelect } from 'antd';
import { BaseOptionType } from 'antd/es/select';

import { Label } from 'components/Label';
import { Select } from 'components/Select';
import {
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from 'components/Select';
import { Flex, Grid } from 'components/layout';
import { Typography } from 'components/typography';
import { IntegrationCore } from 'contexts/serviceContext';
import { useFieldAttributes, useLoadContext } from 'hooks/useLoadContext';
import useTMSContext from 'hooks/useTMSContext';
import {
  LoadSelectInput,
  LoadTextInput,
} from 'pages/LoadView/LoadInformation/Components';
import {
  getCommodityOptions,
  getTransportTypeOptions,
} from 'pages/QuoteView/LoadBuilding/McleodSectionForms/helpers';
import { unitsList } from 'pages/QuoteView/LoadBuilding/TurvoSectionForms/constants';
import { Load, NormalizedLoad } from 'types/Load';
import { getFieldAttribute, initFieldAttributes } from 'types/LoadAttributes';
import { TransportType } from 'types/QuoteRequest';
import { TMS } from 'types/enums/Integrations';
import { resolveTransportType } from 'utils/loadInfoAndBuilding';

export function SpecificationsForm({
  load,
  formMethods,
}: {
  load: Load | NormalizedLoad;
  formMethods: UseFormReturn<NormalizedLoad>;
}) {
  const {
    control,
    formState: { errors },
    setValue,
    getValues,
  } = formMethods;
  const { loadAttrsObj: attrs } = useLoadContext();
  const specs = load.specifications;
  const { tmsName, tenant, tmsID } = useTMSContext();
  const [isMcleodTMS, setIsMcleodTMS] = useState<boolean>(
    tmsName === TMS.McleodEnterprise
  );

  const isTurvoTMS = tmsName === TMS.Turvo;
  const isAljexTMS = tmsName === TMS.Aljex;

  const allFieldAttrs = useFieldAttributes();

  const getFieldAttr = (fieldName: string) =>
    getFieldAttribute(allFieldAttrs, fieldName) ?? initFieldAttributes;

  useEffect(() => {
    setIsMcleodTMS(tmsName === TMS.McleodEnterprise);
  }, [tmsName]);

  useEffect(() => {
    if (!isMcleodTMS && specs.transportType !== undefined) {
      const currentFormValue = getValues('specifications.transportType');
      const resolvedValueFromSpecs = resolveTransportType(
        specs.transportType ?? null
      );

      if (currentFormValue !== resolvedValueFromSpecs) {
        setValue('specifications.transportType', resolvedValueFromSpecs ?? '', {
          shouldDirty: true,
        });
      }
    }
  }, [specs.transportType, isMcleodTMS, getValues, setValue]);

  // Field support checks - common fields across all TMS
  const isWeightSupported = !attrs.specifications.totalWeight.isNotSupported;
  const isVolumeSupported = !attrs.specifications.totalVolume.isNotSupported;
  const isDistanceSupported =
    !attrs.specifications.totalDistance.isNotSupported;
  const isPalletSupported =
    !attrs.specifications.totalOutPalletCount.isNotSupported;
  const isPiecesSupported = !attrs.specifications.totalPieces.isNotSupported;
  const isPiecesTypeSupported =
    !attrs.specifications.totalPiecesType.isNotSupported;
  const isMinTempSupported =
    !attrs.specifications.minTempFahrenheit.isNotSupported;
  const isModeSupported = !attrs.mode.isNotSupported;

  // Turvo-specific field support checks (not in core attrs structure)
  const isNetWeightSupported = !getFieldAttr('specifications.netWeight')
    .isNotSupported;
  const isTransportWeightSupported = !getFieldAttr(
    'specifications.transportWeight'
  ).isNotSupported;
  const isTransportSizeSupported = !getFieldAttr('specifications.transportSize')
    .isNotSupported;
  const isServiceTypeSupported = !getFieldAttr('specifications.serviceType')
    .isNotSupported;

  const isPiecesTypeTrackedAsUnit =
    load.specifications.totalPiecesType &&
    load.specifications.totalPieces?.unit;

  const transportTypeOptions = isMcleodTMS
    ? getTransportTypeOptions({
        id: tmsID,
        name: tmsName,
        tenant,
      } as IntegrationCore)
    : undefined;

  const commodityOptions = isMcleodTMS
    ? getCommodityOptions({
        id: tmsID,
        name: tmsName,
        tenant,
      } as IntegrationCore)
    : undefined;

  return (
    <Flex direction='col' gap='md' className='w-full'>
      {/* Turvo-specific top fields */}
      {isTurvoTMS && isModeSupported && (
        <LoadTextInput name='mode' label='Mode' />
      )}

      {isTurvoTMS && isServiceTypeSupported && (
        <LoadTextInput name='specifications.serviceType' label='Service Type' />
      )}

      {/* Transport Type and Size row */}
      <Grid cols='2' gap='sm' className='w-full'>
        {/* Transport Type - always shown if supported */}
        {!getFieldAttr('specifications.transportType').isNotSupported && (
          <>
            {!isMcleodTMS ? (
              <div>
                <Label name={'specifications.transportType'}>
                  Transport Type
                </Label>
                <Controller
                  name='specifications.transportType'
                  control={control}
                  rules={{ required: false }}
                  render={({ field }) => {
                    return (
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <SelectTrigger className='w-full mt-1'>
                          <SelectValue placeholder='Choose' />
                        </SelectTrigger>
                        <SelectContent>
                          {Object.values(TransportType)
                            .filter(
                              // Filter out transport types that are not supported by the TMS
                              (value) =>
                                value !== TransportType.SPECIAL &&
                                value !== TransportType.HOTSHOT
                            )
                            .map((option) => (
                              <SelectItem key={option} value={option}>
                                {option}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                    );
                  }}
                />
                <ErrorMessage
                  errors={errors}
                  name={'specifications.transportType'}
                  render={({ message }: { message: string }) => (
                    <Typography variant='body-xs' className='text-error-500'>
                      {message}
                    </Typography>
                  )}
                />
              </div>
            ) : (
              <div>
                <Label name={'specifications.transportType'}>
                  Transport Type
                </Label>
                <Controller
                  name='specifications.transportType'
                  control={control}
                  rules={{ required: false }}
                  render={({ field }) => (
                    <AntdSelect
                      showSearch
                      className='h-9 text-neutral-500'
                      placeholder={'Choose'}
                      optionFilterProp='children'
                      filterOption={(
                        input: string,
                        option: BaseOptionType | undefined
                      ) =>
                        (option?.label.toLocaleLowerCase() ?? '').includes(
                          input.toLocaleLowerCase()
                        )
                      }
                      filterSort={(
                        optionA: BaseOptionType,
                        optionB: BaseOptionType
                      ) =>
                        (optionA?.label ?? '')
                          .toLowerCase()
                          .localeCompare((optionB?.label ?? '').toLowerCase())
                      }
                      onChange={field.onChange}
                      value={field.value}
                      options={transportTypeOptions?.map((type) => ({
                        value: type.label,
                        label: type.label,
                      }))}
                    />
                  )}
                />
                <ErrorMessage
                  errors={errors}
                  name={'specifications.transportType'}
                  render={({ message }: { message: string }) => (
                    <Typography variant='body-xs' className='text-error-500'>
                      {message}
                    </Typography>
                  )}
                />
              </div>
            )}
          </>
        )}

        {/* Transport Size - Turvo only */}
        {isTurvoTMS && isTransportSizeSupported ? (
          <LoadTextInput name='specifications.transportSize' label='Size' />
        ) : (
          <div />
        )}
      </Grid>

      {/* Commodity, Quantity, and Units row */}
      <Grid cols='3' gap='sm' className='w-full'>
        {/* Commodity/Item */}
        {!getFieldAttr('specifications.commodities').isNotSupported ? (
          <>
            {isMcleodTMS ? (
              <div>
                <Label name={'specifications.commodities'}>Commodity</Label>
                <Controller
                  name='specifications.commodities'
                  control={control}
                  rules={{ required: false }}
                  render={({ field }) => (
                    <AntdSelect
                      showSearch
                      className='h-9 text-neutral-500'
                      placeholder={'Choose'}
                      optionFilterProp='children'
                      filterOption={(
                        input: string,
                        option: BaseOptionType | undefined
                      ) =>
                        (option?.label.toLocaleLowerCase() ?? '').includes(
                          input.toLocaleLowerCase()
                        )
                      }
                      filterSort={(
                        optionA: BaseOptionType,
                        optionB: BaseOptionType
                      ) =>
                        (optionA?.label ?? '')
                          .toLowerCase()
                          .localeCompare((optionB?.label ?? '').toLowerCase())
                      }
                      onChange={field.onChange}
                      value={field.value}
                      options={commodityOptions?.map((type) => ({
                        value: type.code,
                        label: type.description,
                      }))}
                    />
                  )}
                />
                <ErrorMessage
                  errors={errors}
                  name={'specifications.commodities'}
                  render={({ message }: { message: string }) => (
                    <Typography variant='body-xs' className='text-error-500'>
                      {message}
                    </Typography>
                  )}
                />
              </div>
            ) : (
              <LoadTextInput name='specifications.commodities' label='Item' />
            )}
          </>
        ) : (
          <div />
        )}

        {/* Quantity */}
        {isPiecesSupported ? (
          <LoadTextInput
            name='specifications.totalPieces.val'
            label={`Quantity`}
            inputType='number'
            options={{ valueAsNumber: true }}
          />
        ) : (
          <div />
        )}

        {/* Units */}
        {isPiecesTypeSupported ? (
          <>
            {!isTurvoTMS ? (
              <LoadTextInput
                name={`specifications.${isPiecesTypeTrackedAsUnit ? 'totalPiecesType' : 'totalPieces.unit'}`}
                label='Units'
              />
            ) : (
              <LoadSelectInput
                name='specifications.totalPieces.unit'
                label='Units'
                options={unitsList.map((unit) => ({
                  value: unit,
                  label: unit,
                }))}
                required={false}
                placeholder='Choose'
              />
            )}
          </>
        ) : (
          <div />
        )}
      </Grid>

      {/* Weight fields */}
      <Flex direction='col' gap='md'>
        {isWeightSupported && (
          <LoadTextInput
            name='specifications.totalWeight.val'
            label={`Gross Weight${specs.totalWeight?.unit ? ' (' + specs.totalWeight?.unit + ')' : ''}`}
            inputType='number'
            options={{ valueAsNumber: true }}
          />
        )}

        {isTurvoTMS && isNetWeightSupported && (
          <LoadTextInput
            name='specifications.netWeight.val'
            label='Net Weight'
            inputType='number'
            options={{ valueAsNumber: true }}
          />
        )}
      </Flex>

      {/* Volume */}
      {isVolumeSupported && (
        <LoadTextInput
          name='specifications.totalVolume.val'
          label={`Volume${specs.totalVolume?.unit ? ' (' + specs.totalVolume?.unit + ')' : ''}`}
          inputType='number'
          options={{ valueAsNumber: true }}
        />
      )}

      {/* Additional fields row */}
      <Grid cols='2' gap='sm' className='w-full'>
        {isTurvoTMS && isTransportWeightSupported ? (
          <LoadTextInput
            name='specifications.transportWeight.val'
            label='Equipment Weight'
            inputType='number'
            options={{ valueAsNumber: true }}
          />
        ) : (
          <div />
        )}

        {isTurvoTMS && isMinTempSupported ? (
          <LoadTextInput
            name='specifications.minTempFahrenheit'
            label='Temperature'
            inputType='number'
            options={{ valueAsNumber: true }}
          />
        ) : (
          <div />
        )}

        {isDistanceSupported ? (
          <LoadTextInput
            name='specifications.totalDistance.val'
            label={`Distance${specs.totalDistance?.unit ? ' (' + specs.totalDistance?.unit + ')' : ''}`}
            inputType='number'
            options={{ valueAsNumber: true }}
          />
        ) : (
          <div />
        )}

        {isPalletSupported ? (
          <div className={isAljexTMS ? 'col-span-2' : ''}>
            <LoadTextInput
              name='specifications.totalOutPalletCount'
              label='Pallets'
              inputType='number'
              options={{ valueAsNumber: true }}
              readOnly={isAljexTMS}
            />
          </div>
        ) : (
          <div />
        )}
      </Grid>
    </Flex>
  );
}
