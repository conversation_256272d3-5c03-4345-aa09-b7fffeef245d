import React from 'react';

import { type VariantProps, cva } from 'class-variance-authority';

import { cn } from 'utils/shadcn';

const typographyVariants = cva('', {
  variants: {
    variant: {
      // Headings
      h1: 'text-3xl font-bold leading-tight tracking-tight',
      h2: 'text-2xl font-semibold leading-tight tracking-tight',
      h3: 'text-xl font-semibold leading-tight',
      h4: 'text-lg font-semibold leading-tight',
      h5: 'text-base font-semibold leading-tight',
      h6: 'text-sm font-semibold leading-tight',

      // Body text
      body: 'text-base leading-relaxed',
      'body-sm': 'text-sm leading-relaxed',
      'body-xs': 'text-xs leading-relaxed',
      'body-xxs': 'text-[10px] leading-relaxed',

      // Specialized text
      caption: 'text-xs text-neutral-500',
      overline: 'text-xs font-medium uppercase tracking-wider',
      subtitle1: 'text-lg leading-relaxed',
      subtitle2: 'text-base leading-relaxed',

      // Interactive text
      link: 'text-brand-main hover:text-brand-600 underline cursor-pointer',
      button: 'text-sm font-medium',

      // Form text
      label: 'text-sm font-medium text-neutral-900',
      input: 'text-[13px] text-neutral-500',
      error: 'text-xs text-error-500',
      hint: 'text-xs text-neutral-500',
    },
    textColor: {
      default: 'text-neutral-900',
      muted: 'text-neutral-500',
      brand: 'text-brand-main',
      error: 'text-error-500',
      success: 'text-success-500',
      warning: 'text-warning-500',
      info: 'text-info-500',
    },
    weight: {
      light: 'font-light',
      normal: 'font-normal',
      medium: 'font-medium',
      semibold: 'font-semibold',
      bold: 'font-bold',
    },
    align: {
      left: 'text-left',
      center: 'text-center',
      right: 'text-right',
      justify: 'text-justify',
    },
  },
  defaultVariants: {
    variant: 'body',
    textColor: 'default',
    weight: 'normal',
    align: 'left',
  },
});

interface TypographyProps
  extends Omit<React.HTMLAttributes<HTMLElement>, 'color'>,
    VariantProps<typeof typographyVariants> {
  as?: keyof React.JSX.IntrinsicElements;
  children: React.ReactNode;
}

const Typography = React.forwardRef<HTMLElement, TypographyProps>(
  (
    { className, variant, textColor, weight, align, as, children, ...props },
    ref
  ) => {
    // Determine the appropriate HTML element based on variant
    const getElement = (): keyof React.JSX.IntrinsicElements => {
      if (as) return as;

      switch (variant) {
        case 'h1':
        case 'h2':
        case 'h3':
        case 'h4':
        case 'h5':
        case 'h6':
          return variant;
        case 'link':
          return 'a';
        case 'button':
          return 'span';
        default:
          return 'p';
      }
    };

    const Element = getElement();
    const classNameValue = cn(
      typographyVariants({ variant, textColor, weight, align }),
      className
    );

    // Use React.createElement to avoid complex type issues
    return React.createElement(
      Element,
      {
        ref,
        className: classNameValue,
        ...props,
      },
      children
    );
  }
);

Typography.displayName = 'Typography';

export { Typography, typographyVariants };
export type { TypographyProps };
