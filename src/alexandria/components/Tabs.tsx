import * as React from 'react';

import * as TabsPrimitive from '@radix-ui/react-tabs';

import { cn } from 'utils/shadcn';

const Tabs = TabsPrimitive.Root;

const TabsList = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.List>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List> & {
    className?: string;
  }
>(({ className, ...props }, ref) => (
  <div className='relative'>
    <TabsPrimitive.List
      ref={ref}
      className={cn('flex gap-8 h-10 min-h-10 items-center !px-4', className)}
      {...props}
    />

    {/* Artificial border added to TabsList so the selected tab can override it's z-index */}
    <div className='absolute bottom-0 left-0 right-0 h-[1px] bg-neutral-300 z-1' />
  </div>
));
TabsList.displayName = TabsPrimitive.List.displayName;

const TabsTrigger = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger> & {
    className?: string;
    onClick?: (ev: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;
    variant?: string;
  }
>(({ className, onClick, variant, ...props }, ref) => (
  <TabsPrimitive.Trigger
    ref={ref}
    className={cn(
      'flex h-10 gap-2 z-10 items-center justify-center cursor-pointer whitespace-nowrap relative rounded-sm border-b-2 border-transparent transition-all disabled:pointer-events-none disabled:opacity-50 data-[state=active]:text-brand-main data-[state=active]:[&_[variant:sparkle-ai]]:text-info-main data-[state=active]:border-brand-main data-[state=active]:font-medium data-[state=active]:border-b-2 data-[state=inactive]:[&>p]:hidden',
      className,
      variant === TabsTriggerVariants.LoadTabs ||
        variant === TabsTriggerVariants.QuoteTabs
        ? 'flex-initial justify-center basis-[44px] min-w-0 px-[8px] rounded-none gap-0 data-[state=active]:flex-1 data-[state=active]:gap-[6px] [&_p]:data-[state=inactive]:hidden overflow-hidden'
        : ''
    )}
    onClick={(ev) => {
      // ev.currentTarget.scrollIntoView(true);
      onClick && onClick(ev);
    }}
    {...props}
  />
));
TabsTrigger.displayName = TabsPrimitive.Trigger.displayName;

const TabsContent = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content> & {
    className?: string;
  }
>(({ className, ...props }, ref) => (
  <TabsPrimitive.Content
    ref={ref}
    className={cn(
      'ring-offset-neutral-50 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-neutral-900 focus-visible:ring-offset-2',
      className
    )}
    {...props}
  />
));
TabsContent.displayName = TabsPrimitive.Content.displayName;

enum TabsTriggerVariants {
  LoadTabs = 'LoadTabs',
  QuoteTabs = 'QuoteTabs',
}

export { Tabs, TabsList, TabsTrigger, TabsContent, TabsTriggerVariants };
