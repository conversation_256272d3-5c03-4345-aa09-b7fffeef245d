import React, { ErrorInfo } from 'react';

import { isAxiosError } from 'axios';
import { AlertCircleIcon } from 'lucide-react';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import Login from '@auth/Login';

import ReloadExtension from 'components/ReloadExtension';
import DrumkitLogo from 'icons/DrumkitLogo';
import { Maybe } from 'types/UtilityTypes';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';
import captureException from 'utils/captureException';

import { Button } from './Button';
import { Flex } from './layout';
import { Typography } from './typography';

interface Props {
  children: React.ReactNode;
}

interface State {
  error: Maybe<Error>;
}

// NOTE: ErrorBoundary can only be defined as a class component
// https://react.dev/reference/react/Component#catching-rendering-errors-with-an-error-boundary
class ErrorBoundary extends React.Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { error: null };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI.
    return { error };
  }

  componentDidCatch(error: Error, _info: ErrorInfo) {
    if (!error) {
      return;
    }

    if (isAxiosError(error) && error.response?.status === 401) {
      return;
    }

    if (error.message === 'Extension context invalidated.') {
      return;
    }

    if (
      error.message ===
      "Cannot read properties of undefined (reading 'sendMessage')"
    ) {
      return;
    }

    captureException(error);
  }

  render() {
    const error = this.state.error;

    if (error && isAxiosError(error) && error.response?.status === 401) {
      return <Login />;
    }

    if (error && error.message === 'Extension context invalidated.') {
      return <ReloadExtension />;
    }

    if (
      error &&
      error.message ===
        "Cannot read properties of undefined (reading 'sendMessage')"
    ) {
      return <ReloadExtension />;
    }

    if (error && error.message.includes('Should have a queue')) {
      return <ReloadExtension />;
    }

    if (error) {
      return <ErrorMessage />;
    }

    return this.props.children;
  }
}

export default ErrorBoundary;

const ErrorMessage = () => (
  <Flex direction='col' align='center' justify='center' className='p-4'>
    <div className='mb-4 relative'>
      <DrumkitLogo className='w-16 h-16' />
      <AlertCircleIcon className='absolute bottom-0 -right-2 !stroke-error !fill-neutral-50' />
    </div>

    <Typography variant='h3' align='center' weight='bold' className='mb-2'>
      Drumkit had an unexpected error!
    </Typography>

    <Typography variant='body-sm' align='center' className='mb-2'>
      Please refresh the page to try again.
    </Typography>
    <Typography variant='body-sm' align='center' className='mb-4'>
      {`If the problem continues, reach out to our support team `}
      <a href='mailto:<EMAIL>' className='text-info underline'>
        here
      </a>
      {` or email us <NAME_EMAIL>.`}
    </Typography>
    <Typography variant='body-sm' align='center' className='mb-6'>
      We apologize for the inconvenience.
    </Typography>

    <Button
      variant='default'
      buttonNamePosthog={ButtonNamePosthog.RefreshPage}
      onClick={() => window.location.reload()}
      className='h-9 text-sm px-12'
    >
      Refresh Page
    </Button>
  </Flex>
);
