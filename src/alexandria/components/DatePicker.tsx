import { useState } from 'react';
import { ControllerRenderProps } from 'react-hook-form';

import dayjs from 'dayjs';
import { CalendarIcon } from 'lucide-react';

import { Button } from 'components/Button';
import { FieldAttributes } from 'types/LoadAttributes';
import { ButtonNamePosthog } from 'types/enums/CustomPosthogEvents';
import { cn } from 'utils/shadcn';

import { Calendar } from './Calendar';
import { Popover, PopoverContent, PopoverTrigger } from './Popover';

export interface DatePickerProps {
  field: ControllerRenderProps<any>;
  thisFieldAttr?: FieldAttributes;
  highlightDirtyField?: boolean;
  highlightError?: boolean;
  logProperties?: Record<string, any>;
  className?: string;
  calendarClassName?: string;
}

function DatePicker({
  field,
  highlightDirtyField,
  highlightError,
  thisFieldAttr,
  logProperties,
  className,
  calendarClassName,
}: DatePickerProps) {
  const [isCalendarPopoverOpen, setIsCalendarPopoverOpen] = useState(false);

  const textPlaceholder =
    thisFieldAttr?.isReadOnly && !field.value ? '' : 'Pick a date';

  return (
    <Popover
      open={isCalendarPopoverOpen}
      onOpenChange={(open) => setIsCalendarPopoverOpen(open)}
    >
      <PopoverTrigger asChild>
        <Button
          variant='outline'
          size='sm'
          className={cn(
            'w-full justify-start text-left font-normal rounded-[4px] border-neutral-input-border bg-neutral-input-bg text-neutral-input-text py-1 h-8',
            'disabled:cursor-not-allowed disabled:bg-neutral-input-disabled-bg disabled:border-neutral-input-disabled-border disabled:opacity-100',
            !field.value && 'text-neutral-400',
            highlightDirtyField && 'bg-warning-50',
            highlightError && 'bg-error-50',
            className
          )}
          buttonNamePosthog={ButtonNamePosthog.ToggleDatePicker}
          logProperties={logProperties}
          disabled={thisFieldAttr?.isReadOnly}
        >
          <CalendarIcon
            className={cn(
              'mr-2 h-4 w-4 shrink-0 stroke-neutral-500',
              calendarClassName
            )}
          />
          <span className='text-[13px] text-neutral-500'>
            {field.value
              ? dayjs(field.value as Date).format('MM/DD/YY')
              : textPlaceholder}
          </span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className='w-auto p-0'>
        <Calendar
          mode='single'
          selected={
            field.value
              ? dayjs.isDayjs(field.value)
                ? field.value.toDate()
                : new Date(field.value as string)
              : undefined
          }
          onSelect={(newValue: any) => {
            if (typeof newValue === 'undefined') {
              field.onChange(null);
              return;
            }

            if (field.value && newValue) {
              newValue.setHours(new Date(field.value as string).getHours());
              newValue.setMinutes(new Date(field.value as string).getMinutes());
            }

            field.onChange(newValue);
            setIsCalendarPopoverOpen(false);
          }}
        />
      </PopoverContent>
    </Popover>
  );
}

export { DatePicker };
