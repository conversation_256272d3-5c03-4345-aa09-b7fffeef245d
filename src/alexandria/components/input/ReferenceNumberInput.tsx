import React, { useEffect, useMemo, useRef, useState } from 'react';
import { UseControllerProps, useController } from 'react-hook-form';

import { ChevronDown } from 'lucide-react';

import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from 'components/Command';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from 'components/Dropdown';
import { Label } from 'components/Label';
import { Input } from 'components/input';
import { Flex } from 'components/layout';
import { Typography } from 'components/typography';
import { NormalizedLoad } from 'types/Load';
import { cn } from 'utils/shadcn';

export interface ReferenceOption {
  value: string;
  label: string;
  source: string;
}

interface ReferenceNumberInputProps<T extends Record<string, any>>
  extends UseControllerProps<T> {
  label: string;
  placeholder?: string;
  required?: boolean;
  description?: string;
  load: NormalizedLoad;
  className?: string;
  // Custom function to generate options, if not provided will use default logic
  generateOptions?: (load: NormalizedLoad) => ReferenceOption[];
}

export function ReferenceNumberInput<T extends Record<string, any>>({
  name,
  control,
  rules,
  label,
  placeholder = 'Enter reference number',
  required = false,
  description,
  load,
  className,
  generateOptions,
}: ReferenceNumberInputProps<T>) {
  const {
    field: { value, onChange, onBlur },
    fieldState: { error },
  } = useController({
    name,
    control,
    rules: {
      ...rules,
      ...(required && { required: 'This field is required' }),
    },
  });

  const [isOpen, setIsOpen] = useState(false);
  const [inputValue, setInputValue] = useState(value || '');

  const inputContainerRef = useRef<HTMLDivElement>(null);
  const [dropdownWidth, setDropdownWidth] = useState<number | undefined>(
    undefined
  );

  useEffect(() => {
    if (inputContainerRef.current) {
      setDropdownWidth(inputContainerRef.current.offsetWidth);
    }
  }, []);

  // Sync input value when form value changes
  useEffect(() => {
    setInputValue(value || '');
  }, [value]);

  // Generate reference options from load data
  const referenceOptions = useMemo(() => {
    if (generateOptions) {
      return generateOptions(load);
    }

    // Default option generation logic
    const options: ReferenceOption[] = [];
    const seenValues = new Set<string>();

    // Add TMS ID (PRO Number/Freight Tracking ID)
    if (load?.freightTrackingID) {
      const freightId = load.freightTrackingID;
      if (!seenValues.has(freightId)) {
        seenValues.add(freightId);
        options.push({
          value: freightId,
          label: `TMS ID: ${freightId}`,
          source: 'freightTrackingID',
        });
      }
    }

    // Add pickup refNumber (remove "PO " prefix if present)
    if (load?.pickup?.refNumber) {
      const pickupRef = load.pickup.refNumber.replace(/^PO\s*/i, '');
      if (pickupRef && !seenValues.has(pickupRef)) {
        seenValues.add(pickupRef);
        options.push({
          value: pickupRef,
          label: `Pickup Ref: ${pickupRef}`,
          source: 'pickup',
        });
      }
    }

    // Add consignee refNumber
    if (load?.consignee?.refNumber) {
      const consigneeRef = load.consignee.refNumber;
      if (consigneeRef && !seenValues.has(consigneeRef)) {
        seenValues.add(consigneeRef);
        options.push({
          value: consigneeRef,
          label: `Consignee Ref: ${consigneeRef}`,
          source: 'consignee',
        });
      }
    }

    // Add customer refNumber
    if (load?.customer?.refNumber) {
      const customerRef = load.customer.refNumber;
      if (customerRef && !seenValues.has(customerRef)) {
        seenValues.add(customerRef);
        options.push({
          value: customerRef,
          label: `Customer Ref: ${customerRef}`,
          source: 'customer',
        });
      }
    }

    // Add poNums split by comma
    if (load?.poNums) {
      const poNumbers = load.poNums
        .split(',')
        .map((po) => po.trim())
        .filter((po) => po);
      poNumbers.forEach((po) => {
        if (po && !seenValues.has(po)) {
          seenValues.add(po);
          options.push({
            value: po,
            label: `PO Number: ${po}`,
            source: 'poNums',
          });
        }
      });
    }

    return options;
  }, [load, generateOptions]);

  const handleSelectOption = (optionValue: string) => {
    onChange(optionValue);
    setInputValue(optionValue);
    setIsOpen(false);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    onChange(newValue);
  };

  const handleInputBlur = () => {
    // Ensure the form value is updated when input loses focus
    onChange(inputValue);
    onBlur();
  };

  const toggleDropdown = () => {
    if (referenceOptions.length > 0) {
      setIsOpen(!isOpen);
    }
  };

  return (
    <div className={cn('space-y-2 w-full', className)}>
      <Label name={name as string} required={required} className='mb-1'>
        {label}
      </Label>
      {description && (
        <Typography variant='body-xs' className='text-neutral-500'>
          {description}
        </Typography>
      )}
      <div className='relative' ref={inputContainerRef}>
        <Input
          value={inputValue}
          onChange={handleInputChange}
          onBlur={handleInputBlur}
          placeholder={placeholder}
          className={cn(
            'pr-10 w-full',
            error && 'border-error-500 focus:border-error-500'
          )}
        />
        {referenceOptions.length > 0 && (
          <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
            <DropdownMenuTrigger asChild>
              <button
                type='button'
                className={cn(
                  'absolute right-0 top-0 h-full px-3 flex items-center justify-center border-l transition-colors',
                  isOpen ? 'bg-neutral-200' : 'hover:bg-neutral-100'
                )}
                onClick={toggleDropdown}
                title='Toggle reference options'
              >
                <ChevronDown
                  className={cn(
                    'h-4 w-4 text-neutral-500 transition-transform',
                    isOpen && 'rotate-180'
                  )}
                />
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align='end'
              style={{ width: dropdownWidth ? `${dropdownWidth}px` : 'auto' }}
              className='w-full px-0 py-0.5 z-[9999] bg-white border border-neutral-300 shadow-lg rounded-md overflow-hidden'
              sideOffset={2}
              side='bottom'
            >
              <Command className='rounded-lg'>
                <CommandInput
                  placeholder='Search and select options...'
                  className='border-0 focus:ring-0 rounded-t-lg h-10 text-xs'
                />
                <CommandList className='max-h-64'>
                  <CommandEmpty className='py-6 text-center text-sm text-neutral-500'>
                    {referenceOptions.length === 0
                      ? 'No reference options available'
                      : 'No reference options found.'}
                  </CommandEmpty>
                  <CommandGroup>
                    {referenceOptions.map((option) => (
                      <CommandItem
                        key={`${option.source}-${option.value}`}
                        value={option.value}
                        onSelect={() => handleSelectOption(option.value)}
                        className='cursor-pointer hover:bg-neutral-100 transition-colors mx-0.5 rounded'
                      >
                        <Flex
                          direction='col'
                          gap='none'
                          className='py-1 px-1 w-full'
                        >
                          <Typography
                            weight='medium'
                            className='text-neutral-900'
                            variant='body-sm'
                          >
                            {option.value}
                          </Typography>
                          <Typography
                            variant='body-xs'
                            className='text-neutral-500 mt-0.5 leading-tight'
                          >
                            {option.label}
                          </Typography>
                        </Flex>
                      </CommandItem>
                    ))}
                  </CommandGroup>
                </CommandList>
              </Command>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
      {error && (
        <Typography variant='body-xs' className='text-error-500'>
          {error.message}
        </Typography>
      )}
    </div>
  );
}
