import React from 'react';

import { Slot } from '@radix-ui/react-slot';
import { type VariantProps, cva } from 'class-variance-authority';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore posthog is in the parent dir
import { usePostHog } from 'posthog-js/react';

import { Maybe } from 'types/UtilityTypes';
import { cn } from 'utils/shadcn';

const buttonVariants = cva(
  'inline-flex items-center justify-center whitespace-nowrap rounded-[4px] text-base font-medium cursor-pointer transition-colors [&>svg]:transition-colors disabled:pointer-events-none disabled:opacity-50',
  {
    variants: {
      variant: {
        default: 'bg-brand-main text-neutral-50 text-md hover:bg-brand-600',
        secondary:
          'border bg-neutral-50 text-neutral-900 text-md hover:border-brand hover:bg-brand-50 hover:text-brand hover:[&>svg]:!stroke-brand',
        warning: 'bg-warning-main text-neutral-50 text-md hover:bg-warning-600',
        outline:
          'border bg-neutral-50 text-neutral-900 border-neutral-500 hover:bg-neutral-100 hover:border-neutral-600 hover:border-neutral-700',
        ghost: 'border border-transparent hover:border-brand-main',
        underline: 'border-b border-brand-700 rounded-none',
        titlebarActionIcon:
          'hover:border-0 hover:bg-brand hover:[&>svg]:!stroke-white',
        destructive:
          'border border-error-500 text-error-500 hover:bg-error-500 hover:text-neutral-50',
      },
      size: {
        default: 'h-11 px-4 py-2',
        xs: 'h-7 w-7 px-1',
        sm: 'h-9 px-3',
        lg: 'h-13 px-8',
        icon: 'h-10 min-h-10 w-10',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

// TODO: add prop to turn button to link - href?: string;
interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  buttonNamePosthog: Maybe<string>;
  logProperties?: { [key: string]: any };
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      buttonNamePosthog,
      logProperties,
      onClick,
      variant,
      size,
      asChild = false,
      ...props
    },
    ref
  ) => {
    const posthog = usePostHog();

    const handleOnClick = (
      e: React.MouseEvent<HTMLButtonElement, MouseEvent>
    ) => {
      if (buttonNamePosthog) {
        posthog?.capture(buttonNamePosthog, { ...logProperties });
      }

      onClick && onClick(e);
    };

    const Comp = asChild ? Slot : 'button';
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        onClick={handleOnClick}
        {...props}
      />
    );
  }
);
Button.displayName = 'Button';

export { Button, buttonVariants };
