import { isAxiosError } from 'axios';
import { Result, err, ok } from 'neverthrow';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import axios from '@utils/axios';

import { ApiError } from 'types/api/ApiError';
import captureException from 'utils/captureException';

type GetDATLaneHistoryParams = {
  transportType: string;
  originCity: string;
  originState: string;
  destinationCity: string;
  destinationState: string;
};

type PriceRange = {
  rateUsd: number;
  highUsd: number;
  lowUsd: number;
};

export type DATLaneHistoryEntry = {
  year: number;
  month: number;
  perMile: PriceRange;
  averageFuelSurchargePerMileUsd: number;
  perTrip: PriceRange;
  averageFuelSurchargePerTripUsd: number;
};

export type DATLaneHistoryResponse = {
  rates: DATLaneHistoryEntry[];
};

export default async function getDATLaneHistory(
  request: GetDATLaneHistoryParams
): Promise<Result<DATLaneHistoryResponse, ApiError>> {
  try {
    const queryData = buildDATLaneHistoryQuery(request);

    const response = await axios.get<DATLaneHistoryResponse>(
      `quote/private/lane-history/dat?${queryData}`
    );

    return ok(response.data);
  } catch (error) {
    captureException(error, { functionName: 'getDATLaneHistory' });

    if (!isAxiosError(error)) {
      return err({ message: 'Failed to get DAT lane history' });
    }

    return err({
      message: error.response?.data || 'Failed to get DAT lane history',
    });
  }
}

const buildDATLaneHistoryQuery = ({
  originCity,
  originState,
  destinationCity,
  destinationState,
  transportType,
}: GetDATLaneHistoryParams): string => {
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth() + 1; // 0-indexed

  let oneMonthAgoMonth = currentMonth - 1;
  let oneMonthAgoYear = currentYear;

  if (oneMonthAgoMonth < 1) {
    oneMonthAgoYear--;
    oneMonthAgoMonth = 12 + oneMonthAgoMonth;
  }

  let fiveMonthsAgoMonth = currentMonth - 5;
  let fiveMonthsAgoYear = currentYear;

  if (fiveMonthsAgoMonth < 1) {
    fiveMonthsAgoYear--;
    fiveMonthsAgoMonth = 12 + fiveMonthsAgoMonth;
  }

  const params = {
    originCity,
    originState,
    destinationCity,
    destinationState,
    rateType: 'SPOT',
    equipment: transportType,
    fromYear: fiveMonthsAgoYear.toString(),
    fromMonth: fiveMonthsAgoMonth.toString(),
    toYear: oneMonthAgoYear.toString(),
    toMonth: oneMonthAgoMonth.toString(),
  };

  return new URLSearchParams(params).toString();
};
