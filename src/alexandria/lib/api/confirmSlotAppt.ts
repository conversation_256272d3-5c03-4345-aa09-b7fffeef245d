import { isAxiosError } from 'axios';
import { Result, err, ok } from 'neverthrow';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import axios from '@utils/axios';

import { SearchOption } from 'constants/SearchOptions';
import {
  CustomApptFieldsTemplate,
  SchedulingPortals,
  StopTypes,
} from 'types/Appointment';
import { ApiError } from 'types/api/ApiError';
import { ConfirmSlotApptResult } from 'types/api/ConfirmSlotAppt';
import captureException from 'utils/captureException';

import { AppointmentFile, uploadSingleFile } from './uploadApptFile';

interface ConfirmSlotApptProps {
  source?: SchedulingPortals;
  isTMSLoad: boolean;
  stopType: StopTypes;
  start: Date;
  loadTypeId: string;
  warehouseID: string;
  warehouseTimezone: string;
  dockId: string;
  loadID: number;
  freightTrackingId: string;
  poNums?: string;
  refNumber?: string; // Customer-specific reference number, may be 1) FreightTrackingID, 2) PO nums, 3) Load.Consignee.RefNumber
  apptKey?: string;
  carrierScac?: string;
  weight?: string;
  trailerType?: string; // Required for 7/11 VA, optional for others
  customApptFieldsTemplate?: CustomApptFieldsTemplate[];
  subscribedEmail?: string;
  notes?: string;
  trailerId?: string;
  schedulePid?: string;
  requestType?: StopTypes;
  email?: string;
  phone?: string;
  appointments?: {
    start: Date;
    freightTrackingId: string;
    zipCode?: string;
    city?: string;
    state?: string;
    country?: string;
    searchBy?: SearchOption;
    // Manhattan-specific fields
    facilityId?: string;
    facilityText?: string;
    appointmentType?: string;
    // Costco-specific fields
    proNumber?: string;
    doorType?: string;
    unloadType?: string;
    commodity?: string;
    containerNumber?: string;
    linkLoadId?: string;
    notes?: string;
    uom?: string;
    qtyCount?: number;
    depotValue?: string;
  }[];
  operation?: string;
  appointmentType?: string;
  company?: string;
  // Manhattan-specific fields
  facilityId?: string;
  facilityText?: string;
  integrationId?: number;

  documentFiles?: AppointmentFile[];
}

export async function confirmSlotAppt(
  props: ConfirmSlotApptProps
): Promise<Result<ConfirmSlotApptResult, ApiError>> {
  // Handle making appt with file uploads if files are provided and source is Opendock
  const fileUploadResult = await handleFileUploads(props);
  if (fileUploadResult !== null) {
    return fileUploadResult;
  }

  // Standard JSON request (no file upload)
  const { documentFiles: _, ...requestProps } = props;
  const requestBody = {
    dryRun: false,
    ...requestProps,
  };

  // Exists on both forms, but not required for non-load
  if (props.freightTrackingId) {
    Object.assign(requestBody, {
      freightTrackingId: props.freightTrackingId,
    });
  }

  // if we have a trailer type from default form, we're working with 7-Eleven's load info case
  if (props.trailerType) {
    Object.assign(requestBody, {
      trailerType: props.trailerType,
    });
  }

  try {
    const response = await axios.post<ConfirmSlotApptResult>(
      '/appt',
      requestBody
    );
    return ok(response.data);
  } catch (error) {
    captureException(error, { functionName: 'confirmSlotAppt' });

    if (!isAxiosError(error)) {
      return err({ message: 'Failed to set appointment' });
    }

    if (error && isAxiosError(error) && error.response?.status === 409) {
      return err({ message: 'Conflicting Appointments' });
    }

    if (error && isAxiosError(error) && error.response?.status === 401) {
      throw error;
    }

    if (error && error.message === 'Extension context invalidated.') {
      throw error;
    }

    return err({
      ID: error.response?.data.ID,
      ExternalID: error.response?.data.ExternalID,
      ConfirmationNo: error.response?.data.ConfirmationNo,
      message: error.response?.data.message || 'Failed to set appointment',
    });
  }
}

/**
 * Helper function to handle file uploads for Opendock appointments
 * Only processes uploads when documentFiles exist and source is Opendock
 * @param props - The appointment confirmation properties
 * @returns Promise<Result> if files need to be uploaded, null if no upload needed
 */
async function handleFileUploads(
  props: ConfirmSlotApptProps
): Promise<Result<ConfirmSlotApptResult, ApiError> | null> {
  const files = props.documentFiles || [];
  const shouldUploadFiles =
    files.length > 0 && props.source === SchedulingPortals.Opendock;

  if (!shouldUploadFiles) {
    return null;
  }

  const uploadedKeys: string[] = [];

  // Validate and upload each file
  for (const file of files) {
    if (file.size > 6 * 1024 * 1024) {
      return err({
        message: `File ${file.fileName} exceeds 6MB limit. Please use a smaller file.`,
      });
    }

    const uploadResult = await uploadSingleFile(file);
    if (!uploadResult.isOk()) {
      return uploadResult as any;
    }

    uploadedKeys.push(uploadResult.value.key);
  }

  // Prepare request body with uploaded file keys
  const { documentFiles: _, ...requestProps } = props;
  const requestBody = {
    dryRun: false,
    ...requestProps,
    start: props.start.toISOString(),
    uploadedFileKeys: uploadedKeys,
  };

  delete (requestBody as any).documentFiles;

  try {
    const response = await axios.post<ConfirmSlotApptResult>(
      '/appt',
      requestBody
    );
    return ok(response.data);
  } catch (error) {
    captureException(error, {
      functionName: 'confirmSlotAppt (file uploads)',
    });

    if (!isAxiosError(error)) {
      return err({
        message: 'Failed to create appointment with file uploads',
      });
    }

    if (error && isAxiosError(error) && error.response?.status === 409) {
      return err({ message: 'Conflicting Appointments' });
    }

    if (error && isAxiosError(error) && error.response?.status === 401) {
      throw error;
    }

    return err({
      message: error.response?.data?.error || error.message,
    });
  }
}
