import { isAxiosError } from 'axios';
import { Result, err, ok } from 'neverthrow';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import axios from '@utils/axios';

import {
  CarrierQuoteConfig,
  IntegrationCore,
  QuickQuoteConfig,
  SchedulerConfig,
  Service,
  ServiceFeatureConfigurations,
  ServiceFeaturesListType,
} from 'contexts/serviceContext';
import { Maybe } from 'types/UtilityTypes';
import { ApiError } from 'types/api/ApiError';
import captureException from 'utils/captureException';

type FetchServiceFeaturesResponse = {
  id: number;
  tmsIntegrations: IntegrationCore[];
  quotingIntegrations: IntegrationCore[];
  schedulerIntegrations: IntegrationCore[];
  configurations: ServiceFeatureConfigurations;
  quickQuoteConfig: QuickQuoteConfig;
  carrierQuoteConfig?: Maybe<CarrierQuoteConfig>;
  schedulerConfig?: SchedulerConfig;
  scac?: Maybe<string>;
} & ServiceFeaturesListType;

export default async function fetchServiceFeatures(
  serviceID: number
): Promise<Result<FetchServiceFeaturesResponse, ApiError>> {
  try {
    const response = await axios.get<FetchServiceFeaturesResponse>(
      `service/${encodeURIComponent(serviceID)}/features`
    );

    return ok(response.data);
  } catch (error) {
    captureException(error, { functionName: 'fetchServiceFeatures' });

    if (!isAxiosError(error)) {
      return err({ message: 'Failed to fetch service features' });
    }

    if (error.response?.status === 404) {
      return err({ message: 'Service not found' });
    }

    return err({
      message:
        error.response?.data?.message || 'Failed to fetch service features',
    });
  }
}

export const convertServiceDataToService = (
  serviceData: FetchServiceFeaturesResponse
): Service => {
  const {
    id,
    tmsIntegrations,
    quotingIntegrations,
    schedulerIntegrations,
    configurations,
    quickQuoteConfig,
    carrierQuoteConfig,
    schedulerConfig,
    ...features
  } = serviceData;

  return {
    serviceID: id,
    tmsIntegrations,
    quotingIntegrations,
    schedulerIntegrations,
    configurations,
    quickQuoteConfig,
    carrierQuoteConfig,
    schedulerConfig,
    serviceFeaturesEnabled: features,
  } as Service;
};
