import { isAxiosError } from 'axios';
import dayjs, { Dayjs } from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import { Result, err, ok } from 'neverthrow';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import axios from '@utils/axios';

import { SearchOption } from 'constants/SearchOptions';
import {
  GetSlotsResponse,
  GroupedSlot,
  OrderedSlots,
  Slot,
  StopTypes,
} from 'types/Appointment';
import { SchedulingPortals } from 'types/Appointment';
import { ApiError } from 'types/api/ApiError';
import captureException from 'utils/captureException';

dayjs.extend(timezone);

// for retalix
interface WarehouseDetails {
  warehouseID: string;
  warehouseName: string;
}

interface BaseGetOpenSlotsProps {
  // TODO: move this to GetOpenSlotsOpendock after updating the backend
  freightTrackingID?: string;
  loadTypeID: string;
  warehouseID?: string;
  warehouseTimezoneStartDate?: dayjs.Dayjs;
  warehouseTimezoneEndDate?: dayjs.Dayjs;
  operation?: string;
  appointmentType?: string;
  company?: string;
  // Manhattan-specific fields
  facilityId?: string;
  facilityText?: string;
  zipCode?: string;
  city?: string;
  state?: string;
  country?: string;
}

interface GetOpenSlotsE2open {
  source: SchedulingPortals.E2open;
  startDateTime?: dayjs.Dayjs;
  endDateTime?: dayjs.Dayjs;
  requestType: StopTypes;
}

interface GetOpenSlotsOneNetwork {
  source: SchedulingPortals.OneNetwork;
  startDateTime?: dayjs.Dayjs;
  endDateTime?: dayjs.Dayjs;
  requestType: StopTypes;
  state?: string;
  city?: string;
  zipCode?: string;
  searchBy?: SearchOption;
}

interface GetOpenSlotsManhattan {
  source: SchedulingPortals.Manhattan;
  startDateTime?: dayjs.Dayjs;
  endDateTime?: dayjs.Dayjs;
  requestType: StopTypes;
}

interface GetOpenSlotsOpendock {
  source: SchedulingPortals.Opendock;
  trailerType?: string;
  dockId?: string;
}

interface GetOpenSlotsRetalix {
  source: SchedulingPortals.Retalix;
  warehouse: WarehouseDetails;
  poNumbers: string[];
}

interface GetOpenSlotsYardView {
  source: SchedulingPortals.YardView;
  filterType?: string;
  startDateTime?: dayjs.Dayjs;
  endDateTime?: dayjs.Dayjs;
}

type GetOpenSlotsProps = BaseGetOpenSlotsProps &
  (
    | GetOpenSlotsE2open
    | GetOpenSlotsManhattan
    | GetOpenSlotsOneNetwork
    | GetOpenSlotsOpendock
    | GetOpenSlotsRetalix
    | GetOpenSlotsYardView
  );

export async function getOpenApptSlots(
  props: GetOpenSlotsProps
): Promise<Result<OrderedSlots, ApiError>> {
  try {
    const base = '/appt/slots';
    const params = buildCommonParams(props);

    switch (props.source) {
      case SchedulingPortals.E2open:
        params.requestType = props.requestType;

        if (!props.startDateTime && !props.endDateTime) {
          break;
        }

        if (!props.endDateTime) {
          return err({
            message: 'End date is required when start date is provided',
          });
        }

        params.end = props.endDateTime.format();
        params.start = props.startDateTime?.format() ?? dayjs().format();

        if (params.start > params.end) {
          return err({
            message: 'Start date must be before end date',
          });
        }
        break;
      case SchedulingPortals.Manhattan:
        params.requestType = props.requestType;

        // Validate Manhattan-specific required fields
        if (!props.facilityId) {
          return err({
            message: 'Facility ID is required for Manhattan',
          });
        }
        if (!props.facilityText) {
          return err({
            message: 'Facility text is required for Manhattan',
          });
        }
        if (!props.appointmentType) {
          return err({
            message: 'Appointment type is required for Manhattan',
          });
        }

        if (!props.startDateTime && !props.endDateTime) {
          break;
        }

        if (!props.endDateTime) {
          return err({
            message: 'End date is required when start date is provided',
          });
        }

        params.end = props.endDateTime.format();
        params.start = props.startDateTime?.format() ?? dayjs().format();

        if (params.start > params.end) {
          return err({
            message: 'Start date must be before end date',
          });
        }
        break;

      case SchedulingPortals.OneNetwork:
        if (props.warehouseID) {
          params.warehouseID = props.warehouseID;
        }

        if (props.state) {
          params.state = props.state;
        }

        if (props.city) {
          params.city = props.city;
        }

        if (props.zipCode) {
          params.zipCode = props.zipCode;
        }

        if (props.searchBy) {
          params.searchBy = props.searchBy;
        }

        if (!props.startDateTime && !props.endDateTime) {
          break;
        }

        if (!props.endDateTime) {
          return err({
            message: 'End date is required when start date is provided',
          });
        }

        params.end = props.endDateTime.format();
        params.start = props.startDateTime?.format() ?? dayjs().format();

        if (params.start > params.end) {
          return err({
            message: 'Start date must be before end date',
          });
        }
        break;

      case SchedulingPortals.Opendock:
        if (props.warehouseID) {
          params.warehouseID = props.warehouseID;
        }
        if (props.freightTrackingID) {
          params.freightTrackingID = props.freightTrackingID;
        }
        if (props.trailerType) {
          params.trailerType = props.trailerType;
        }
        if (props.dockId) {
          params.dockID = props.dockId;
        }
        break;

      case SchedulingPortals.Retalix:
        if (!props.warehouse || !props.poNumbers?.length) {
          throw new Error(
            'Warehouse details and PO numbers are required for Retalix'
          );
        }
        params['warehouse.warehouseID'] = props.warehouse.warehouseID;
        params['warehouse.warehouseName'] = props.warehouse.warehouseName;
        params.poNumbers = JSON.stringify(props.poNumbers);
        break;

      case SchedulingPortals.YardView:
        if (props.warehouseID) {
          params.warehouseID = props.warehouseID;
        }

        if (props.filterType) {
          params.filterType = props.filterType;
        }

        if (!props.startDateTime && !props.endDateTime) {
          break;
        }

        if (!props.endDateTime) {
          return err({
            message: 'End date is required when start date is provided',
          });
        }

        params.start = props.startDateTime?.format() ?? dayjs().format();
        params.end = props.endDateTime.format();

        if (params.start > params.end) {
          return err({
            message: 'Start date must be before end date',
          });
        }
    }

    const url = `${base}?${buildQueryString(params)}`;

    const response = await axios.get(url);

    switch (props.source) {
      case SchedulingPortals.E2open:
      case SchedulingPortals.Manhattan:
      case SchedulingPortals.OneNetwork:
      case SchedulingPortals.Retalix:
      case SchedulingPortals.YardView: {
        if (!response.data || !response.data.slots) {
          return err({ message: 'Invalid response format' });
        }
        const transformedResponse: GetSlotsResponse = normalizeSlotResponse(
          response.data
        );
        return ok(groupSlotsByDate(transformedResponse, props.source));
      }

      default:
        // For Opendock, use response directly
        return ok(groupSlotsByDate(response.data, props.source));
    }
  } catch (error) {
    captureException(error, { functionName: 'getOpenApptSlots' });

    if (!isAxiosError(error)) {
      return err({ message: 'Oops, something went wrong!' });
    }

    if (error && isAxiosError(error) && error.response?.status === 401) {
      throw error;
    }

    if (error && error.message === 'Extension context invalidated.') {
      throw error;
    }

    return err({
      message: error.response?.data.message || 'Oops, something went wrong!',
    });
  }
}

type QueryParams = Record<string, string>;

function buildCommonParams(props: GetOpenSlotsProps): QueryParams {
  const params: QueryParams = {
    loadTypeID: props.loadTypeID,
    source: props.source,
    zipCode: props.zipCode || '',
  };

  if (props.freightTrackingID) {
    params.freightTrackingID = props.freightTrackingID;
  }

  if (props.warehouseTimezoneStartDate) {
    params.start = props.warehouseTimezoneStartDate.format();
  }

  if (props.warehouseTimezoneEndDate) {
    params.end = props.warehouseTimezoneEndDate.format();
  }

  if (props.appointmentType) {
    params.appointmentType = props.appointmentType || '';
  }
  if (props.operation) {
    params.operation = props.operation || '';
  }
  if (props.company) {
    params.company = props.company || '';
  }
  if (props.city) {
    params.city = props.city || '';
  }
  if (props.state) {
    params.state = props.state || '';
  }
  if (props.country) {
    params.country = props.country || '';
  }

  // Manhattan-specific fields
  if (props.facilityId) {
    params.facilityId = props.facilityId;
  }
  if (props.facilityText) {
    params.facilityText = props.facilityText;
  }

  return params;
}

function buildQueryString(params: QueryParams): string {
  return Object.entries(params)
    .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
    .join('&');
}

function groupSlotsByDate(
  resp: GetSlotsResponse,
  source: SchedulingPortals
): OrderedSlots {
  const grouped: Record<string, GroupedSlot[]> = {};
  let warehouseTimezone = '';

  resp.slots.forEach((slot: Slot) => {
    if (warehouseTimezone === '') {
      warehouseTimezone = slot.warehouse.warehouseTimezone;
    }

    slot.startTimes.forEach((startTime: string) => {
      // Skip timezone conversion for E2open when timezone is empty
      const startTimeWithTimezone =
        source === SchedulingPortals.E2open && !slot.warehouse.warehouseTimezone
          ? dayjs.utc(startTime)
          : dayjs(startTime).tz(slot.warehouse.warehouseTimezone);

      const date = formatDateToMMDD(
        startTimeWithTimezone,
        source,
        slot.warehouse.warehouseTimezone
      );

      if (!grouped[date]) {
        grouped[date] = [];
      }

      const hasSlotOnDateTime = grouped[date].some(
        (s) =>
          startTimeWithTimezone.toDate().toISOString() ===
          s.startTime.toISOString()
      );

      if (hasSlotOnDateTime) {
        return;
      }

      grouped[date].push({
        dock: slot.dock,
        startTime: startTimeWithTimezone.toDate(),
        timezone: slot.warehouse.warehouseTimezone,
      });
    });
  });

  for (const date in grouped) {
    grouped[date].sort((a: GroupedSlot, b: GroupedSlot) => {
      return dayjs(a.startTime).diff(dayjs(b.startTime));
    });
  }

  return {
    start: resp.start,
    end: resp.end,
    warehouse: resp.warehouse,
    warehouseTimezone: warehouseTimezone,
    loadType: resp.loadType,
    trailerType: resp.trailerType,
    slots: grouped,
  };
}

function formatDateToMMDD(
  date: Dayjs,
  source: SchedulingPortals,
  warehouseTimezone: string
): string {
  const dateToFormat =
    source === SchedulingPortals.E2open && !warehouseTimezone
      ? date.utc()
      : date;
  const formattedDate = dateToFormat.format('M/D/YY');
  const dayOfWeek = dateToFormat.format('ddd');

  return `${dayOfWeek}, ${formattedDate}`;
}

function normalizeSlotResponse(data: any): GetSlotsResponse {
  return {
    trailerType: data.trailerType || '',
    start: data.start,
    end: data.end,
    loadType: {
      id: data.loadType.id || '',
      name: data.loadType.name || '',
      direction: data.loadType.direction || '',
    },
    warehouse: {
      warehouseID: data.warehouse.warehouseID,
      company_name: data.warehouse.warehouseName,
      location_name: data.warehouse.warehouseFullAddress,
      warehouseAddressLine1: data.warehouse.warehouseAddressLine1,
      warehouseAddressLine2: data.warehouse.warehouseAddressLine2,
      city: data.warehouse.warehouseCity || '',
      state: data.warehouse.warehouseState || '',
      zip: '',
      contact_email: data.warehouse.defaultSubscribedEmail || '',
      contact_phone: '',
    },
    slots: data.slots.map((slot: any) => ({
      dock: {
        id: slot.dock.id || '',
        name: slot.dock.name || '',
        warehouseId: slot.dock.warehouseId || '',
        orgId: '', // Required by interface
        loadTypeIds: slot.dock.loadTypeIds || [],
      },
      availability: [], // Required by interface
      startTimes: slot.startTimes,
      warehouse: {
        id: slot.warehouse.warehouseID,
        name: slot.warehouse.warehouseName,
        orgId: '', // Required by interface
        warehouseTimezone: slot.warehouse.warehouseTimezone || 'UTC',
        email: slot.warehouse.defaultSubscribedEmail || '',
      },
      timezone: slot.warehouse.warehouseTimezone || 'UTC',
    })),
  };
}
