import { QuotingPortal } from 'lib/hosts/quoting/interface';
import { Undef } from 'types/UtilityTypes';
import captureException from 'utils/captureException';

export const ShipwellSubmitAction = 'shipwell-submit-quote';

export class Shipwell implements QuotingPortal {
  submitAction = ShipwellSubmitAction;

  /**
   * Determines if a quote can be submitted to Shipwell based on the current tab and document state.
   * @param tab The current Chrome tab
   * @param html The current document HTML
   * @returns boolean indicating if a quote can be submitted
   */
  canSubmit(tab: Undef<chrome.tabs.Tab>, html: string): boolean {
    if (!tab?.url) return false;
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');

    try {
      const url = new URL(tab.url);
      const isShipwell = url.origin?.includes('app.shipwell.com');

      if (!isShipwell) return false;

      // Check if we're on a load detail page (e.g., /load-board/LIDBAAZCD)
      const isLoadDetailPage =
        url.pathname?.includes('load-board/') &&
        url.pathname.split('/').length >= 3;
      if (!isLoadDetailPage) return false;

      // Check if there's load data and the Quick Quote form is available
      const hasQuickQuoteForm = this.hasQuickQuoteForm(doc);
      const hasLoadData = this.hasLoadData(doc);

      return hasQuickQuoteForm && hasLoadData;
    } catch (error: any) {
      captureException('Error checking Shipwell quote page:', error);
      return false;
    }
  }

  /**
   * Checks if the Quick Quote form is present on the page
   */
  private hasQuickQuoteForm(doc: Document): boolean {
    // Look for the Quick Quote panel elements
    const quickQuoteButton =
      doc.querySelector('button:contains("Get Quick Quote")') ||
      doc.querySelector('[data-testid*="quick-quote"]') ||
      doc.querySelector('.quick-quote-button') ||
      doc.querySelector('button[type="submit"]');

    return quickQuoteButton !== null;
  }

  /**
   * Checks if there's load data available on the page (for load detail pages)
   */
  private hasLoadData(doc: Document): boolean {
    // Look for dashboard summary elements that contain pickup/delivery data
    const dashboardSummary =
      doc.querySelector('.dashboard-summary__stop-address') ||
      doc.querySelector('[class*="dashboard-summary"]') ||
      doc.querySelector('[class*="stop-address"]');

    return dashboardSummary !== null;
  }
}
