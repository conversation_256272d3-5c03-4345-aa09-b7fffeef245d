import { AvailableTabs } from 'constants/SidebarTabs';
import { Dropoff, NormalizedLoad, Pickup } from 'types/Load';
import { Maybe } from 'types/UtilityTypes';

import { HostInterface } from './interface';

export class Tai implements HostInterface {
  getFieldMoniker(key: keyof NormalizedLoad): string {
    switch (key) {
      case 'externalTMSID':
        return '';
      case 'freightTrackingID':
        return 'PRO #';
      default:
        return '';
    }
  }

  parseFreightTrackingID(): string {
    return '';
  }

  parseExternalTMSID(): string {
    return '';
  }

  parsePickup(): Maybe<Pickup> {
    return null;
  }

  parseDropoff(): Maybe<Dropoff> {
    return null;
  }

  determineDefaultLoadTab(): AvailableTabs {
    return AvailableTabs.LoadInformation;
  }

  shouldAutomaticallyOpenDrumkit(): boolean {
    return location.href.includes('taicloud.net');
  }
}
