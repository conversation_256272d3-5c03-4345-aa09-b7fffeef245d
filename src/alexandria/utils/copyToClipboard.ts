// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore jodit is in the parent dir
import { IJodit } from 'jodit/esm/types';

import { toast } from 'hooks/useToaster';
import { Maybe } from 'types/UtilityTypes';
import captureException from 'utils/captureException';

export type ClipboardContent = {
  text: string;
  html?: string;
};

export type CopyToClipboardOptions = {
  showToast?: boolean;
  toastMessage?: string;
  toastVariant?: 'default' | 'destructive' | 'success';
  wrapInHTML?: boolean;
  joditEditorRef?: React.RefObject<Maybe<IJodit>>;
};

export const isHTMLContent = (text: string): boolean => {
  const htmlRegex = /<[a-z][\s\S]*>/i;
  return htmlRegex.test(text);
};

// Creates ClipboardContent from HTML string
export const createRichTextContent = (
  html: string,
  fallbackText?: string
): ClipboardContent => {
  return {
    text: fallbackText || html.replace(/<[^>]*>/g, ''), // Strip HTML tags for text fallback
    html: html,
  };
};

export const copyToClipboard = async (
  content: string | ClipboardContent,
  options: CopyToClipboardOptions = {}
): Promise<boolean> => {
  const {
    showToast = true,
    toastMessage = 'Copied to clipboard.',
    toastVariant = 'success',
    wrapInHTML = false,
    joditEditorRef,
  } = options;

  // If a Jodit editor ref is provided, use it to copy the content natively
  if (joditEditorRef && joditEditorRef.current) {
    try {
      copyToClipboardJodit(joditEditorRef.current);

      if (showToast) {
        toast({
          description: toastMessage,
          variant: toastVariant,
        });
      }

      return true;
    } catch (error) {
      console.error('Failed to copy to clipboard using Jodit editor:', error);
    }
  }

  // Normalize content to handle both string and ClipboardContent
  const normalizedContent: ClipboardContent =
    typeof content === 'string' ? { text: content } : content;

  try {
    // Attempt using either the modern Clipboard API or simple plaintext copy based on content type
    if (normalizedContent.html) {
      const htmlContent = wrapInHTML
        ? wrapInHTMLSkeleton(normalizedContent.html)
        : normalizedContent.html;

      const blob = new Blob([htmlContent], { type: 'text/html' });
      const clipboardItem = new ClipboardItem({ 'text/html': blob });
      await navigator.clipboard.write([clipboardItem]);
    } else {
      await navigator.clipboard.writeText(normalizedContent.text);
    }

    if (showToast) {
      toast({
        description: toastMessage,
        variant: toastVariant,
      });
    }

    return true;
  } catch (error) {
    console.error('Modern clipboard API failed:', error);

    return await fallbackCopyToClipboard(normalizedContent, options);
  }
};

// Special handling for cases such as Outlook rich text copying
const fallbackCopyToClipboard = async (
  content: ClipboardContent,
  options: CopyToClipboardOptions
): Promise<boolean> => {
  const {
    showToast = true,
    toastMessage = 'Copied to clipboard.',
    toastVariant = 'success',
  } = options;

  try {
    let tempElement: HTMLDivElement | HTMLTextAreaElement;

    // The temporary element depends on the content type
    // - <div> keeps HTML formatting correctly
    // - <textarea> keeps plain text with no unwanted line breaks
    if (content.html) {
      tempElement = document.createElement('div');
      tempElement.innerHTML = content.html!;
    } else {
      tempElement = document.createElement('textarea');
      tempElement.value = content.text;
    }

    tempElement.style.position = 'fixed';
    tempElement.style.opacity = '0';
    tempElement.style.pointerEvents = 'none';
    document.body.appendChild(tempElement);

    // Create selection range for all HTML content in <div>, or simply select textarea text
    if (content.html) {
      const range = document.createRange();

      range.selectNode(tempElement);
      window.getSelection()?.removeAllRanges();
      window.getSelection()?.addRange(range);
    } else {
      (tempElement as HTMLTextAreaElement).select();
    }

    const successful = document.execCommand('copy');

    window.getSelection()?.removeAllRanges();
    document.body.removeChild(tempElement);

    if (successful && showToast) {
      toast({
        description: toastMessage,
        variant: toastVariant,
      });
    }

    return successful;
  } catch (error) {
    captureException(error, { functionName: 'fallbackCopyToClipboard' });

    if (showToast) {
      toast({
        description: 'Failed to copy to clipboard.',
        variant: 'destructive',
      });
    }

    return false;
  }
};

const wrapInHTMLSkeleton = (content: string) => {
  return `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8">
      </head>
      <body>
        ${content}
      </body>
    </html>
  `;
};

const copyToClipboardJodit = (jodit: IJodit) => {
  jodit.execCommand('selectall');
  jodit.execCommand('copy');
  jodit.execCommand('unselect');
};
