import { Maybe } from 'types/UtilityTypes';

export type ConfirmSlotApptResult = {
  // TODO: Redefine using camelCase
  ID: string; // Drumkit DB ID
  ExternalID: string; // Integration's ID (e.g. Opendock's)
  ConfirmationNo: string; // There may be an additional identifier besides ID (e.g. OpenDock)

  isAppointmentTMSUpdateEnabled: boolean;
  tmsUpdateSucceeded: boolean;
  message: Maybe<string>;
  url: Maybe<string>;
};
