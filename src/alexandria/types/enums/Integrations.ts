// These enums must match those in drumkit/common/models/integration.
export enum TMS {
  Aljex = 'aljex',
  Turvo = 'turvo',
  FreightFlow = 'freightflow',
  Relay = 'relay',
  Tai = 'tai',
  McleodEnterprise = 'mcleodenterprise',
  Mcleod = 'mcleod',
  GlobalTranzTMS = 'globaltranztms',
  ThreeG = 'threeg',
}

export enum Quoting {
  DAT = 'dat',
  Greenscreens = 'greenscreens',
  GlobalTranz = 'globaltranz',
  TruckStop = 'truckstop',
}

export enum QuotingPortals {
  FreightView = 'freightview',
  E2Open = 'e2open-quoting', // Different from TMS integration
  UberFreight = 'uberfreight',
  Shipwell = 'shipwell',
}

export enum CarrierVerification {
  MyCarrierPortal = 'mycarrierportal',
  Highway = 'highway',
  FreightValidate = 'freightvalidate',
  Truckstop = 'truckstop',
}

// Map enums to their human-readable names
export const integrationNameMap: Record<
  TMS | Quoting | QuotingPortals,
  string
> = {
  [Quoting.DAT]: 'DAT',
  [Quoting.Greenscreens]: 'Greenscreens',
  [Quoting.GlobalTranz]: 'GlobalTranz',
  [Quoting.TruckStop]: 'Truckstop',
  [TMS.Aljex]: 'Aljex',
  [TMS.Turvo]: 'Turvo',
  [TMS.ThreeG]: '3G',
  [TMS.FreightFlow]: 'FreightFlow',
  [TMS.Relay]: 'Relay',
  [TMS.Tai]: 'Tai',
  // Workaround to clarify on FE that this data is customer-specific. Trifecta our only GlobalTranzTMS customer at the moment.
  [TMS.GlobalTranzTMS]: 'Trifecta',
  // Typically customers have Mcleod Enterprise or Mcleod Innovation so we can use "Mcleod" for both keep user-facing name concise
  [TMS.McleodEnterprise]: 'McLeod',
  [TMS.Mcleod]: 'McLeod',
  [QuotingPortals.FreightView]: 'FreightView',
  [QuotingPortals.E2Open]: 'E2Open',
  [QuotingPortals.UberFreight]: 'UberFreight',
  [QuotingPortals.Shipwell]: 'Shipwell',
};
