import { useCallback, useEffect, useState } from 'react';

import { ValueType } from 'components/DebounceSelect';
import { IntegrationCore } from 'contexts/serviceContext';
import { getCustomers } from 'lib/api/getCustomers';
import { TMSCustomer } from 'types/Load';
import { Maybe } from 'types/UtilityTypes';
import { TMS } from 'types/enums/Integrations';
import {
  GenericCompanySearchableFields,
  customerSearchHandler,
} from 'utils/loadInfoAndBuilding';

interface UseCustomersResult {
  customers: Maybe<TMSCustomer[]>;
  isLoading: boolean;
  tmsTenant: Maybe<string>;
  refreshCustomers: () => Promise<void>;
  resetCustomerSearch: () => void;
  customerSearch: (
    field: GenericCompanySearchableFields,
    value: string
  ) => Promise<ValueType[]>;
}

interface UseCustomersOptions {
  /** Toast function for displaying notifications. Optional - if not provided, no toasts will be shown */
  toast?: (params: {
    description: string;
    variant: 'success' | 'destructive';
  }) => void;
  /** Suggested customer ID to preserve when resetting search. Optional */
  suggestedCustomerId?: string;
}

// TODO: Move to BE StaticTMSInterface
const customerSupportedTMSes: TMS[] = [
  TMS.McleodEnterprise,
  TMS.Turvo,
  TMS.Aljex,
  TMS.ThreeG,
];

export const isCustomerSupportedTMS = (
  tmsIntegrations: Maybe<IntegrationCore[]>
) =>
  tmsIntegrations?.some((tms) =>
    customerSupportedTMSes.includes(tms.name as TMS)
  );

/**
 * Centralised hook that encapsulates all customer-list behaviour shared
 * between QuickQuoteForm and BatchQuoteForm.
 */
export function useCustomers(
  tmsIntegrations: Maybe<IntegrationCore[]>,
  options: UseCustomersOptions = {}
): UseCustomersResult {
  const { toast, suggestedCustomerId } = options;
  const [customers, setCustomers] = useState<Maybe<TMSCustomer[]>>(null);
  const [initialCustomers, setInitialCustomers] =
    useState<Maybe<TMSCustomer[]>>(null);
  const [tmsTenant, setTMSTenant] = useState<Maybe<string>>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const isSupportedTMS = isCustomerSupportedTMS(tmsIntegrations);

  const fetchCustomers = useCallback(
    async (forceRefresh = false) => {
      if (!tmsIntegrations?.length || !isSupportedTMS) return;
      setIsLoading(true);

      const integrationId = tmsIntegrations?.[0]?.id;
      if (!integrationId) {
        setIsLoading(false);
        return;
      }

      const res = await getCustomers(integrationId, forceRefresh);
      if (res.isOk()) {
        setCustomers(res.value.customerList);
        setInitialCustomers(res.value.customerList);
        setTMSTenant(res.value.tmsTenant);
      }
      setIsLoading(false);
    },
    [tmsIntegrations, isSupportedTMS]
  );

  // initial fetch
  useEffect(() => {
    fetchCustomers();
  }, [fetchCustomers]);

  const refreshCustomers = useCallback(async () => {
    if (!tmsIntegrations?.length || !isSupportedTMS) return;
    setIsLoading(true);
    const integrationId = tmsIntegrations?.[0]?.id;
    if (!integrationId) {
      setIsLoading(false);
      return;
    }

    const res = await getCustomers(integrationId, true);
    if (res.isOk()) {
      setInitialCustomers(res.value.customerList);
      setCustomers(res.value.customerList);
      setTMSTenant(res.value.tmsTenant);

      if (toast) {
        toast({
          description: 'Successfully refreshed customer list.',
          variant: 'success',
        });
      }
    } else {
      if (toast) {
        toast({
          description: 'Error while refreshing customer list.',
          variant: 'destructive',
        });
      }
    }
    setIsLoading(false);
  }, [tmsIntegrations, isSupportedTMS, toast]);

  const resetCustomerSearch = useCallback(() => {
    // Always preserve any customers that were fetched via suggestions
    if (suggestedCustomerId && customers && initialCustomers) {
      const suggestedCustomer = customers.find(
        (c) => c.externalTMSID === suggestedCustomerId
      );
      if (suggestedCustomer) {
        // Always preserve the suggested customer, regardless of whether it's in initialCustomers
        const customersWithoutSuggested = initialCustomers.filter(
          (c) => c.externalTMSID !== suggestedCustomerId
        );
        setCustomers([...customersWithoutSuggested, suggestedCustomer]);
        return;
      }
    }

    setCustomers(initialCustomers);
  }, [suggestedCustomerId, customers, initialCustomers]);

  const customerSearch = async (
    field: GenericCompanySearchableFields,
    value: string
  ): Promise<ValueType[]> => {
    const integrationId = tmsIntegrations?.[0]?.id ?? 0;
    return customerSearchHandler({
      tmsID: integrationId,
      customers,
      setCustomers,
      field,
      value,
    });
  };

  return {
    customers,
    isLoading,
    tmsTenant,
    refreshCustomers,
    resetCustomerSearch,
    customerSearch,
  };
}
