import { includeIgnoreFile } from '@eslint/compat';
import eslint from '@eslint/js';
import tseslintParser from '@typescript-eslint/parser';
import prettierConfig from 'eslint-config-prettier';
import react from 'eslint-plugin-react';
import path from 'node:path';
import { fileURLToPath } from 'node:url';
import tseslint from 'typescript-eslint';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const gitignorePath = path.resolve(__dirname, '.gitignore');

const customRules = {
  'no-grid-without-explicit-margin-and-width': {
    meta: {
      type: 'warn',
      docs: {
        description:
          'Enforce explicit margin and width classes with grid layouts to override TMS website defaults',
        category: 'Best Practices',
        recommended: false,
      },
      schema: [], // no options
    },
    create(context) {
      return {
        JSXAttribute(node) {
          if (
            node.name.name === 'className' &&
            typeof node.value.value === 'string'
          ) {
            const classNameValue = node.value.value;
            const hasGrid = /\bgrid\b/.test(classNameValue);
            const hasGridColsOrRows = /\b(grid-cols-\d+|grid-rows-\d+)\b/.test(
              classNameValue
            );
            const hasMx = /\bm[xy]?-\d+\b/.test(classNameValue);
            const hasWidth = /\bw-(?:full|\d+\/\d+|\d+)\b/.test(classNameValue);

            if (hasGrid && hasGridColsOrRows && (!hasMx || !hasWidth)) {
              context.report({
                node,
                message:
                  'Using `grid` with `grid-cols-*` or `grid-rows-*` without explicit margin and/or width class. Consider adding `mx-0 w-full` or appropriate classes.',
              });
            }
          }
        },
      };
    },
  },
  'no-alexandria-capture-exception-in-modules': {
    meta: {
      type: 'error',
      docs: {
        description:
          'Prevent use of Alexandria captureException utility in background modules folder. Use Sentry.captureException directly instead.',
        category: 'Best Practices',
        recommended: false,
      },
      schema: [],
    },
    create(context) {
      const filename = context.getFilename();
      const isInBackgroundFolder = filename.includes('/pages/background/');

      if (!isInBackgroundFolder) {
        return {};
      }

      return {
        ImportDeclaration(node) {
          if (node.source.value && typeof node.source.value === 'string') {
            const importPath = node.source.value;

            // Only flag Alexandria utility imports, not Sentry imports
            if (
              (importPath.includes('utils/captureException') ||
                importPath.includes('alexandria/utils/captureException')) &&
              !importPath.includes('@sentry')
            ) {
              context.report({
                node,
                message:
                  'Alexandria captureException utility should not be used in background modules. Use Sentry.captureException directly instead.',
              });
            }
          }
        },
        CallExpression(node) {
          // Check for calls to captureException that are not from Sentry
          if (
            node.callee.type === 'Identifier' &&
            node.callee.name === 'captureException'
          ) {
            // Only report if this appears to be the Alexandria version
            // (the Sentry version would be Sentry.captureException or imported differently)
            const sourceCode = context.getSourceCode();
            const imports = sourceCode.ast.body.filter(
              (n) => n.type === 'ImportDeclaration'
            );

            // Check if captureException was imported from a non-Sentry source
            const hasAlexandriaImport = imports.some(
              (imp) =>
                imp.source.value &&
                (imp.source.value.includes('utils/captureException') ||
                  imp.source.value.includes(
                    'alexandria/utils/captureException'
                  )) &&
                !imp.source.value.includes('@sentry') &&
                imp.specifiers.some(
                  (spec) =>
                    spec.type === 'ImportDefaultSpecifier' &&
                    spec.local.name === 'captureException'
                )
            );

            if (hasAlexandriaImport) {
              context.report({
                node,
                message:
                  'Alexandria captureException utility should not be used in background modules. Use Sentry.captureException directly instead.',
              });
            }
          }
        },
      };
    },
  },
};

export default [
  includeIgnoreFile(gitignorePath),
  eslint.configs.recommended,
  ...tseslint.configs.recommended,
  {
    files: ['**/*.{js,mjs,cjs,jsx,mjsx,ts,tsx,mtsx}'],
    plugins: {
      react,
      custom: { rules: customRules },
    },
    settings: {
      react: {
        version: 'detect',
      },
    },
    rules: {
      'react/button-has-type': 'off',
      'react/react-in-jsx-scope': 'off',
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/no-unused-vars': [
        'warn',
        { argsIgnorePattern: '^_', varsIgnorePattern: '^_' },
      ],
      '@typescript-eslint/no-unused-expressions': [
        'error',
        {
          allowShortCircuit: true,
          allowTernary: true,
          allowTaggedTemplates: true,
        },
      ],
      '@typescript-eslint/no-empty-object-type': [
        'error',
        { allowInterfaces: 'with-single-extends' },
      ],
      'custom/no-grid-without-explicit-margin-and-width': 'warn',
      'custom/no-alexandria-capture-exception-in-modules': 'error',
    },
    languageOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module',
      parser: tseslintParser,
      parserOptions: {
        ecmaFeatures: {
          jsx: true,
        },
      },
      globals: {
        chrome: 'readonly',
      },
    },
  },
  {
    ignores: ['watch.js', '**/dist/**', '**/.yarn/**', '**/tests/**'],
  },
  prettierConfig,
];
